import React, { useState } from 'react';
import { useAI } from '../hooks/useAI';
import { 
  FileText, 
  Clock, 
  User, 
  Trash2, 
  Download, 
  Filter,
  Search,
  RefreshCw
} from 'lucide-react';
import AiConfidenceBadge from './AiConfidenceBadge';

const AiLogsPanel: React.FC = () => {
  const { history, clearHistory } = useAI();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');

  const filteredHistory = history.filter(entry => {
    const matchesSearch = entry.content.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterType === 'all' || entry.id.includes(filterType);
    return matchesSearch && matchesFilter;
  });

  const exportLogs = () => {
    const logsData = {
      exportDate: new Date().toISOString(),
      totalEntries: history.length,
      entries: history.map(entry => ({
        id: entry.id,
        timestamp: entry.timestamp,
        confidence: entry.confidence,
        content: entry.content,
        suggestions: entry.suggestions
      }))
    };

    const blob = new Blob([JSON.stringify(logsData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getTypeIcon = (id: string) => {
    if (id.includes('explain')) return '💡';
    if (id.includes('generate')) return '⚡';
    if (id.includes('refactor')) return '🔧';
    if (id.includes('debug')) return '🐛';
    if (id.includes('suggest')) return '💭';
    return '🤖';
  };

  const getTypeLabel = (id: string) => {
    if (id.includes('explain')) return 'Explain';
    if (id.includes('generate')) return 'Generate';
    if (id.includes('refactor')) return 'Refactor';
    if (id.includes('debug')) return 'Debug';
    if (id.includes('suggest')) return 'Suggest';
    return 'AI Request';
  };

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <FileText className="w-5 h-5 text-blue-400" />
            <h3 className="text-lg font-semibold text-white">AI Activity Logs</h3>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={exportLogs}
              disabled={history.length === 0}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-3 py-2 rounded text-sm transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
            <button
              onClick={clearHistory}
              disabled={history.length === 0}
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-3 py-2 rounded text-sm transition-colors"
            >
              <Trash2 className="w-4 h-4" />
              <span>Clear</span>
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search AI interactions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400"
            />
          </div>
          
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
          >
            <option value="all">All Types</option>
            <option value="explain">Explanations</option>
            <option value="generate">Code Generation</option>
            <option value="refactor">Refactoring</option>
            <option value="debug">Debugging</option>
            <option value="suggest">Suggestions</option>
          </select>
        </div>
      </div>

      {/* Stats */}
      <div className="bg-gray-800/50 border-b border-gray-700 p-4">
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{history.length}</div>
            <div className="text-xs text-gray-400">Total Requests</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">
              {history.filter(h => h.confidence >= 0.8).length}
            </div>
            <div className="text-xs text-gray-400">High Confidence</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {history.filter(h => h.id.includes('generate')).length}
            </div>
            <div className="text-xs text-gray-400">Code Generated</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">
              {Math.round(history.reduce((sum, h) => sum + h.confidence, 0) / history.length * 100) || 0}%
            </div>
            <div className="text-xs text-gray-400">Avg Confidence</div>
          </div>
        </div>
      </div>

      {/* Log Entries */}
      <div className="flex-1 overflow-y-auto p-4">
        {filteredHistory.length === 0 ? (
          <div className="text-center py-12 text-gray-400">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>No AI activity logs</p>
            <p className="text-sm">AI interactions will appear here</p>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredHistory.slice().reverse().map((entry, index) => (
              <div key={entry.id} className="bg-gray-800/50 rounded-lg border border-gray-700 p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-lg">{getTypeIcon(entry.id)}</span>
                    <div>
                      <div className="text-white font-medium">{getTypeLabel(entry.id)}</div>
                      <div className="flex items-center space-x-2 text-sm text-gray-400">
                        <Clock className="w-3 h-3" />
                        <span>{entry.timestamp.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  
                  <AiConfidenceBadge confidence={entry.confidence} size="sm" />
                </div>
                
                <div className="text-gray-300 text-sm mb-3 line-clamp-3">
                  {entry.content}
                </div>
                
                {entry.suggestions && entry.suggestions.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {entry.suggestions.slice(0, 3).map((suggestion, idx) => (
                      <span
                        key={idx}
                        className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded"
                      >
                        {suggestion}
                      </span>
                    ))}
                    {entry.suggestions.length > 3 && (
                      <span className="text-xs text-gray-400">
                        +{entry.suggestions.length - 3} more
                      </span>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AiLogsPanel;