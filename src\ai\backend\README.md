# LUREON AI Backend

Enterprise-grade AI backend service for the LUREON Industrial Automation IDE.

## Features

### 🧠 AI Model Integration
- **Multi-Provider Support**: OpenAI, Claude, Ollama
- **Streaming Responses**: Real-time token streaming
- **Model Routing**: Automatic model selection based on request type
- **Response Validation**: Safety and compliance checking

### 🔒 Security & Compliance
- **Rate Limiting**: Tiered limits by user plan
- **Digital Signatures**: Cryptographic signing of AI outputs
- **Audit Logging**: Complete request/response tracking
- **Usage Metering**: Token and cost tracking

### ⚡ Performance & Scaling
- **Job Queue**: Background processing with retry logic
- **Prompt Caching**: Redis-based response caching
- **Load Balancing**: Nginx reverse proxy
- **Health Monitoring**: Comprehensive health checks

### 📊 Analytics & Feedback
- **Feedback Collection**: User rating and comment system
- **Usage Analytics**: Detailed usage metrics and reporting
- **Compliance Reporting**: Automated compliance report generation

## Quick Start

### Development

```bash
# Install dependencies
npm install

# Start AI backend server
npm run ai-server

# Start full stack (frontend + backend)
npm run dev:full
```

### Production Deployment

```bash
# Using Docker Compose
docker-compose up -d

# Or build and run manually
docker build -t lureon-ai-backend .
docker run -p 3001:3001 lureon-ai-backend
```

## API Endpoints

### AI Requests

```bash
# Standard AI request
POST /api/ai/request
{
  "type": "generate",
  "prompt": "Create motor control logic",
  "context": { "language": "ladder" },
  "model": "gpt-4"
}

# Streaming request
POST /api/ai/stream
{
  "prompt": "Explain this ladder logic",
  "model": "gpt-3.5-turbo"
}
```

### Job Queue

```bash
# Queue a job
POST /api/ai/jobs
{
  "request": { "type": "generate", "prompt": "..." },
  "priority": "high"
}

# Check job status
GET /api/ai/jobs/{jobId}
```

### Feedback

```bash
# Submit feedback
POST /api/ai/feedback
{
  "suggestionId": "ai_123",
  "rating": "thumbs_up",
  "category": "accuracy",
  "comment": "Great suggestion!"
}
```

### Usage & Analytics

```bash
# Get usage metrics
GET /api/ai/usage?period=monthly

# Get audit report
GET /api/ai/audit?startDate=2024-01-01&endDate=2024-01-31

# Cache statistics
GET /api/ai/cache/stats
```

## Configuration

### Environment Variables

```env
# Server
NODE_ENV=production
PORT=3001
FRONTEND_URL=http://localhost:5173

# AI Providers
OPENAI_API_KEY=your_openai_key
CLAUDE_API_KEY=your_claude_key

# Database
REDIS_URL=redis://localhost:6379
POSTGRES_URL=postgresql://user:pass@localhost:5432/lureon_ai

# Security
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key
```

### Rate Limits

| Plan | Requests/15min | Tokens/Day | Models |
|------|----------------|------------|--------|
| Free | 10 | 10,000 | GPT-3.5 |
| Pro | 100 | 100,000 | GPT-3.5, GPT-4 |
| Enterprise | Unlimited | Unlimited | All |

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Nginx         │    │   AI Backend    │
│   (React)       │◄──►│   (Proxy)       │◄──►│   (Express)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │   Redis         │◄────────────┤
                       │   (Cache)       │             │
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   PostgreSQL    │◄────────────┘
                       │   (Audit/Data)  │
                       └─────────────────┘
```

## Security Features

### Authentication & Authorization
- JWT token validation
- Role-based access control
- API key authentication for enterprise

### Data Protection
- Request/response encryption
- Digital signature verification
- Audit trail integrity

### Rate Limiting
- IP-based rate limiting
- User-based quota enforcement
- Burst protection

## Monitoring & Observability

### Health Checks
```bash
# Service health
GET /health

# Detailed diagnostics
GET /api/ai/diagnostics
```

### Metrics
- Request latency and throughput
- Model performance metrics
- Cache hit rates
- Error rates and types

### Logging
- Structured JSON logging
- Request/response correlation
- Security event logging
- Performance metrics

## Compliance & Audit

### Audit Trail
- Complete request/response logging
- User action tracking
- Compliance report generation
- Data retention policies

### Digital Signatures
- Cryptographic signing of AI outputs
- Integrity verification
- Non-repudiation guarantees

### Data Retention
- 7-year audit log retention
- Automated data archival
- Secure data deletion

## Development

### Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage

# Load testing
npm run test:load
```

### Code Quality

```bash
# Linting
npm run lint

# Type checking
npm run type-check

# Security audit
npm audit
```

## Deployment

### Docker

```dockerfile
# Multi-stage build
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY src ./src
EXPOSE 3001
CMD ["node", "src/ai/backend/server.js"]
```

### Kubernetes

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lureon-ai-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: lureon-ai-backend
  template:
    metadata:
      labels:
        app: lureon-ai-backend
    spec:
      containers:
      - name: ai-backend
        image: lureon/ai-backend:latest
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

## Troubleshooting

### Common Issues

**High Latency**
- Check model response times
- Verify cache hit rates
- Monitor database performance

**Rate Limit Errors**
- Review user plan limits
- Check for abuse patterns
- Adjust rate limit windows

**Memory Issues**
- Monitor cache size
- Check for memory leaks
- Optimize job queue size

### Debug Mode

```bash
# Enable debug logging
DEBUG=lureon:* npm run ai-server

# Verbose logging
LOG_LEVEL=debug npm run ai-server
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

Copyright © 2024 LUREON Industrial Automation IDE
Licensed under the MIT License