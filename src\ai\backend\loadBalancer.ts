export class LoadBalancer {
    private instances: Array<{ id: string; url: string; healthy: boolean; load: number }> = [];

    async addInstance(instance: { id: string; url: string }) {
        this.instances.push({ ...instance, healthy: true, load: 0 });
    }

    async getOptimalInstance(): Promise<string> {
        const healthyInstances = this.instances.filter(i => i.healthy);
        if (healthyInstances.length === 0) {
            throw new Error('No healthy instances available');
        }

        // Return instance with lowest load
        return healthyInstances.reduce((min, current) =>
            current.load < min.load ? current : min
        ).url;
    }

    async updateInstanceLoad(instanceId: string, load: number) {
        const instance = this.instances.find(i => i.id === instanceId);
        if (instance) {
            instance.load = load;
        }
    }
}