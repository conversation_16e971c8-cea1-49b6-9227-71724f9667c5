// Model Context Protocol (MCP) Types

export type ContextItemType = 'tag' | 'program' | 'rung' | 'safety' | 'doc' | 'version' | 'standard';

export interface ContextItem {
  id: string;
  type: ContextItemType;
  source: string; // 'auto' | 'user' | 'system'
  value: string;
  timestamp: string;
  metadata?: Record<string, any>;
  expires_at?: string;
  version?: number;
}

export interface ContextRelationship {
  id: string;
  source_id: string;
  target_id: string;
  relationship_type: 'depends_on' | 'related_to' | 'conflicts_with' | 'enhances';
  strength: number; // 0.0 to 1.0
  metadata?: Record<string, any>;
}

export interface ContextUsage {
  id: string;
  context_id: string;
  request_id: string;
  prompt_type: string;
  timestamp: string;
  confidence_contribution: number;
  user_id: string;
  metadata?: Record<string, any>;
}

export interface PromptEnhancementRequest {
  prompt: string;
  contextTypes?: ContextItemType[];
  maxTokens?: number;
}

export interface PromptEnhancementResponse {
  enhancedPrompt: string;
  contextItems: string[];
  confidence: number;
  contextFingerprint: string;
}

export interface ConfidenceRequest {
  contextItems: string[];
  promptType: string;
}

export interface ConfidenceResponse {
  confidence: number;
  breakdown: {
    completeness: number;
    freshness: number;
    relevance: number;
    quality: number;
  };
  suggestions: string[];
}

export interface ContextCollectionRequest {
  type: ContextItemType;
  source: string;
  value: string;
  user_id: string;
  project_id?: string;
  metadata?: Record<string, any>;
}

export interface MCPConfig {
  enabled: boolean;
  contextTypes: ContextItemType[];
  confidenceThreshold: number;
  maxContextItems: number;
  contextTTL: number; // in seconds
  autoCollect: boolean;
}