import { ContextItem, ContextItemType, ContextRelationship } from '../../types/mcp';
import { db } from '../backend/db';

export class ContextStorageService {
  async getContextItem(id: string): Promise<ContextItem | null> {
    try {
      const result = await db.query(
        'SELECT * FROM mcp_context_items WHERE id = $1',
        [id]
      );
      
      if (result.rows.length === 0) {
        return null;
      }
      
      const item = result.rows[0];
      return {
        ...item,
        metadata: item.metadata ? JSON.parse(item.metadata) : {}
      };
    } catch (error) {
      console.error(`Error getting context item ${id}:`, error);
      return null;
    }
  }
  
  async getContextItems(
    filters: {
      types?: ContextItemType[];
      userId?: string;
      projectId?: string;
      source?: string;
      since?: Date;
    },
    limit: number = 50,
    offset: number = 0
  ): Promise<ContextItem[]> {
    try {
      let query = 'SELECT * FROM mcp_context_items WHERE 1=1';
      const params: any[] = [];
      let paramIndex = 1;
      
      if (filters.types && filters.types.length > 0) {
        const typePlaceholders = filters.types.map((_, i) => `$${paramIndex++}`).join(', ');
        query += ` AND type IN (${typePlaceholders})`;
        params.push(...filters.types);
      }
      
      if (filters.userId) {
        query += ` AND user_id = $${paramIndex++}`;
        params.push(filters.userId);
      }
      
      if (filters.projectId) {
        query += ` AND project_id = $${paramIndex++}`;
        params.push(filters.projectId);
      }
      
      if (filters.source) {
        query += ` AND source = $${paramIndex++}`;
        params.push(filters.source);
      }
      
      if (filters.since) {
        query += ` AND timestamp >= $${paramIndex++}`;
        params.push(filters.since);
      }
      
      query += ` ORDER BY timestamp DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
      params.push(limit, offset);
      
      const result = await db.query(query, params);
      
      return result.rows.map((row: any) => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : {}
      }));
    } catch (error) {
      console.error('Error getting context items:', error);
      return [];
    }
  }
  
  async createContextItem(item: Partial<ContextItem>): Promise<ContextItem | null> {
    try {
      const result = await db.query(
        `INSERT INTO mcp_context_items 
         (type, source, value, user_id, project_id, metadata, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())
         RETURNING *`,
        [
          item.type,
          item.source,
          item.value,
          item.user_id,
          item.project_id,
          JSON.stringify(item.metadata || {})
        ]
      );
      
      const newItem = result.rows[0];
      return {
        ...newItem,
        metadata: newItem.metadata ? JSON.parse(newItem.metadata) : {}
      };
    } catch (error) {
      console.error('Error creating context item:', error);
      return null;
    }
  }
  
  async updateContextItem(id: string, updates: Partial<ContextItem>): Promise<ContextItem | null> {
    try {
      const fields: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;
      
      if (updates.value !== undefined) {
        fields.push(`value = $${paramIndex++}`);
        values.push(updates.value);
      }
      
      if (updates.metadata !== undefined) {
        fields.push(`metadata = $${paramIndex++}`);
        values.push(JSON.stringify(updates.metadata));
      }
      
      if (updates.expires_at !== undefined) {
        fields.push(`expires_at = $${paramIndex++}`);
        values.push(updates.expires_at);
      }
      
      if (updates.version !== undefined) {
        fields.push(`version = $${paramIndex++}`);
        values.push(updates.version);
      }
      
      if (fields.length === 0) {
        return await this.getContextItem(id);
      }
      
      values.push(id);
      
      const result = await db.query(
        `UPDATE mcp_context_items 
         SET ${fields.join(', ')}
         WHERE id = $${paramIndex}
         RETURNING *`,
        values
      );
      
      if (result.rows.length === 0) {
        return null;
      }
      
      const updatedItem = result.rows[0];
      return {
        ...updatedItem,
        metadata: updatedItem.metadata ? JSON.parse(updatedItem.metadata) : {}
      };
    } catch (error) {
      console.error(`Error updating context item ${id}:`, error);
      return null;
    }
  }
  
  async deleteContextItem(id: string): Promise<boolean> {
    try {
      const result = await db.query(
        'DELETE FROM mcp_context_items WHERE id = $1 RETURNING id',
        [id]
      );
      
      return result.rowCount > 0;
    } catch (error) {
      console.error(`Error deleting context item ${id}:`, error);
      return false;
    }
  }
  
  async createContextRelationship(relationship: Partial<ContextRelationship>): Promise<ContextRelationship | null> {
    try {
      const result = await db.query(
        `INSERT INTO mcp_context_relationships 
         (source_id, target_id, relationship_type, strength, metadata)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [
          relationship.source_id,
          relationship.target_id,
          relationship.relationship_type,
          relationship.strength || 1.0,
          JSON.stringify(relationship.metadata || {})
        ]
      );
      
      const newRelationship = result.rows[0];
      return {
        ...newRelationship,
        metadata: newRelationship.metadata ? JSON.parse(newRelationship.metadata) : {}
      };
    } catch (error) {
      console.error('Error creating context relationship:', error);
      return null;
    }
  }
  
  async getRelatedContextItems(contextId: string, relationshipType?: string): Promise<ContextItem[]> {
    try {
      let query = `
        SELECT ci.* FROM mcp_context_items ci
        JOIN mcp_context_relationships cr ON ci.id = cr.target_id
        WHERE cr.source_id = $1
      `;
      const params: any[] = [contextId];
      
      if (relationshipType) {
        query += ` AND cr.relationship_type = $2`;
        params.push(relationshipType);
      }
      
      const result = await db.query(query, params);
      
      return result.rows.map((row: any) => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : {}
      }));
    } catch (error) {
      console.error(`Error getting related context items for ${contextId}:`, error);
      return [];
    }
  }
  
  async cleanupExpiredContext(): Promise<number> {
    try {
      const result = await db.query(
        'DELETE FROM mcp_context_items WHERE expires_at < NOW() RETURNING id'
      );
      
      return result.rowCount;
    } catch (error) {
      console.error('Error cleaning up expired context:', error);
      return 0;
    }
  }
}

export const contextStorageService = new ContextStorageService();