import jwt from 'jsonwebtoken';
import { RequestHand<PERSON> } from 'express';

interface AuthUser {
  id: string;
  plan: string;
  organizationId: string;
  email?: string;
}

export const authenticateUser: RequestHandler = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // For development, use mock authentication
    if (process.env.NODE_ENV === 'development' && token === 'dev-token') {
      (req as any).user = {
        id: 'dev-user-1',
        plan: 'pro',
        organizationId: 'dev-org-1',
        email: '<EMAIL>'
      };
      return next();
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    (req as any).user = {
      id: decoded.userId,
      plan: decoded.plan || 'basic',
      organizationId: decoded.organizationId,
      email: decoded.email
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ error: 'Invalid or expired token' });
  }
};