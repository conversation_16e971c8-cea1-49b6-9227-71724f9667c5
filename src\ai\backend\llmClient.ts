import { AIRequest, AIResponse } from '../hooks/useAI';

export interface LLMProvider {
  name: 'openai' | 'claude' | 'ollama';
  endpoint: string;
  apiKey: string;
  models: string[];
}

export interface LLMOptions {
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  model?: string;
}

export interface ValidationResult {
  isValid: boolean;
  safetyScore: number;
  complianceIssues: string[];
  suggestions: string[];
}

class LLMClient {
  private providers: Map<string, LLMProvider> = new Map();
  private defaultProvider = 'ollama'; // Use Ollama as default for development
  private ollamaUrl = process.env.OLLAMA_HOST || 'http://localhost:11434';

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders() {
    // Local Ollama Provider (primary for development)
    this.providers.set('ollama', {
      name: 'ollama',
      endpoint: `${this.ollamaUrl}/api/generate`,
      apiKey: '', // Ollama doesn't need API key
      models: ['llama3', 'codellama', 'mistral', 'llama2']
    });

    // OpenAI Provider
    this.providers.set('openai', {
      name: 'openai',
      endpoint: 'https://api.openai.com/v1/chat/completions',
      apiKey: process.env.OPENAI_API_KEY || '',
      models: ['gpt-4', 'gpt-3.5-turbo']
    });

    // Claude Provider
    this.providers.set('claude', {
      name: 'claude',
      endpoint: 'https://api.anthropic.com/v1/messages',
      apiKey: process.env.CLAUDE_API_KEY || '',
      models: ['claude-3-opus', 'claude-3-sonnet']
    });
  }

  // Main method called by API routes
  async generateResponse(request: {
    prompt: string;
    context?: any;
    model?: string;
    options?: any;
  }): Promise<AIResponse> {
    const model = request.model || 'llama3';
    const provider = this.getProviderForModel(model);

    try {
      if (provider.name === 'ollama') {
        return await this.callOllama(request.prompt, model, request.options);
      } else {
        // Fallback for other providers (OpenAI, Claude)
        return await this.callExternalProvider(provider, request.prompt, model, request.options);
      }
    } catch (error) {
      console.error('LLM request failed:', error);

      // Return mock response as fallback
      return {
        id: `fallback_${Date.now()}`,
        content: `PLC Programming Response to: "${request.prompt.substring(0, 50)}..."\n\nThis is a fallback response because the AI service is unavailable. In production, this would be a helpful response about ladder logic, structured text, or function blocks.`,
        confidence: 0.7,
        cached: false,
        timestamp: new Date(),
        model: model,
        usage: {
          inputTokens: request.prompt.length,
          outputTokens: 150,
          totalTokens: request.prompt.length + 150
        }
      };
    }
  }

  private async callOllama(prompt: string, model: string, options: any = {}): Promise<AIResponse> {
    try {
      const response = await fetch(`${this.ollamaUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: model,
          prompt: this.enhancePromptForPLC(prompt),
          stream: false,
          options: {
            temperature: options.temperature || 0.7,
            top_p: options.top_p || 0.9,
            num_predict: options.maxTokens || 1000
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Ollama request failed: ${response.statusText}`);
      } const data = await response.json() as any;

      return {
        id: `ollama_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: data.response || 'No response from Ollama',
        confidence: 0.85,
        cached: false,
        timestamp: new Date(),
        model: model,
        usage: {
          inputTokens: prompt.length,
          outputTokens: data.response?.length || 0,
          totalTokens: prompt.length + (data.response?.length || 0)
        }
      };
    } catch (error) {
      console.error('Ollama request failed:', error);
      throw error;
    }
  }

  private async callExternalProvider(provider: LLMProvider, prompt: string, model: string, options: any = {}): Promise<AIResponse> {
    // Placeholder for OpenAI/Claude integration
    // For now, return mock response
    return {
      id: `external_${Date.now()}`,
      content: `External provider response for: ${prompt.substring(0, 50)}...`,
      confidence: 0.8,
      cached: false,
      timestamp: new Date(),
      model: model,
      usage: {
        inputTokens: prompt.length,
        outputTokens: 100,
        totalTokens: prompt.length + 100
      }
    };
  }

  private enhancePromptForPLC(prompt: string): string {
    // Add PLC-specific context to improve responses
    const plcContext = `You are an expert in industrial automation and PLC programming. Provide clear, safe, and standards-compliant responses for PLC programming questions. Focus on ladder logic, structured text, and function block diagrams according to IEC 61131-3 standards.

Human question: ${prompt}

Please provide a helpful response:`;

    return plcContext;
  }
  async getAvailableModels(): Promise<any[]> {
    try {
      // Try to get Ollama models first
      const response = await fetch(`${this.ollamaUrl}/api/tags`);
      if (response.ok) {
        const data = await response.json() as any;
        const ollamaModels = data.models?.map((m: any) => ({
          id: m.name,
          name: m.name,
          provider: 'ollama',
          context_length: 4096,
          capabilities: ['text-generation', 'code-completion', 'plc-assistance']
        })) || [];

        return [
          ...ollamaModels,
          {
            id: 'gpt-3.5-turbo',
            name: 'GPT-3.5 Turbo',
            provider: 'openai',
            context_length: 4096,
            capabilities: ['text-generation', 'code-completion', 'plc-assistance']
          },
          {
            id: 'claude-3-sonnet',
            name: 'Claude 3 Sonnet',
            provider: 'anthropic',
            context_length: 200000,
            capabilities: ['text-generation', 'code-completion', 'plc-assistance', 'safety-analysis']
          }
        ];
      }
    } catch (error) {
      console.error('Failed to fetch Ollama models:', error);
    }

    // Fallback models
    return [
      {
        id: 'llama3',
        name: 'Llama 3',
        provider: 'ollama',
        context_length: 8192,
        capabilities: ['text-generation', 'code-completion', 'plc-assistance']
      },
      {
        id: 'codellama',
        name: 'Code Llama',
        provider: 'ollama',
        context_length: 4096,
        capabilities: ['code-completion', 'plc-assistance']
      }
    ];
  }

  async generateStreamingResponse(request: {
    prompt: string;
    context?: any;
    model?: string;
  }): Promise<AsyncGenerator<any, void, unknown>> {
    const model = request.model || 'llama3';

    return this.streamOllama(request.prompt, model);
  }

  private async* streamOllama(prompt: string, model: string): AsyncGenerator<any, void, unknown> {
    try {
      const response = await fetch(`${this.ollamaUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: model,
          prompt: this.enhancePromptForPLC(prompt),
          stream: true
        })
      });

      if (!response.ok) {
        throw new Error(`Ollama streaming failed: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              if (data.response) {
                yield { content: data.response };
              }
              if (data.done) {
                return;
              }
            } catch (e) {
              // Skip invalid JSON lines
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
      yield { content: 'Streaming error occurred' };
    }
  }

  private getProviderForModel(model: string): LLMProvider {
    // Check if model exists in any provider
    for (const provider of this.providers.values()) {
      if (provider.models.includes(model)) {
        return provider;
      }
    }

    // Default to Ollama for unknown models
    return this.providers.get('ollama')!;
  }

  async healthCheck(): Promise<{ status: string; responseTime: number }> {
    try {
      const start = Date.now();
      const response = await fetch(`${this.ollamaUrl}/api/tags`);
      const responseTime = Date.now() - start;

      return {
        status: response.ok ? 'healthy' : 'unhealthy',
        responseTime
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: -1
      };
    }
  }
}

export const llmClient = new LLMClient();