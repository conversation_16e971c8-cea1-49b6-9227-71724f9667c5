import { AIRequest, AIResponse } from '../hooks/useAI';

export interface LLMProvider {
  name: 'openai' | 'claude' | 'ollama';
  endpoint: string;
  apiKey: string;
  models: string[];
}

export interface LLMOptions {
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  model?: string;
}

export interface ValidationResult {
  isValid: boolean;
  safetyScore: number;
  complianceIssues: string[];
  suggestions: string[];
}

class LLMClient {
  private providers: Map<string, LLMProvider> = new Map();
  private defaultProvider = 'openai';

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders() {
    // OpenAI Provider
    this.providers.set('openai', {
      name: 'openai',
      endpoint: 'https://api.openai.com/v1/chat/completions',
      apiKey: process.env.OPENAI_API_KEY || '',
      models: ['gpt-4', 'gpt-3.5-turbo']
    });

    // <PERSON> Provider
    this.providers.set('claude', {
      name: 'claude',
      endpoint: 'https://api.anthropic.com/v1/messages',
      apiKey: process.env.CLAUDE_API_KEY || '',
      models: ['claude-3-opus', 'claude-3-sonnet']
    });

    // Local Ollama Provider
    this.providers.set('ollama', {
      name: 'ollama',
      endpoint: 'http://localhost:11434/api/generate',
      apiKey: '',
      models: ['llama3', 'codellama']
    });
  }

  async invoke(model: string, prompt: string, options: LLMOptions = {}): Promise<AIResponse> {
    const provider = this.getProviderForModel(model);
    const startTime = Date.now();

    try {
      const response = await this.makeRequest(provider, model, prompt, options);
      const timeTaken = (Date.now() - startTime) / 1000;

      // Validate response for safety and compliance
      const validation = await this.validateResponse(response.content, { prompt, model });

      return {
        id: `llm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: response.content,
        confidence: this.calculateConfidence(response, validation),
        suggestions: validation.suggestions,
        timestamp: new Date(),
        timeTaken,
        validation
      };
    } catch (error) {
      console.error('LLM request failed:', error);
      throw new Error(`LLM request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async *stream(model: string, prompt: string, options: LLMOptions = {}): AsyncGenerator<string> {
    const provider = this.getProviderForModel(model);
    
    try {
      const response = await fetch(provider.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${provider.apiKey}`,
        },
        body: JSON.stringify({
          model,
          messages: [{ role: 'user', content: prompt }],
          stream: true,
          ...options
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') return;
            
            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) yield content;
            } catch (e) {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming failed:', error);
      throw error;
    }
  }

  async validateResponse(response: string, context: any): Promise<ValidationResult> {
    // Safety validation rules for PLC code
    const safetyKeywords = ['emergency_stop', 'safety', 'interlock', 'fail_safe'];
    const dangerousPatterns = ['bypass', 'override', 'disable_safety'];
    
    let safetyScore = 0.8; // Base score
    const complianceIssues: string[] = [];
    const suggestions: string[] = [];

    // Check for safety keywords
    const hasSafetyKeywords = safetyKeywords.some(keyword => 
      response.toLowerCase().includes(keyword)
    );
    if (hasSafetyKeywords) safetyScore += 0.1;

    // Check for dangerous patterns
    const hasDangerousPatterns = dangerousPatterns.some(pattern => 
      response.toLowerCase().includes(pattern)
    );
    if (hasDangerousPatterns) {
      safetyScore -= 0.3;
      complianceIssues.push('Contains potentially unsafe patterns');
    }

    // IEC 61131-3 compliance checks
    if (context.prompt?.includes('ladder') || context.prompt?.includes('st')) {
      if (!response.includes('END_') && response.includes('FUNCTION_BLOCK')) {
        complianceIssues.push('Missing END_FUNCTION_BLOCK statement');
        safetyScore -= 0.1;
      }
    }

    // Generate suggestions
    if (safetyScore < 0.7) {
      suggestions.push('Consider adding safety interlocks');
      suggestions.push('Review emergency stop implementation');
    }

    return {
      isValid: safetyScore >= 0.6,
      safetyScore,
      complianceIssues,
      suggestions
    };
  }

  private getProviderForModel(model: string): LLMProvider {
    for (const provider of this.providers.values()) {
      if (provider.models.includes(model)) {
        return provider;
      }
    }
    return this.providers.get(this.defaultProvider)!;
  }

  private async makeRequest(provider: LLMProvider, model: string, prompt: string, options: LLMOptions) {
    const response = await fetch(provider.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${provider.apiKey}`,
      },
      body: JSON.stringify({
        model,
        messages: [{ role: 'user', content: prompt }],
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 2000,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      content: data.choices?.[0]?.message?.content || '',
      usage: data.usage
    };
  }

  private calculateConfidence(response: any, validation: ValidationResult): number {
    let confidence = 0.8; // Base confidence

    // Adjust based on safety validation
    confidence = (confidence + validation.safetyScore) / 2;

    // Adjust based on response length and structure
    if (response.content.length > 100) confidence += 0.05;
    if (response.content.includes('FUNCTION_BLOCK')) confidence += 0.05;

    return Math.min(Math.max(confidence, 0), 1);
  }
}

export const llmClient = new LLMClient();