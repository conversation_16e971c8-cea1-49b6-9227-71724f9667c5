import React, { useState, useEffect } from 'react';
import { 
  Bar<PERSON><PERSON>3, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Calendar, 
  Download, 
  RefreshCw, 
  Users, 
  Activity, 
  Zap, 
  Clock, 
  FileText, 
  CheckCircle2, 
  XCircle, 
  AlertTriangle,
  Filter,
  ChevronDown,
  ChevronUp,
  ArrowUpRight,
  ArrowDownRight,
  <PERSON>rk<PERSON>
} from 'lucide-react';

const AnalyticsDashboard: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([
    'users', 'sessions', 'projects', 'deployments'
  ]);
  
  // Mock data for demonstration
  const [analyticsData, setAnalyticsData] = useState({
    summary: {
      activeUsers: 876,
      activeUsersChange: 12.5,
      newUsers: 124,
      newUsersChange: 8.3,
      totalProjects: 3245,
      totalProjectsChange: 5.2,
      deployments: 1876,
      deploymentsChange: 22.7,
      aiUsage: 15432,
      aiUsageChange: 45.8,
      averageSessionTime: 42,
      averageSessionTimeChange: -3.2
    },
    userActivity: [
      { date: '2023-06-01', users: 720, sessions: 1450 },
      { date: '2023-06-02', users: 735, sessions: 1480 },
      { date: '2023-06-03', users: 742, sessions: 1510 },
      { date: '2023-06-04', users: 750, sessions: 1520 },
      { date: '2023-06-05', users: 765, sessions: 1550 },
      { date: '2023-06-06', users: 780, sessions: 1580 },
      { date: '2023-06-07', users: 795, sessions: 1620 },
      { date: '2023-06-08', users: 810, sessions: 1650 },
      { date: '2023-06-09', users: 825, sessions: 1680 },
      { date: '2023-06-10', users: 840, sessions: 1710 },
      { date: '2023-06-11', users: 855, sessions: 1740 },
      { date: '2023-06-12', users: 870, sessions: 1770 },
      { date: '2023-06-13', users: 876, sessions: 1790 }
    ],
    userTypes: [
      { type: 'Engineer', count: 450, color: '#3b82f6' },
      { type: 'Operator', count: 230, color: '#10b981' },
      { type: 'Manager', count: 120, color: '#8b5cf6' },
      { type: 'Safety Engineer', count: 76, color: '#ef4444' }
    ],
    featureUsage: [
      { feature: 'Ladder Editor', usage: 3245, change: 8.5 },
      { feature: 'Structured Text', usage: 1876, change: 12.3 },
      { feature: 'Simulation', usage: 2543, change: 15.7 },
      { feature: 'AI Assistant', usage: 1987, change: 45.8 },
      { feature: 'Deployment', usage: 1432, change: 22.7 },
      { feature: 'Collaboration', usage: 1245, change: 18.2 }
    ],
    aiMetrics: {
      totalRequests: 15432,
      successRate: 98.5,
      averageResponseTime: 1.2,
      topFeatures: [
        { feature: 'Code Generation', usage: 5432, change: 32.5 },
        { feature: 'Code Explanation', usage: 4321, change: 28.7 },
        { feature: 'Debugging Help', usage: 3210, change: 45.2 },
        { feature: 'Optimization', usage: 2479, change: 38.9 }
      ]
    },
    deploymentStats: {
      total: 1876,
      successful: 1765,
      failed: 111,
      byTarget: [
        { target: 'Siemens S7', count: 876, success: 845 },
        { target: 'Allen-Bradley', count: 543, success: 512 },
        { target: 'Schneider', count: 321, success: 298 },
        { target: 'Beckhoff', count: 136, success: 110 }
      ]
    }
  });

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  }, []);

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  };

  const handleExport = () => {
    // In a real implementation, this would generate and download a report
    alert('Exporting analytics data...');
  };

  const toggleMetric = (metric: string) => {
    if (selectedMetrics.includes(metric)) {
      setSelectedMetrics(selectedMetrics.filter(m => m !== metric));
    } else {
      setSelectedMetrics([...selectedMetrics, metric]);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with controls */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Analytics Dashboard</h2>
          <div className="flex items-center space-x-3">
            <div className="relative">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Filter className="w-4 h-4" />
                <span>Filters</span>
                {showFilters ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
              </button>
              
              {showFilters && (
                <div className="absolute right-0 mt-2 w-64 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-10 p-4">
                  <h4 className="text-white font-medium mb-3">Select Metrics</h4>
                  <div className="space-y-2">
                    {[
                      { id: 'users', label: 'Users' },
                      { id: 'sessions', label: 'Sessions' },
                      { id: 'projects', label: 'Projects' },
                      { id: 'deployments', label: 'Deployments' },
                      { id: 'ai', label: 'AI Usage' },
                      { id: 'features', label: 'Feature Usage' }
                    ].map(metric => (
                      <div key={metric.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`metric-${metric.id}`}
                          checked={selectedMetrics.includes(metric.id)}
                          onChange={() => toggleMetric(metric.id)}
                          className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor={`metric-${metric.id}`} className="text-gray-300">{metric.label}</label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            
            <button
              onClick={handleRefresh}
              className={`p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors ${isLoading ? 'animate-spin text-blue-400' : ''}`}
              disabled={isLoading}
            >
              <RefreshCw className="w-5 h-5" />
            </button>
            
            <button
              onClick={handleExport}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
        
        <p className="text-gray-400 text-sm">
          Viewing analytics data for {timeRange === '7d' ? 'the last 7 days' : 
                                     timeRange === '30d' ? 'the last 30 days' : 
                                     timeRange === '90d' ? 'the last 90 days' : 
                                     'the last year'}
        </p>
      </div>

      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <RefreshCw className="w-12 h-12 text-blue-400 animate-spin mb-4" />
            <p className="text-white">Loading analytics data...</p>
          </div>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto space-y-6">
          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {selectedMetrics.includes('users') && (
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                      <Users className="w-6 h-6 text-blue-400" />
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Active Users</p>
                      <h3 className="text-2xl font-bold text-white">{analyticsData.summary.activeUsers.toLocaleString()}</h3>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-1 ${
                    analyticsData.summary.activeUsersChange >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {analyticsData.summary.activeUsersChange >= 0 ? (
                      <ArrowUpRight className="w-4 h-4" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4" />
                    )}
                    <span>{Math.abs(analyticsData.summary.activeUsersChange)}%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">New Users</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-white">{analyticsData.summary.newUsers}</span>
                    <span className={analyticsData.summary.newUsersChange >= 0 ? 'text-green-400' : 'text-red-400'}>
                      {analyticsData.summary.newUsersChange >= 0 ? '+' : ''}{analyticsData.summary.newUsersChange}%
                    </span>
                  </div>
                </div>
              </div>
            )}
            
            {selectedMetrics.includes('projects') && (
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                      <FileText className="w-6 h-6 text-purple-400" />
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Total Projects</p>
                      <h3 className="text-2xl font-bold text-white">{analyticsData.summary.totalProjects.toLocaleString()}</h3>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-1 ${
                    analyticsData.summary.totalProjectsChange >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {analyticsData.summary.totalProjectsChange >= 0 ? (
                      <ArrowUpRight className="w-4 h-4" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4" />
                    )}
                    <span>{Math.abs(analyticsData.summary.totalProjectsChange)}%</span>
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2 mb-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{ width: '65%' }}></div>
                </div>
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <span>Active: 2,145</span>
                  <span>Archived: 1,100</span>
                </div>
              </div>
            )}
            
            {selectedMetrics.includes('deployments') && (
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                      <Activity className="w-6 h-6 text-green-400" />
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Deployments</p>
                      <h3 className="text-2xl font-bold text-white">{analyticsData.summary.deployments.toLocaleString()}</h3>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-1 ${
                    analyticsData.summary.deploymentsChange >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {analyticsData.summary.deploymentsChange >= 0 ? (
                      <ArrowUpRight className="w-4 h-4" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4" />
                    )}
                    <span>{Math.abs(analyticsData.summary.deploymentsChange)}%</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <div className="flex items-center space-x-1 text-green-400">
                    <CheckCircle2 className="w-4 h-4" />
                    <span>{analyticsData.deploymentStats.successful} successful</span>
                  </div>
                  <div className="flex items-center space-x-1 text-red-400">
                    <XCircle className="w-4 h-4" />
                    <span>{analyticsData.deploymentStats.failed} failed</span>
                  </div>
                </div>
              </div>
            )}
            
            {selectedMetrics.includes('ai') && (
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                      <Sparkles className="w-6 h-6 text-blue-400" />
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">AI Usage</p>
                      <h3 className="text-2xl font-bold text-white">{analyticsData.summary.aiUsage.toLocaleString()}</h3>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-1 ${
                    analyticsData.summary.aiUsageChange >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {analyticsData.summary.aiUsageChange >= 0 ? (
                      <ArrowUpRight className="w-4 h-4" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4" />
                    )}
                    <span>{Math.abs(analyticsData.summary.aiUsageChange)}%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Success Rate</span>
                  <span className="text-green-400">{analyticsData.aiMetrics.successRate}%</span>
                </div>
              </div>
            )}
            
            {selectedMetrics.includes('sessions') && (
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                      <Clock className="w-6 h-6 text-yellow-400" />
                    </div>
                    <div>
                      <p className="text-gray-400 text-sm">Avg. Session Time</p>
                      <h3 className="text-2xl font-bold text-white">{analyticsData.summary.averageSessionTime} min</h3>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-1 ${
                    analyticsData.summary.averageSessionTimeChange >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {analyticsData.summary.averageSessionTimeChange >= 0 ? (
                      <ArrowUpRight className="w-4 h-4" />
                    ) : (
                      <ArrowDownRight className="w-4 h-4" />
                    )}
                    <span>{Math.abs(analyticsData.summary.averageSessionTimeChange)}%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Total Sessions</span>
                  <span className="text-white">1,790 today</span>
                </div>
              </div>
            )}
          </div>

          {/* User Activity Chart */}
          {selectedMetrics.includes('users') && (
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-white">User Activity</h3>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                    <span className="text-sm text-gray-400">Users</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                    <span className="text-sm text-gray-400">Sessions</span>
                  </div>
                </div>
              </div>
              
              <div className="h-64 relative">
                {/* This would be a real chart in production */}
                <div className="absolute inset-0 flex items-end">
                  {analyticsData.userActivity.map((day, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center space-y-1">
                      <div className="relative w-full">
                        <div 
                          className="w-4/5 mx-auto bg-purple-400/20 hover:bg-purple-400/30 transition-colors rounded-t"
                          style={{ height: `${(day.sessions / 2000) * 100}%` }}
                        ></div>
                        <div 
                          className="w-4/5 mx-auto bg-blue-400/20 hover:bg-blue-400/30 transition-colors rounded-t absolute bottom-0 left-0 right-0"
                          style={{ height: `${(day.users / 1000) * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-xs text-gray-500">{day.date.split('-')[2]}</span>
                    </div>
                  ))}
                </div>
                
                {/* Y-axis labels */}
                <div className="absolute left-0 inset-y-0 flex flex-col justify-between text-xs text-gray-500 py-4">
                  <span>1000</span>
                  <span>750</span>
                  <span>500</span>
                  <span>250</span>
                  <span>0</span>
                </div>
              </div>
            </div>
          )}

          {/* Feature Usage and User Types */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {selectedMetrics.includes('features') && (
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-6">Feature Usage</h3>
                <div className="space-y-4">
                  {analyticsData.featureUsage.map((feature, index) => (
                    <div key={index}>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-white">{feature.feature}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-300">{feature.usage.toLocaleString()}</span>
                          <span className={feature.change >= 0 ? 'text-green-400' : 'text-red-400'}>
                            {feature.change >= 0 ? '+' : ''}{feature.change}%
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            index % 4 === 0 ? 'bg-blue-500' :
                            index % 4 === 1 ? 'bg-purple-500' :
                            index % 4 === 2 ? 'bg-green-500' :
                            'bg-yellow-500'
                          }`}
                          style={{ width: `${(feature.usage / 4000) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {selectedMetrics.includes('users') && (
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-6">User Types</h3>
                <div className="flex items-center justify-center h-64">
                  {/* This would be a real pie chart in production */}
                  <div className="relative w-48 h-48">
                    <svg viewBox="0 0 100 100" className="w-full h-full">
                      {/* Simulated pie chart segments */}
                      <circle cx="50" cy="50" r="40" fill="transparent" stroke="#3b82f6" strokeWidth="20" strokeDasharray="125.6 125.6" strokeDashoffset="0" />
                      <circle cx="50" cy="50" r="40" fill="transparent" stroke="#10b981" strokeWidth="20" strokeDasharray="125.6 125.6" strokeDashoffset="-62.8" />
                      <circle cx="50" cy="50" r="40" fill="transparent" stroke="#8b5cf6" strokeWidth="20" strokeDasharray="125.6 125.6" strokeDashoffset="-94.2" />
                      <circle cx="50" cy="50" r="40" fill="transparent" stroke="#ef4444" strokeWidth="20" strokeDasharray="125.6 125.6" strokeDashoffset="-110.6" />
                    </svg>
                  </div>
                  
                  <div className="ml-8 space-y-3">
                    {analyticsData.userTypes.map((type, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <div className="w-3 h-3 rounded-full" style={{ backgroundColor: type.color }}></div>
                        <span className="text-white">{type.type}</span>
                        <span className="text-gray-400">{type.count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* AI Analytics */}
          {selectedMetrics.includes('ai') && (
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-white">AI Assistant Analytics</h3>
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <Sparkles className="w-4 h-4 text-blue-400" />
                  <span>Powered by AI</span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">Total Requests</span>
                    <Zap className="w-4 h-4 text-blue-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">{analyticsData.aiMetrics.totalRequests.toLocaleString()}</div>
                  <div className="text-xs text-green-400 mt-1">+45.8% from last period</div>
                </div>
                
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">Success Rate</span>
                    <CheckCircle2 className="w-4 h-4 text-green-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">{analyticsData.aiMetrics.successRate}%</div>
                  <div className="text-xs text-green-400 mt-1">+1.2% from last period</div>
                </div>
                
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">Avg. Response Time</span>
                    <Clock className="w-4 h-4 text-yellow-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">{analyticsData.aiMetrics.averageResponseTime}s</div>
                  <div className="text-xs text-green-400 mt-1">-0.3s from last period</div>
                </div>
                
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">User Satisfaction</span>
                    <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  </div>
                  <div className="text-2xl font-bold text-white">4.8/5</div>
                  <div className="text-xs text-green-400 mt-1">+0.2 from last period</div>
                </div>
              </div>
              
              <h4 className="text-white font-medium mb-4">Top AI Features</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {analyticsData.aiMetrics.topFeatures.map((feature, index) => (
                    <div key={index}>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-white">{feature.feature}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-gray-300">{feature.usage.toLocaleString()}</span>
                          <span className="text-green-400">+{feature.change}%</span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            index % 4 === 0 ? 'bg-blue-500' :
                            index % 4 === 1 ? 'bg-purple-500' :
                            index % 4 === 2 ? 'bg-green-500' :
                            'bg-yellow-500'
                          }`}
                          style={{ width: `${(feature.usage / 6000) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <h5 className="text-white font-medium mb-3">AI Feedback Analysis</h5>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Positive Feedback</span>
                      <span className="text-white">82%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div className="bg-green-500 h-2 rounded-full" style={{ width: '82%' }}></div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Neutral Feedback</span>
                      <span className="text-white">12%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '12%' }}></div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-gray-400">Negative Feedback</span>
                      <span className="text-white">6%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div className="bg-red-500 h-2 rounded-full" style={{ width: '6%' }}></div>
                    </div>
                  </div>
                  
                  <div className="mt-4 pt-4 border-t border-gray-600">
                    <h5 className="text-white font-medium mb-2">Top Improvement Areas</h5>
                    <ul className="space-y-1 text-sm">
                      <li className="text-gray-300">• Code generation accuracy</li>
                      <li className="text-gray-300">• Response time for complex queries</li>
                      <li className="text-gray-300">• Safety validation suggestions</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Deployment Analytics */}
          {selectedMetrics.includes('deployments') && (
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold text-white mb-6">Deployment Analytics</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">Total Deployments</span>
                    <Activity className="w-4 h-4 text-blue-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">{analyticsData.deploymentStats.total.toLocaleString()}</div>
                  <div className="text-xs text-green-400 mt-1">+22.7% from last period</div>
                </div>
                
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">Success Rate</span>
                    <CheckCircle2 className="w-4 h-4 text-green-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">
                    {((analyticsData.deploymentStats.successful / analyticsData.deploymentStats.total) * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-green-400 mt-1">+1.5% from last period</div>
                </div>
                
                <div className="bg-gray-700/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">Failed Deployments</span>
                    <AlertTriangle className="w-4 h-4 text-yellow-400" />
                  </div>
                  <div className="text-2xl font-bold text-white">{analyticsData.deploymentStats.failed}</div>
                  <div className="text-xs text-red-400 mt-1">+3.2% from last period</div>
                </div>
              </div>
              
              <h4 className="text-white font-medium mb-4">Deployments by Target</h4>
              <div className="space-y-4">
                {analyticsData.deploymentStats.byTarget.map((target, index) => (
                  <div key={index}>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-white">{target.target}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-300">{target.count}</span>
                        <span className="text-green-400">{((target.success / target.count) * 100).toFixed(1)}%</span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-green-500 h-2 rounded-full" 
                        style={{ width: `${(target.success / target.count) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AnalyticsDashboard;