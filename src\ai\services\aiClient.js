"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Update the getAuthToken method for development
class AIClient {
    constructor() {
        this.baseUrl = import.meta.env.VITE_AI_BACKEND_URL || 'http://localhost:3001/api/ai';
    }
    async request(request) {
        const response = await fetch(`${this.baseUrl}/request`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.getAuthToken()}`
            },
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`AI request failed: ${response.statusText} - ${errorData.error || ''}`);
        }
        return response.json();
    }
    getAuthToken() {
        // For development, use a dev token
        if (import.meta.env.NODE_ENV === 'development') {
            return 'dev-token';
        }
        // In production, get from your auth system
        return localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || '';
    }
}
