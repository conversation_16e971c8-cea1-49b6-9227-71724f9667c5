export class AIJobQueue {
  private jobs = new Map();

  async addJob(request: any, userId: string, priority: string) {
    const jobId = `job_${Date.now()}`;
    this.jobs.set(jobId, {
      id: jobId,
      request,
      userId,
      priority,
      status: 'pending',
      created: new Date()
    });
    return jobId;
  }

  async getJob(jobId: string) {
    return this.jobs.get(jobId);
  }
}

export const aiJobQueue = new AIJobQueue();