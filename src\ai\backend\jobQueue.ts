import { AIRequest, AIResponse } from '../hooks/useAI';

export interface AIJob {
  id: string;
  type: 'generate' | 'explain' | 'refactor' | 'suggest' | 'debug';
  priority: 'low' | 'normal' | 'high' | 'critical';
  userId: string;
  request: AIRequest;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  result?: AIResponse;
  error?: string;
  retryCount: number;
  maxRetries: number;
}

class AIJobQueue {
  private jobs: Map<string, AIJob> = new Map();
  private processing = false;
  private maxConcurrent = 3;
  private currentlyProcessing = 0;

  async addJob(request: AIRequest, userId: string, priority: <PERSON>Job['priority'] = 'normal'): Promise<string> {
    const job: AIJob = {
      id: `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: request.type,
      priority,
      userId,
      request,
      status: 'pending',
      createdAt: new Date(),
      retryCount: 0,
      maxRetries: 3
    };

    this.jobs.set(job.id, job);
    
    // Start processing if not already running
    if (!this.processing) {
      this.startProcessing();
    }

    return job.id;
  }

  async getJob(jobId: string): Promise<AIJob | null> {
    return this.jobs.get(jobId) || null;
  }

  async getJobsByUser(userId: string): Promise<AIJob[]> {
    return Array.from(this.jobs.values()).filter(job => job.userId === userId);
  }

  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.jobs.get(jobId);
    if (!job || job.status === 'processing') {
      return false;
    }

    this.jobs.delete(jobId);
    return true;
  }

  private async startProcessing(): Promise<void> {
    this.processing = true;

    while (this.hasPendingJobs() || this.currentlyProcessing > 0) {
      if (this.currentlyProcessing < this.maxConcurrent) {
        const job = this.getNextJob();
        if (job) {
          this.processJob(job);
        }
      }
      
      // Wait a bit before checking again
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.processing = false;
  }

  private hasPendingJobs(): boolean {
    return Array.from(this.jobs.values()).some(job => job.status === 'pending');
  }

  private getNextJob(): AIJob | null {
    const pendingJobs = Array.from(this.jobs.values())
      .filter(job => job.status === 'pending')
      .sort((a, b) => {
        // Sort by priority, then by creation time
        const priorityOrder = { critical: 4, high: 3, normal: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return a.createdAt.getTime() - b.createdAt.getTime();
      });

    return pendingJobs[0] || null;
  }

  private async processJob(job: AIJob): Promise<void> {
    this.currentlyProcessing++;
    job.status = 'processing';
    job.startedAt = new Date();

    try {
      // Import AI client dynamically to avoid circular dependencies
      const { aiClient } = await import('../services/aiClient');
      const result = await aiClient.request(job.request);
      
      job.result = result;
      job.status = 'completed';
      job.completedAt = new Date();
    } catch (error) {
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.retryCount++;

      if (job.retryCount < job.maxRetries) {
        // Retry with exponential backoff
        const delay = Math.pow(2, job.retryCount) * 1000;
        setTimeout(() => {
          job.status = 'pending';
        }, delay);
      } else {
        job.status = 'failed';
        job.completedAt = new Date();
      }
    } finally {
      this.currentlyProcessing--;
    }
  }

  // Get queue statistics
  getStats(): {
    total: number;
    pending: number;
    processing: number;
    completed: number;
    failed: number;
  } {
    const jobs = Array.from(this.jobs.values());
    return {
      total: jobs.length,
      pending: jobs.filter(j => j.status === 'pending').length,
      processing: jobs.filter(j => j.status === 'processing').length,
      completed: jobs.filter(j => j.status === 'completed').length,
      failed: jobs.filter(j => j.status === 'failed').length
    };
  }

  // Cleanup completed jobs older than 24 hours
  cleanup(): void {
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000);
    for (const [id, job] of this.jobs.entries()) {
      if ((job.status === 'completed' || job.status === 'failed') && 
          job.completedAt && job.completedAt < cutoff) {
        this.jobs.delete(id);
      }
    }
  }
}

export const aiJobQueue = new AIJobQueue();

// Cleanup old jobs every hour
setInterval(() => {
  aiJobQueue.cleanup();
}, 60 * 60 * 1000);