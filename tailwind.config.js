/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // Industrial Color Palette
        'base-dark': '#111213',
        'primary': '#FDB100',
        'secondary': '#4F5D75',
        'neutral': '#E1E1E1',
        'accent': '#00C2A8',
        'error': '#FF5454',
        'success': '#82D173',
        
        // Extended palette for UI components
        'base': {
          50: '#f8f9fa',
          100: '#e9ecef',
          200: '#dee2e6',
          300: '#ced4da',
          400: '#adb5bd',
          500: '#6c757d',
          600: '#495057',
          700: '#343a40',
          800: '#212529',
          900: '#111213',
        },
        'amber': {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#FDB100',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        'control': {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#4F5D75',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        'teal': {
          50: '#f0fdfa',
          100: '#ccfbf1',
          200: '#99f6e4',
          300: '#5eead4',
          400: '#2dd4bf',
          500: '#00C2A8',
          600: '#0d9488',
          700: '#0f766e',
          800: '#115e59',
          900: '#134e4a',
        }
      },
      backgroundColor: {
        'primary': '#FDB100',
        'secondary': '#4F5D75',
        'accent': '#00C2A8',
        'error': '#FF5454',
        'success': '#82D173',
        'base-dark': '#111213',
        'neutral': '#E1E1E1',
      },
      textColor: {
        'primary': '#FDB100',
        'secondary': '#4F5D75',
        'accent': '#00C2A8',
        'error': '#FF5454',
        'success': '#82D173',
        'neutral': '#E1E1E1',
      },
      borderColor: {
        'primary': '#FDB100',
        'secondary': '#4F5D75',
        'accent': '#00C2A8',
        'error': '#FF5454',
        'success': '#82D173',
        'neutral': '#E1E1E1',
      }
    },
  },
  plugins: [],
  safelist: [
    // Ensure dynamic color classes are included
    'bg-primary', 'bg-secondary', 'bg-accent', 'bg-error', 'bg-success',
    'text-primary', 'text-secondary', 'text-accent', 'text-error', 'text-success',
    'border-primary', 'border-secondary', 'border-accent', 'border-error', 'border-success',
    'hover:bg-primary', 'hover:bg-secondary', 'hover:bg-accent',
    'focus:border-primary', 'focus:border-accent',
    // Opacity variants
    'bg-primary/10', 'bg-primary/20', 'bg-secondary/10', 'bg-secondary/20',
    'bg-accent/10', 'bg-accent/20', 'bg-error/10', 'bg-error/20',
    'bg-success/10', 'bg-success/20'
  ]
};