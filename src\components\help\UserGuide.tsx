import React, { useState } from 'react';
import { 
  Book, 
  ChevronLeft, 
  Search, 
  Command, 
  Play, 
  Grid3X3, 
  Code, 
  Monitor, 
  Network, 
  Cable, 
  Eye, 
  Zap, 
  Download, 
  Upload, 
  Users, 
  Shield, 
  Settings, 
  HelpCircle,
  Cpu,
  FileText,
  Workflow,
  GitBranch,
  Sparkles,
  Home,
  ArrowLeft,
  Video,
  MessageSquare,
  ExternalLink
} from 'lucide-react';

interface UserGuideProps {
  onClose: () => void;
}

const UserGuide: React.FC<UserGuideProps> = ({ onClose }) => {
  const [activeSection, setActiveSection] = useState('getting-started');
  const [activeTab, setActiveTab] = useState('guide');

  const handleSectionClick = (sectionId: string) => {
    setActiveSection(sectionId);
    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });
  };

  const renderGuideContent = () => (
    <div className="flex-1 overflow-y-auto p-6">
      <div className="max-w-4xl mx-auto space-y-10">
        <section id="getting-started">
          <h2 className="text-2xl font-bold text-white mb-4">Getting Started</h2>
          <div className="prose prose-invert">
            <p>
              Welcome to LUREON, the modern Industrial Automation IDE designed for PLC engineers. 
              This guide will help you get started with the platform and explore its features.
            </p>
            
            <h3>Creating Your First Project</h3>
            <ol>
              <li>Click the menu icon in the top-left corner and select "New Project"</li>
              <li>Enter a project name and description</li>
              <li>Select a PLC target type (e.g., Siemens S7-1500)</li>
              <li>Click "Create Project"</li>
            </ol>
            
            <h3>Opening an Existing Project</h3>
            <ol>
              <li>Click the menu icon in the top-left corner</li>
              <li>Select "Open Project"</li>
              <li>Browse to your project file or select from recent projects</li>
            </ol>
            
            <h3>Importing and Exporting</h3>
            <p>
              LUREON supports importing and exporting projects for backup or sharing:
            </p>
            <ul>
              <li>To export: Click the menu icon and select "Export Project"</li>
              <li>To import: Click the menu icon and select "Import Project"</li>
            </ul>
          </div>
        </section>
        
        <section id="interface">
          <h2 className="text-2xl font-bold text-white mb-4">Interface Overview</h2>
          <div className="prose prose-invert">
            <p>
              The LUREON IDE is divided into several key areas:
            </p>
            
            <div className="bg-gray-800 p-4 rounded-lg mb-4">
              <img src="https://via.placeholder.com/800x450?text=LUREON+Interface+Overview" alt="LUREON Interface" className="rounded-lg w-full" />
            </div>
            
            <ol>
              <li><strong>Top Bar</strong>: Contains the main menu, search, collaboration status, and user settings</li>
              <li><strong>Project Navigator</strong>: Tree view of your project structure (programs, tags, targets)</li>
              <li><strong>Editor Pane</strong>: Main workspace for editing programs and viewing tools</li>
              <li><strong>Properties Panel</strong>: Shows properties of the selected item and AI assistant</li>
              <li><strong>Bottom Panel</strong>: Console output, diagnostics, and AI chat</li>
            </ol>
            
            <h3>Command Palette</h3>
            <p>
              The command palette provides quick access to all IDE functions:
            </p>
            <ol>
              <li>Press <code>Ctrl+K</code> (or <code>Cmd+K</code> on Mac) to open</li>
              <li>Type to search for commands</li>
              <li>Use arrow keys to navigate and Enter to select</li>
              <li>Try typing "ai" to access AI assistant commands</li>
            </ol>
          </div>
        </section>
        
        <section id="programming">
          <h2 className="text-2xl font-bold text-white mb-4">Programming</h2>
          <div className="prose prose-invert">
            <p>
              LUREON supports multiple IEC 61131-3 programming languages:
            </p>
            
            <h3>Creating a New Program</h3>
            <ol>
              <li>Right-click on "Programs" in the Project Navigator</li>
              <li>Select the type of program you want to create</li>
              <li>Enter a name for your program</li>
            </ol>
            
            <p>
              Alternatively, you can use the command palette (<code>Ctrl+K</code>) and type "new program".
            </p>
          </div>
        </section>
        
        <section id="ladder-logic">
          <h2 className="text-2xl font-bold text-white mb-4">Ladder Logic</h2>
          <div className="prose prose-invert">
            <p>
              Ladder Logic is a graphical programming language that resembles electrical relay logic.
            </p>
            
            <div className="bg-gray-800 p-4 rounded-lg mb-4">
              <img src="https://via.placeholder.com/800x400?text=Ladder+Logic+Editor" alt="Ladder Logic Editor" className="rounded-lg w-full" />
            </div>
            
            <h3>Creating a Ladder Logic Program</h3>
            <ol>
              <li>Right-click on "Programs" in the Project Navigator</li>
              <li>Select "New Ladder Program"</li>
              <li>Enter a name for your program</li>
            </ol>
            
            <h3>Adding Rungs</h3>
            <ol>
              <li>Click the "Add Rung" button at the top of the editor</li>
              <li>Each rung represents a line of ladder logic</li>
            </ol>
            
            <h3>Adding Elements</h3>
            <ol>
              <li>Select a rung by clicking on it</li>
              <li>Use the toolbar to add elements:
                <ul>
                  <li><code>][</code> - Normally Open Contact</li>
                  <li><code>/]</code> - Normally Closed Contact</li>
                  <li><code>( )</code> - Output Coil</li>
                  <li><code>TON</code> - Timer On-Delay</li>
                  <li><code>CTU</code> - Counter Up</li>
                  <li>And many more...</li>
                </ul>
              </li>
              <li>Click on an element to assign a tag</li>
            </ol>
            
            <h3>Quick Text Input</h3>
            <ol>
              <li>Double-click on a rung to open the quick input dialog</li>
              <li>Type element names separated by spaces (e.g., "NO NC COIL TON")</li>
              <li>Press Enter or click "Add" to add the elements to the rung</li>
              <li>Supported elements: NO, NC, COIL, TON, TOF, CTU, CTD, CMP, CALC, SFT, MUT</li>
            </ol>
            
            <h3>Editing Elements</h3>
            <ol>
              <li>Click on an element to select it</li>
              <li>Use the properties panel to edit its properties</li>
              <li>For contacts, you can toggle between normally open and normally closed</li>
            </ol>
            
            <h3>Rung Operations</h3>
            <ul>
              <li>Use the buttons on each rung to:
                <ul>
                  <li>Move rungs up or down</li>
                  <li>Duplicate rungs</li>
                  <li>Delete rungs</li>
                  <li>Enable/disable rungs</li>
                </ul>
              </li>
              <li>Add comments to rungs for documentation</li>
            </ul>
            
            <h3>Simulation in Editor</h3>
            <ol>
              <li>Click the lightning bolt icon in the editor toolbar</li>
              <li>The simulation will start and the icon will turn amber</li>
              <li>Watch as signals flow through your logic in real-time</li>
              <li>Click the lightning bolt again to stop simulation</li>
            </ol>
            
            <h3>Parallel Branches</h3>
            <p>
              Ladder logic supports parallel branches for implementing OR logic:
            </p>
            <ol>
              <li>Select an element in your rung</li>
              <li>Click the "Add Branch" button in the toolbar</li>
              <li>Add elements to the new branch</li>
              <li>Connect branches by clicking and dragging between connection points</li>
            </ol>
            <p>
              Parallel branches allow you to create complex logic structures with both inputs and outputs in parallel paths.
            </p>
          </div>
        </section>
        
        <section id="structured-text">
          <h2 className="text-2xl font-bold text-white mb-4">Structured Text</h2>
          <div className="prose prose-invert">
            <p>
              Structured Text (ST) is a high-level text-based programming language similar to Pascal.
            </p>
            
            <h3>Creating a Structured Text Program</h3>
            <ol>
              <li>Right-click on "Programs" in the Project Navigator</li>
              <li>Select "New Structured Text Program"</li>
              <li>Enter a name for your program</li>
            </ol>
            
            <h3>ST Editor Features</h3>
            <ul>
              <li>Syntax highlighting for IEC 61131-3 ST</li>
              <li>Code completion (IntelliSense)</li>
              <li>Error checking and validation</li>
              <li>Code folding for blocks</li>
            </ul>
            
            <h3>ST Syntax Example</h3>
            <pre className="bg-gray-800 p-4 rounded-lg overflow-x-auto">
              <code className="text-sm">
{`FUNCTION_BLOCK MotorControl
VAR_INPUT
    Start_Button : BOOL;
    Stop_Button : BOOL;
    Emergency_Stop : BOOL;
END_VAR

VAR_OUTPUT
    Motor_Run : BOOL;
END_VAR

VAR
    Motor_Running : BOOL;
END_VAR

// Motor start/stop logic with seal-in circuit
IF Start_Button AND NOT Stop_Button AND Emergency_Stop THEN
    Motor_Running := TRUE;
ELSIF Stop_Button OR NOT Emergency_Stop THEN
    Motor_Running := FALSE;
END_IF;

Motor_Run := Motor_Running;

END_FUNCTION_BLOCK`}
              </code>
            </pre>
          </div>
        </section>
        
        <section id="function-blocks">
          <h2 className="text-2xl font-bold text-white mb-4">Function Blocks</h2>
          <div className="prose prose-invert">
            <p>
              Function Block Diagram (FBD) is a graphical language for representing signal and data flows.
            </p>
            
            <h3>Creating a Function Block Diagram</h3>
            <ol>
              <li>Right-click on "Programs" in the Project Navigator</li>
              <li>Select "New Function Block Diagram"</li>
              <li>Enter a name for your program</li>
            </ol>
            
            <h3>Working with Function Blocks</h3>
            <ul>
              <li>Drag function blocks from the library</li>
              <li>Connect inputs and outputs by dragging between connection points</li>
              <li>Configure block parameters in the properties panel</li>
              <li>Create custom function blocks for reuse</li>
            </ul>
          </div>
        </section>
        
        <section id="simulation">
          <h2 className="text-2xl font-bold text-white mb-4">Simulation and Testing</h2>
          <div className="prose prose-invert">
            <p>
              LUREON includes a powerful simulation engine for testing your logic without hardware.
            </p>
            
            <h3>Running Simulation</h3>
            <ol>
              <li>Click the lightning bolt icon in the toolbar or press F5</li>
              <li>The simulator will start and the icon will turn amber</li>
              <li>Open the Simulator tab to view and modify tag values</li>
              <li>You can also run simulation directly in the editor for ladder logic</li>
            </ol>
            
            <h3>Monitoring Tags</h3>
            <ul>
              <li>View tag values in real-time</li>
              <li>Modify input tags to test different scenarios</li>
              <li>Watch outputs change based on your logic</li>
            </ul>
            
            <h3>Signal Tracing</h3>
            <ol>
              <li>Open the Signal Tracer tab</li>
              <li>Select a tag to trace</li>
              <li>Start tracing to visualize signal flow through your logic</li>
            </ol>
          </div>
        </section>
        
        <section id="shortcuts">
          <h2 className="text-2xl font-bold text-white mb-4">Keyboard Shortcuts</h2>
          <div className="prose prose-invert">
            <p>
              LUREON provides numerous keyboard shortcuts to enhance productivity:
            </p>
            
            <div className="bg-gray-800 p-4 rounded-lg overflow-x-auto">
              <table className="min-w-full text-sm">
                <thead>
                  <tr>
                    <th className="text-left py-2 px-4 border-b border-gray-700">Shortcut</th>
                    <th className="text-left py-2 px-4 border-b border-gray-700">Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">Ctrl+K</td>
                    <td className="py-2 px-4 border-b border-gray-700">Open command palette</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">Ctrl+N</td>
                    <td className="py-2 px-4 border-b border-gray-700">New ladder program</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">Ctrl+S</td>
                    <td className="py-2 px-4 border-b border-gray-700">Save project</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">Ctrl+D</td>
                    <td className="py-2 px-4 border-b border-gray-700">Download to PLC</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">Ctrl+U</td>
                    <td className="py-2 px-4 border-b border-gray-700">Upload from PLC</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">F5</td>
                    <td className="py-2 px-4 border-b border-gray-700">Start simulation</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">Shift+F5</td>
                    <td className="py-2 px-4 border-b border-gray-700">Start PLC</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">Shift+F6</td>
                    <td className="py-2 px-4 border-b border-gray-700">Stop PLC</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">Ctrl+H</td>
                    <td className="py-2 px-4 border-b border-gray-700">Open User Guide</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-4 border-b border-gray-700 font-mono">Ctrl+Shift+A</td>
                    <td className="py-2 px-4 border-b border-gray-700">Open AI Assistant</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>
        
        <section id="troubleshooting">
          <h2 className="text-2xl font-bold text-white mb-4">Troubleshooting</h2>
          <div className="prose prose-invert">
            <h3>Common Issues</h3>
            
            <h4>Connection Problems:</h4>
            <ul>
              <li>Verify PLC IP address and network connectivity</li>
              <li>Check firewall settings</li>
              <li>Ensure correct protocol selection</li>
            </ul>

            <h4>Sync Issues:</h4>
            <ul>
              <li>Check internet connectivity</li>
              <li>Clear browser cache</li>
              <li>Restart application</li>
            </ul>

            <h4>Performance:</h4>
            <ul>
              <li>Reduce simulation frequency</li>
              <li>Close unused tabs</li>
              <li>Clear browser storage (Settings &gt; Clear Cache)</li>
              <li>Disable real-time collaboration if not needed</li>
              <li>Split large programs into smaller function blocks</li>
            </ul>

            <h3>Debug Mode</h3>
            <p>Enable debug logging:</p>
            <pre className="bg-gray-800 p-3 rounded">
              <code>localStorage.setItem('debug', 'lureon:*');</code>
            </pre>
          </div>
        </section>
        
        {/* Add more sections as needed */}
        
        {/* Related Topics */}
        <section className="mt-8 pt-8 border-t border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4">Related Topics</h3>
          <div className="grid grid-cols-3 gap-4">
            {activeSection === 'getting-started' && (
              <>
                <a href="#interface" 
                  onClick={(e) => {
                    e.preventDefault();
                    handleSectionClick('interface');
                  }}
                  className="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg transition-colors">
                  <h4 className="text-white font-semibold mb-2">Interface Overview</h4>
                  <p className="text-gray-400 text-sm">Learn about the IDE interface components</p>
                </a>
                <a href="#programming" 
                  onClick={(e) => {
                    e.preventDefault();
                    handleSectionClick('programming');
                  }}
                  className="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg transition-colors">
                  <h4 className="text-white font-semibold mb-2">Programming</h4>
                  <p className="text-gray-400 text-sm">Start programming with different languages</p>
                </a>
                <a href="#shortcuts" 
                  onClick={(e) => {
                    e.preventDefault();
                    handleSectionClick('shortcuts');
                  }}
                  className="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg transition-colors">
                  <h4 className="text-white font-semibold mb-2">Keyboard Shortcuts</h4>
                  <p className="text-gray-400 text-sm">Learn essential keyboard shortcuts</p>
                </a>
              </>
            )}
            
            {activeSection === 'ladder-logic' && (
              <>
                <a href="#structured-text" 
                  onClick={(e) => {
                    e.preventDefault();
                    handleSectionClick('structured-text');
                  }}
                  className="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg transition-colors">
                  <h4 className="text-white font-semibold mb-2">Structured Text</h4>
                  <p className="text-gray-400 text-sm">Learn about text-based programming</p>
                </a>
                <a href="#function-blocks" 
                  onClick={(e) => {
                    e.preventDefault();
                    handleSectionClick('function-blocks');
                  }}
                  className="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg transition-colors">
                  <h4 className="text-white font-semibold mb-2">Function Blocks</h4>
                  <p className="text-gray-400 text-sm">Create reusable function blocks</p>
                </a>
                <a href="#simulation" 
                  onClick={(e) => {
                    e.preventDefault();
                    handleSectionClick('simulation');
                  }}
                  className="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg transition-colors">
                  <h4 className="text-white font-semibold mb-2">Simulation</h4>
                  <p className="text-gray-400 text-sm">Test your ladder logic with simulation</p>
                </a>
              </>
            )}
            
            {activeSection !== 'getting-started' && activeSection !== 'ladder-logic' && (
              <>
                <a href="#getting-started" 
                  onClick={(e) => {
                    e.preventDefault();
                    handleSectionClick('getting-started');
                  }}
                  className="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg transition-colors">
                  <h4 className="text-white font-semibold mb-2">Getting Started</h4>
                  <p className="text-gray-400 text-sm">Back to basics</p>
                </a>
                <a href="#ladder-logic" 
                  onClick={(e) => {
                    e.preventDefault();
                    handleSectionClick('ladder-logic');
                  }}
                  className="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg transition-colors">
                  <h4 className="text-white font-semibold mb-2">Ladder Logic</h4>
                  <p className="text-gray-400 text-sm">Learn about ladder programming</p>
                </a>
                <a href="#troubleshooting" 
                  onClick={(e) => {
                    e.preventDefault();
                    handleSectionClick('troubleshooting');
                  }}
                  className="bg-gray-800 hover:bg-gray-700 p-4 rounded-lg transition-colors">
                  <h4 className="text-white font-semibold mb-2">Troubleshooting</h4>
                  <p className="text-gray-400 text-sm">Solve common issues</p>
                </a>
              </>
            )}
          </div>
        </section>
      </div>
    </div>
  );

  const renderVideosContent = () => (
    <div className="flex-1 overflow-y-auto p-6">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-2xl font-bold text-white mb-6">Video Tutorials</h2>
        
        <div className="grid grid-cols-2 gap-6">
          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="aspect-video bg-gray-900 flex items-center justify-center">
              <Play className="w-12 h-12 text-white opacity-70" />
            </div>
            <div className="p-4">
              <h3 className="text-white font-semibold mb-2">Getting Started with LUREON</h3>
              <p className="text-gray-400 text-sm mb-3">Learn the basics of the LUREON IDE and create your first project.</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">10:25</span>
                <button className="text-blue-400 hover:text-blue-300 text-sm">Watch Now</button>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="aspect-video bg-gray-900 flex items-center justify-center">
              <Play className="w-12 h-12 text-white opacity-70" />
            </div>
            <div className="p-4">
              <h3 className="text-white font-semibold mb-2">Ladder Logic Programming</h3>
              <p className="text-gray-400 text-sm mb-3">Master ladder logic programming with practical examples.</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">15:42</span>
                <button className="text-blue-400 hover:text-blue-300 text-sm">Watch Now</button>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="aspect-video bg-gray-900 flex items-center justify-center">
              <Play className="w-12 h-12 text-white opacity-70" />
            </div>
            <div className="p-4">
              <h3 className="text-white font-semibold mb-2">HMI Design Fundamentals</h3>
              <p className="text-gray-400 text-sm mb-3">Create effective HMI screens for your automation projects.</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">12:18</span>
                <button className="text-blue-400 hover:text-blue-300 text-sm">Watch Now</button>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="aspect-video bg-gray-900 flex items-center justify-center">
              <Play className="w-12 h-12 text-white opacity-70" />
            </div>
            <div className="p-4">
              <h3 className="text-white font-semibold mb-2">Using the AI Assistant</h3>
              <p className="text-gray-400 text-sm mb-3">Boost your productivity with AI-powered code generation and assistance.</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">8:55</span>
                <button className="text-blue-400 hover:text-blue-300 text-sm">Watch Now</button>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="aspect-video bg-gray-900 flex items-center justify-center">
              <Play className="w-12 h-12 text-white opacity-70" />
            </div>
            <div className="p-4">
              <h3 className="text-white font-semibold mb-2">Simulation and Testing</h3>
              <p className="text-gray-400 text-sm mb-3">Test your PLC programs without hardware using the built-in simulator.</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">14:30</span>
                <button className="text-blue-400 hover:text-blue-300 text-sm">Watch Now</button>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg overflow-hidden">
            <div className="aspect-video bg-gray-900 flex items-center justify-center">
              <Play className="w-12 h-12 text-white opacity-70" />
            </div>
            <div className="p-4">
              <h3 className="text-white font-semibold mb-2">Real-time Collaboration</h3>
              <p className="text-gray-400 text-sm mb-3">Work together with your team in real-time on PLC projects.</p>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">11:05</span>
                <button className="text-blue-400 hover:text-blue-300 text-sm">Watch Now</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTroubleshootingContent = () => (
    <div className="flex-1 overflow-y-auto p-6">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-2xl font-bold text-white mb-6">Troubleshooting</h2>
        
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Common Issues</h3>
            
            <div className="space-y-4">
              <div className="border-b border-gray-700 pb-4">
                <h4 className="text-white font-medium mb-2">Connection Problems</h4>
                <p className="text-gray-400 mb-3">Issues connecting to PLC hardware or network devices.</p>
                <div className="bg-gray-900 rounded-lg p-4">
                  <h5 className="text-blue-400 font-medium mb-2">Solutions:</h5>
                  <ul className="list-disc pl-5 text-gray-300 space-y-1">
                    <li>Verify PLC IP address and network connectivity</li>
                    <li>Check firewall settings on your computer</li>
                    <li>Ensure correct protocol selection (EtherNet/IP, Profinet, etc.)</li>
                    <li>Try pinging the PLC from your computer</li>
                    <li>Verify that the PLC is powered on and in RUN mode</li>
                  </ul>
                </div>
              </div>
              
              <div className="border-b border-gray-700 pb-4">
                <h4 className="text-white font-medium mb-2">Sync Issues</h4>
                <p className="text-gray-400 mb-3">Problems with project synchronization or collaboration.</p>
                <div className="bg-gray-900 rounded-lg p-4">
                  <h5 className="text-blue-400 font-medium mb-2">Solutions:</h5>
                  <ul className="list-disc pl-5 text-gray-300 space-y-1">
                    <li>Check internet connectivity</li>
                    <li>Clear browser cache and reload the application</li>
                    <li>Verify that all team members have the latest version</li>
                    <li>Check for conflicting changes in the project</li>
                    <li>Try manually triggering a sync from the status bar</li>
                  </ul>
                </div>
              </div>
              
              <div className="border-b border-gray-700 pb-4">
                <h4 className="text-white font-medium mb-2">Performance Issues</h4>
                <p className="text-gray-400 mb-3">Slow performance or high resource usage.</p>
                <div className="bg-gray-900 rounded-lg p-4">
                  <h5 className="text-blue-400 font-medium mb-2">Solutions:</h5>
                  <ul className="list-disc pl-5 text-gray-300 space-y-1">
                    <li>Reduce simulation frequency in settings</li>
                    <li>Close unused tabs and editors</li>
                    <li>Clear browser storage (Settings &gt; Clear Cache)</li>
                    <li>Disable real-time collaboration if not needed</li>
                    <li>Split large programs into smaller function blocks</li>
                  </ul>
                </div>
              </div>
              
              <div>
                <h4 className="text-white font-medium mb-2">Programming Errors</h4>
                <p className="text-gray-400 mb-3">Issues with PLC code or logic errors.</p>
                <div className="bg-gray-900 rounded-lg p-4">
                  <h5 className="text-blue-400 font-medium mb-2">Solutions:</h5>
                  <ul className="list-disc pl-5 text-gray-300 space-y-1">
                    <li>Use the Signal Tracer to debug logic flow</li>
                    <li>Check tag assignments and data types</li>
                    <li>Verify rung execution order</li>
                    <li>Use simulation to test different scenarios</li>
                    <li>Ask the AI Assistant for help with debugging</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800 rounded-lg p-6">
            <h3 className="text-xl font-semibold text-white mb-4">Getting Help</h3>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-gray-900 rounded-lg p-4 flex flex-col items-center text-center">
                <MessageSquare className="w-10 h-10 text-blue-400 mb-3" />
                <h4 className="text-white font-medium mb-2">Community Forum</h4>
                <p className="text-gray-400 text-sm mb-3">Connect with other LUREON users to ask questions and share solutions.</p>
                <a href="#" className="text-blue-400 hover:text-blue-300 text-sm">Visit Forum</a>
              </div>
              
              <div className="bg-gray-900 rounded-lg p-4 flex flex-col items-center text-center">
                <Book className="w-10 h-10 text-green-400 mb-3" />
                <h4 className="text-white font-medium mb-2">Documentation</h4>
                <p className="text-gray-400 text-sm mb-3">Comprehensive guides, API references, and examples.</p>
                <a href="#" className="text-blue-400 hover:text-blue-300 text-sm">Browse Docs</a>
              </div>
              
              <div className="bg-gray-900 rounded-lg p-4 flex flex-col items-center text-center">
                <HelpCircle className="w-10 h-10 text-purple-400 mb-3" />
                <h4 className="text-white font-medium mb-2">Support Ticket</h4>
                <p className="text-gray-400 text-sm mb-3">Get direct assistance from our support team.</p>
                <a href="#" className="text-blue-400 hover:text-blue-300 text-sm">Open Ticket</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-screen bg-base-dark text-neutral overflow-hidden flex flex-col">
      {/* Header */}
      <div className="bg-control-800 border-b border-control-600 p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Book className="w-5 h-5 text-blue-400" />
          <h1 className="text-xl font-semibold text-white">LUREON User Guide</h1>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={onClose}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Return to Application</span>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-control-800 border-b border-control-600 px-4">
        <div className="flex">
          <button
            className={`px-4 py-3 text-sm transition-colors ${
              activeTab === 'guide' 
                ? 'text-white border-b-2 border-blue-500' 
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('guide')}
          >
            <Book className="w-4 h-4 inline mr-2" />
            User Guide
          </button>
          <button
            className={`px-4 py-3 text-sm transition-colors ${
              activeTab === 'videos' 
                ? 'text-white border-b-2 border-blue-500' 
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('videos')}
          >
            <Video className="w-4 h-4 inline mr-2" />
            Video Tutorials
          </button>
          <button
            className={`px-4 py-3 text-sm transition-colors ${
              activeTab === 'troubleshooting' 
                ? 'text-white border-b-2 border-blue-500' 
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('troubleshooting')}
          >
            <HelpCircle className="w-4 h-4 inline mr-2" />
            Troubleshooting
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden flex">
        {/* Sidebar - only show for guide tab */}
        {activeTab === 'guide' && (
          <div className="w-64 bg-control-800 border-r border-control-600 overflow-y-auto">
            <div className="p-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-control-400" />
                <input
                  type="text"
                  placeholder="Search guide..."
                  className="w-full bg-control-700 border border-control-600 rounded-lg pl-10 pr-4 py-2 text-sm text-white placeholder-control-400"
                />
              </div>
            </div>
            <nav className="p-2">
              <ul className="space-y-1">
                {[
                  { id: 'getting-started', name: 'Getting Started', icon: <Play className="w-4 h-4" /> },
                  { id: 'interface', name: 'Interface Overview', icon: <Grid3X3 className="w-4 h-4" /> },
                  { id: 'programming', name: 'Programming', icon: <Code className="w-4 h-4" /> },
                  { id: 'ladder-logic', name: 'Ladder Logic', icon: <Grid3X3 className="w-4 h-4" /> },
                  { id: 'structured-text', name: 'Structured Text', icon: <FileText className="w-4 h-4" /> },
                  { id: 'function-blocks', name: 'Function Blocks', icon: <Workflow className="w-4 h-4" /> },
                  { id: 'sequential-chart', name: 'Sequential Function Chart', icon: <GitBranch className="w-4 h-4" /> },
                  { id: 'safety', name: 'Safety Programming', icon: <Shield className="w-4 h-4" /> },
                  { id: 'io-mapping', name: 'I/O Mapping', icon: <Cable className="w-4 h-4" /> },
                  { id: 'hmi', name: 'HMI Development', icon: <Monitor className="w-4 h-4" /> },
                  { id: 'network', name: 'Network Configuration', icon: <Network className="w-4 h-4" /> },
                  { id: 'simulation', name: 'Simulation', icon: <Zap className="w-4 h-4" /> },
                  { id: 'signal-tracing', name: 'Signal Tracing', icon: <Eye className="w-4 h-4" /> },
                  { id: 'plc-operations', name: 'PLC Operations', icon: <Cpu className="w-4 h-4" /> },
                  { id: 'ai-assistant', name: 'AI Assistant', icon: <Sparkles className="w-4 h-4" /> },
                  { id: 'collaboration', name: 'Collaboration', icon: <Users className="w-4 h-4" /> },
                  { id: 'enterprise', name: 'Enterprise Features', icon: <Shield className="w-4 h-4" /> },
                  { id: 'shortcuts', name: 'Keyboard Shortcuts', icon: <Command className="w-4 h-4" /> },
                  { id: 'troubleshooting', name: 'Troubleshooting', icon: <HelpCircle className="w-4 h-4" /> },
                ].map(item => (
                  <li key={item.id}>
                    <a 
                      href={`#${item.id}`}
                      className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm hover:bg-control-700 transition-colors ${
                        activeSection === item.id ? 'bg-blue-600/20 text-blue-400 border-l-2 border-blue-500' : 'text-white'
                      }`}
                      onClick={(e) => {
                        e.preventDefault();
                        handleSectionClick(item.id);
                      }}
                    >
                      <span className={activeSection === item.id ? 'text-blue-400' : 'text-control-400'}>
                        {item.icon}
                      </span>
                      <span>{item.name}</span>
                    </a>
                  </li>
                ))}
              </ul>
            </nav>
          </div>
        )}

        {/* Main Content */}
        {activeTab === 'guide' && renderGuideContent()}
        {activeTab === 'videos' && renderVideosContent()}
        {activeTab === 'troubleshooting' && renderTroubleshootingContent()}
      </div>
    </div>
  );
};

export default UserGuide;