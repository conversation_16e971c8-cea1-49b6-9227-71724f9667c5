import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  CheckCircle2, 
  XCircle, 
  Shield, 
  Mail, 
  Key, 
  User, 
  Clock, 
  Download, 
  Upload, 
  MoreHorizontal,
  X,
  Save,
  RefreshCw
} from 'lucide-react';

interface UserData {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'pending';
  lastActive?: Date;
  createdAt: Date;
  permissions: string[];
  organization?: string;
  licenseType?: string;
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<UserData[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserData[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    role: 'user',
    organization: '',
    licenseType: 'standard'
  });

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const mockUsers: UserData[] = [
        {
          id: '1',
          name: 'John Engineer',
          email: '<EMAIL>',
          role: 'engineer',
          status: 'active',
          lastActive: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
          createdAt: new Date(2023, 5, 15),
          permissions: ['project.read', 'project.write', 'program.write'],
          organization: 'Manufacturing Inc.',
          licenseType: 'professional'
        },
        {
          id: '2',
          name: 'Sarah Admin',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          lastActive: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
          createdAt: new Date(2023, 3, 10),
          permissions: ['project.read', 'project.write', 'project.delete', 'user.manage', 'system.admin'],
          organization: 'Manufacturing Inc.',
          licenseType: 'enterprise'
        },
        {
          id: '3',
          name: 'Mike Safety',
          email: '<EMAIL>',
          role: 'safety_engineer',
          status: 'active',
          lastActive: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
          createdAt: new Date(2023, 7, 22),
          permissions: ['project.read', 'project.write', 'safety.manage'],
          organization: 'Manufacturing Inc.',
          licenseType: 'professional'
        },
        {
          id: '4',
          name: 'Lisa Operator',
          email: '<EMAIL>',
          role: 'operator',
          status: 'active',
          lastActive: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
          createdAt: new Date(2023, 9, 5),
          permissions: ['project.read', 'deployment.execute'],
          organization: 'Manufacturing Inc.',
          licenseType: 'standard'
        },
        {
          id: '5',
          name: 'David Manager',
          email: '<EMAIL>',
          role: 'manager',
          status: 'active',
          lastActive: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
          createdAt: new Date(2023, 4, 18),
          permissions: ['project.read', 'project.write', 'user.view', 'reports.view'],
          organization: 'Manufacturing Inc.',
          licenseType: 'professional'
        },
        {
          id: '6',
          name: 'Emma Viewer',
          email: '<EMAIL>',
          role: 'viewer',
          status: 'inactive',
          createdAt: new Date(2023, 10, 12),
          permissions: ['project.read'],
          organization: 'Client Corp',
          licenseType: 'basic'
        },
        {
          id: '7',
          name: 'Robert Pending',
          email: '<EMAIL>',
          role: 'engineer',
          status: 'pending',
          createdAt: new Date(2024, 0, 5),
          permissions: ['project.read', 'project.write'],
          organization: 'New Client LLC',
          licenseType: 'professional'
        }
      ];
      
      setUsers(mockUsers);
      setFilteredUsers(mockUsers);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter users based on search and filters
  useEffect(() => {
    let result = users;
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(user => 
        user.name.toLowerCase().includes(query) || 
        user.email.toLowerCase().includes(query) ||
        user.organization?.toLowerCase().includes(query)
      );
    }
    
    if (filterRole !== 'all') {
      result = result.filter(user => user.role === filterRole);
    }
    
    if (filterStatus !== 'all') {
      result = result.filter(user => user.status === filterStatus);
    }
    
    setFilteredUsers(result);
  }, [users, searchQuery, filterRole, filterStatus]);

  const handleAddUser = () => {
    if (!newUser.name || !newUser.email) return;
    
    const user: UserData = {
      id: (users.length + 1).toString(),
      name: newUser.name,
      email: newUser.email,
      role: newUser.role,
      status: 'pending',
      createdAt: new Date(),
      permissions: getDefaultPermissions(newUser.role),
      organization: newUser.organization,
      licenseType: newUser.licenseType
    };
    
    setUsers([...users, user]);
    setShowAddUserModal(false);
    setNewUser({
      name: '',
      email: '',
      role: 'user',
      organization: '',
      licenseType: 'standard'
    });
  };

  const handleDeleteUser = () => {
    if (!selectedUser) return;
    
    setUsers(users.filter(user => user.id !== selectedUser.id));
    setSelectedUser(null);
    setShowDeleteConfirmation(false);
  };

  const getDefaultPermissions = (role: string): string[] => {
    switch (role) {
      case 'admin':
        return ['project.read', 'project.write', 'project.delete', 'user.manage', 'system.admin'];
      case 'engineer':
        return ['project.read', 'project.write', 'program.write'];
      case 'safety_engineer':
        return ['project.read', 'project.write', 'safety.manage'];
      case 'manager':
        return ['project.read', 'project.write', 'user.view', 'reports.view'];
      case 'operator':
        return ['project.read', 'deployment.execute'];
      case 'viewer':
        return ['project.read'];
      default:
        return ['project.read'];
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'engineer':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'safety_engineer':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'manager':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'operator':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'viewer':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'inactive':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const formatLastActive = (date?: Date) => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 30) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with search and filters */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">User Management</h2>
          <button
            onClick={() => setShowAddUserModal(true)}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Add User</span>
          </button>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div className="col-span-3 sm:col-span-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Roles</option>
              <option value="admin">Admin</option>
              <option value="engineer">Engineer</option>
              <option value="safety_engineer">Safety Engineer</option>
              <option value="manager">Manager</option>
              <option value="operator">Operator</option>
              <option value="viewer">Viewer</option>
            </select>
          </div>
          
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="pending">Pending</option>
            </select>
          </div>
        </div>
      </div>

      {/* User List */}
      <div className="flex-1 bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex items-center space-x-2">
              <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />
              <span className="text-white">Loading users...</span>
            </div>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <Users className="w-16 h-16 text-gray-600 mb-4" />
            <h3 className="text-lg font-medium text-white">No users found</h3>
            <p className="text-gray-400 mt-2">Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Role</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Last Active</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Organization</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">License</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {filteredUsers.map(user => (
                  <tr 
                    key={user.id} 
                    className="hover:bg-gray-700/50 cursor-pointer"
                    onClick={() => setSelectedUser(user)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-semibold">
                          {user.name.charAt(0)}
                        </div>
                        <div>
                          <div className="text-white font-medium">{user.name}</div>
                          <div className="text-gray-400 text-sm">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 rounded-full text-xs border ${getRoleBadgeColor(user.role)}`}>
                        {user.role.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 rounded-full text-xs border ${getStatusBadgeColor(user.status)}`}>
                        {user.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-gray-300 text-sm">
                      {formatLastActive(user.lastActive)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-gray-300 text-sm">
                      {user.organization || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-gray-300 text-sm">
                      {user.licenseType || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedUser(user);
                            // Open edit modal in a real implementation
                          }}
                          className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedUser(user);
                            setShowDeleteConfirmation(true);
                          }}
                          className="p-1 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* User Details Sidebar - Show when a user is selected */}
      {selectedUser && (
        <div className="fixed inset-y-0 right-0 w-96 bg-gray-800 border-l border-gray-700 p-6 overflow-y-auto shadow-xl z-10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">User Details</h3>
            <button
              onClick={() => setSelectedUser(null)}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="flex flex-col items-center mb-6">
            <div className="w-20 h-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-2xl font-semibold mb-3">
              {selectedUser.name.charAt(0)}
            </div>
            <h4 className="text-xl font-semibold text-white">{selectedUser.name}</h4>
            <p className="text-gray-400">{selectedUser.email}</p>
          </div>
          
          <div className="space-y-6">
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">Account Information</h5>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Role</span>
                  <span className={`px-2 py-1 rounded-full text-xs border ${getRoleBadgeColor(selectedUser.role)}`}>
                    {selectedUser.role.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Status</span>
                  <span className={`px-2 py-1 rounded-full text-xs border ${getStatusBadgeColor(selectedUser.status)}`}>
                    {selectedUser.status}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Created</span>
                  <span className="text-white">{selectedUser.createdAt.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Last Active</span>
                  <span className="text-white">{formatLastActive(selectedUser.lastActive)}</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">Organization</h5>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Company</span>
                  <span className="text-white">{selectedUser.organization || '-'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">License Type</span>
                  <span className="text-white capitalize">{selectedUser.licenseType || '-'}</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">Permissions</h5>
              <div className="space-y-2">
                {selectedUser.permissions.map((permission, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <CheckCircle2 className="w-4 h-4 text-green-400" />
                    <span className="text-white">{permission}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  // Open edit modal in a real implementation
                }}
                className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>Edit User</span>
              </button>
              <button
                onClick={() => setShowDeleteConfirmation(true)}
                className="flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add User Modal */}
      {showAddUserModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Add New User</h3>
              <button
                onClick={() => setShowAddUserModal(false)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Full Name</label>
                <input
                  type="text"
                  value={newUser.name}
                  onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter full name"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Email Address</label>
                <input
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter email address"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Role</label>
                <select
                  value={newUser.role}
                  onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="admin">Admin</option>
                  <option value="engineer">Engineer</option>
                  <option value="safety_engineer">Safety Engineer</option>
                  <option value="manager">Manager</option>
                  <option value="operator">Operator</option>
                  <option value="viewer">Viewer</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Organization</label>
                <input
                  type="text"
                  value={newUser.organization}
                  onChange={(e) => setNewUser({...newUser, organization: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter organization name"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">License Type</label>
                <select
                  value={newUser.licenseType}
                  onChange={(e) => setNewUser({...newUser, licenseType: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="basic">Basic</option>
                  <option value="standard">Standard</option>
                  <option value="professional">Professional</option>
                  <option value="enterprise">Enterprise</option>
                </select>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddUserModal(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleAddUser}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                disabled={!newUser.name || !newUser.email}
              >
                Add User
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && selectedUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center space-x-3 text-red-400 mb-4">
              <Shield className="w-6 h-6" />
              <h3 className="text-xl font-semibold">Confirm Deletion</h3>
            </div>
            
            <p className="text-white mb-2">Are you sure you want to delete this user?</p>
            <p className="text-gray-400 mb-6">This action cannot be undone. The user will lose all access to the system.</p>
            
            <div className="bg-gray-700/50 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-semibold">
                  {selectedUser.name.charAt(0)}
                </div>
                <div>
                  <div className="text-white font-medium">{selectedUser.name}</div>
                  <div className="text-gray-400 text-sm">{selectedUser.email}</div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirmation(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteUser}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Delete User
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;