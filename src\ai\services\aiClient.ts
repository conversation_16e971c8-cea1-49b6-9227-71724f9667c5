import { AIRequest, AIResponse } from '../hooks/useAI';
import { promptTemplates } from './promptTemplates';
import { CompletionContext } from '../hooks/useAICompletion';
import { useMCPContext } from '../context/MCPStore';
import { mcpService } from './mcpService';

class AIClient {
  private baseUrl = '/api/ai'; // Backend AI service endpoint
  private apiKey = import.meta.env.VITE_OPENAI_API_KEY;

  async request(request: AIRequest): Promise<AIResponse> {
    // Get appropriate prompt template
    const template = promptTemplates.getTemplate(request.type, request.context);
    
    // Get MCP context if available
    let mcpContext = '';
    try {
      // This is a bit of a hack since we can't use hooks directly in a class
      // In a real implementation, this would be passed in from the component
      const mcpStore = useMCPContext();
      if (mcpStore && mcpStore.serializeForPrompt) {
        mcpContext = mcpStore.serializeForPrompt();
      }
    } catch (error) {
      console.warn('MCP context not available, continuing without it');
    }
    
    // Try to enhance the prompt using the MCP service
    let enhancedPrompt = '';
    try {
      const enhancementResponse = await mcpService.enhancePrompt({
        prompt: request.prompt || '',
        contextTypes: ['program', 'tag', 'safety', 'standard']
      });
      
      enhancedPrompt = enhancementResponse.enhancedPrompt;
    } catch (error) {
      console.warn('Failed to enhance prompt with MCP service, using fallback method');
      
      // Fallback to manual combination
      enhancedPrompt = [
        template,
        mcpContext ? `\n\n# Context Information\n${mcpContext}` : '',
        request.prompt ? `\n\nUser Request: ${request.prompt}` : ''
      ].join('');
    }

    try {
      // In production, this would call your backend AI service
      // For now, we'll simulate the response
      const response = await this.simulateAIResponse(request, enhancedPrompt);
      
      // Calculate confidence based on context quality
      let contextConfidence = 0.7; // Default
      try {
        const mcpStore = useMCPContext();
        if (mcpStore && mcpStore.getContextConfidence) {
          contextConfidence = mcpStore.getContextConfidence();
        }
      } catch (error) {
        // Continue with default confidence
      }
      
      // Adjust response confidence based on context quality
      const adjustedConfidence = (response.confidence + contextConfidence) / 2;
      
      return {
        id: `ai_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: response.content,
        confidence: adjustedConfidence,
        suggestions: response.suggestions,
        timestamp: new Date(),
        prompt: request.prompt,
        timeTaken: response.timeTaken || 1.5
      };
    } catch (error) {
      console.error('AI request failed:', error);
      throw new Error('Failed to get AI response');
    }
  }

  private async simulateAIResponse(request: AIRequest, prompt: string) {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    // Handle different request types
    switch (request.type) {
      case 'explain':
        return this.generateExplanation(request.context);
      case 'refactor':
        return this.generateRefactoring(request.context);
      case 'generate':
        return this.generateCode(request.context);
      case 'suggest':
        return this.generateSuggestions(request.context);
      case 'debug':
        return this.generateDebugHelp(request.context);
      case 'predict_next':
        return this.generateCompletion(request.context);
      default:
        throw new Error('Unknown request type');
    }
  }

  private generateCompletion(context: any): { content: string; confidence: number; suggestions: string[]; timeTaken?: number } {
    // Simulate AI completion based on context
    const mode = context.mode || 'ladder';
    
    if (mode === 'ladder') {
      return this.generateLadderCompletion(context);
    } else if (mode === 'st') {
      return this.generateSTCompletion(context);
    }
    
    return {
      content: 'No suggestion available',
      confidence: 0.5,
      suggestions: [],
      timeTaken: 0.8
    };
  }

  private generateLadderCompletion(context: any): { content: string; confidence: number; suggestions: string[]; timeTaken?: number } {
    // Simulate ladder logic completions
    const ladderSuggestions = [
      {
        content: 'Add Coil element with tag "Pump_Run"',
        confidence: 0.92,
        suggestions: [
          'Add Coil element with tag "Motor_Output"',
          'Add Timer (TON) with preset T#5s',
          'Add Normally Closed contact with tag "Fault_Status"'
        ],
        timeTaken: 0.7
      },
      {
        content: 'Add Normally Closed contact with tag "Emergency_Stop"',
        confidence: 0.88,
        suggestions: [
          'Add Normally Open contact with tag "Start_Button"',
          'Add Normally Closed contact with tag "Overload_Fault"',
          'Add Coil element with tag "Motor_Run"'
        ],
        timeTaken: 0.8
      },
      {
        content: 'Add Timer (TON) with preset T#3s and tag "Start_Delay"',
        confidence: 0.85,
        suggestions: [
          'Add Timer (TOF) with preset T#5s',
          'Add Counter (CTU) with preset 10',
          'Add Compare function with operator ">"'
        ],
        timeTaken: 0.9
      },
      {
        content: 'Create safety interlock rung with Emergency_Stop and Guard_Door',
        confidence: 0.82,
        suggestions: [
          'Create motor start sequence with Start_PB and Run_Feedback',
          'Create alarm monitoring rung with multiple fault conditions',
          'Create pump control sequence with level sensors'
        ],
        timeTaken: 1.1
      }
    ];
    
    return ladderSuggestions[Math.floor(Math.random() * ladderSuggestions.length)];
  }

  private generateSTCompletion(context: any): { content: string; confidence: number; suggestions: string[]; timeTaken?: number } {
    // Simulate structured text completions
    const stSuggestions = [
      {
        content: 'Pump_Run := Start_Button AND NOT Emergency_Stop;',
        confidence: 0.94,
        suggestions: [
          'Motor_Output := Pump_Run AND NOT Fault_Status;',
          'IF Start_Button AND NOT Emergency_Stop THEN',
          'Pump_Run := Start_Button AND Safety_OK;'
        ],
        timeTaken: 0.6
      },
      {
        content: 'IF Temperature > High_Limit THEN',
        confidence: 0.89,
        suggestions: [
          'ELSIF Temperature < Low_Limit THEN',
          'CASE Temperature OF',
          'FOR i := 0 TO 10 BY 1 DO'
        ],
        timeTaken: 0.7
      },
      {
        content: 'Timer1(IN := Start_Signal, PT := T#5s);',
        confidence: 0.87,
        suggestions: [
          'Counter1(CU := Count_Signal, R := Reset, PV := 10);',
          'Timer1.IN := Start_Signal;',
          'Timer1.PT := T#5s;'
        ],
        timeTaken: 0.8
      },
      {
        content: 'END_IF;',
        confidence: 0.95,
        suggestions: [
          'END_CASE;',
          'END_FOR;',
          'END_WHILE;'
        ],
        timeTaken: 0.5
      }
    ];
    
    return stSuggestions[Math.floor(Math.random() * stSuggestions.length)];
  }

  private generateExplanation(context: any) {
    // Implementation unchanged
    const explanations = {
      ladder: `This ladder logic implements a motor control sequence with safety interlocks:

1. **Start Condition**: The motor starts when the Start_Button is pressed AND the Emergency_Stop is not active
2. **Seal-in Circuit**: Once started, the motor maintains operation through the auxiliary contact
3. **Safety Override**: The Emergency_Stop immediately stops the motor regardless of other conditions
4. **Output**: The Motor_Run coil energizes the motor contactor

**Safety Considerations**: This follows standard 3-wire control practices with proper emergency stop integration.`,
      
      st: `This Structured Text code implements a PID temperature controller:

1. **Error Calculation**: Computes the difference between setpoint and process variable
2. **PID Terms**: 
   - Proportional: Immediate response to current error
   - Integral: Eliminates steady-state error over time
   - Derivative: Dampens oscillations and improves stability
3. **Output Limiting**: Constrains output to 0-100% range
4. **Manual Mode**: Allows operator override when needed

**Best Practices**: Uses proper variable naming and includes safety limits.`
    };

    return {
      content: explanations[context.language as keyof typeof explanations] || 'Code explanation generated.',
      confidence: 0.85,
      suggestions: [
        'Add more detailed comments',
        'Consider adding error handling',
        'Implement input validation'
      ],
      timeTaken: 1.2
    };
  }

  private generateRefactoring(context: any) {
    // Implementation unchanged
    const refactorings = {
      ladder: `**Suggested Improvements:**

1. **Add Diagnostic Feedback**:
   - Include motor running feedback contact
   - Add fault detection logic

2. **Enhance Safety**:
   - Use safety-rated emergency stop
   - Add motor overload protection
   - Implement lockout/tagout capability

3. **Improve Maintainability**:
   - Group related logic in function blocks
   - Add descriptive comments
   - Use consistent naming conventions

**Code Quality**: Consider using a timer for motor start delay to prevent rapid cycling.`,

      st: `**Optimization Opportunities:**

1. **Performance**:
   - Use REAL data type for better precision
   - Implement anti-windup for integral term
   - Add derivative filtering

2. **Safety & Reliability**:
   - Add input range checking
   - Implement bumpless transfer
   - Add controller health monitoring

3. **Maintainability**:
   - Extract PID logic into function block
   - Add parameter validation
   - Implement auto-tuning capability`
    };

    return {
      content: refactorings[context.language as keyof typeof refactorings] || 'Refactoring suggestions generated.',
      confidence: 0.78,
      suggestions: [
        'Implement suggested safety improvements',
        'Add diagnostic capabilities',
        'Create reusable function blocks'
      ],
      timeTaken: 1.5
    };
  }

  private generateCode(context: any) {
    // Implementation unchanged
    const codeExamples = {
      'motor control': {
        ladder: `// Motor Start/Stop with Safety
RUNG 0: Start_PB AND Motor_Run_FB AND /Emergency_Stop AND /Motor_OL -> Motor_Run
RUNG 1: Motor_Run -> Motor_Contactor
RUNG 2: Motor_Run AND Run_Timer.Q -> Motor_Running_Lamp`,
        
        st: `FUNCTION_BLOCK MotorControl_FB
VAR_INPUT
    Start_Command : BOOL;
    Stop_Command : BOOL;
    Emergency_Stop : BOOL;
    Motor_Feedback : BOOL;
END_VAR

VAR_OUTPUT
    Motor_Output : BOOL;
    Running_Status : BOOL;
    Fault_Status : BOOL;
END_VAR

VAR
    Motor_Memory : BOOL;
    Start_Timer : TON;
END_VAR

// Start logic with safety checks
IF Start_Command AND NOT Emergency_Stop AND NOT Fault_Status THEN
    Motor_Memory := TRUE;
ELSIF Stop_Command OR Emergency_Stop THEN
    Motor_Memory := FALSE;
END_IF;

// Output assignment
Motor_Output := Motor_Memory;
Running_Status := Motor_Memory AND Motor_Feedback;

END_FUNCTION_BLOCK`
      },
      
      'safety logic': {
        ladder: `// Safety Light Curtain with Muting
RUNG 0: Light_Curtain_OK OR (Muting_Sensor_1 AND Muting_Sensor_2 AND Muting_Enable) -> Safety_OK
RUNG 1: Safety_OK AND Enable_Production -> Machine_Enable
RUNG 2: /Safety_OK -> Safety_Fault_Alarm`,
        
        st: `FUNCTION_BLOCK SafetyLightCurtain_FB
VAR_INPUT
    Light_Curtain_OK : BOOL;
    Muting_Sensor_1 : BOOL;
    Muting_Sensor_2 : BOOL;
    Muting_Enable : BOOL;
    Reset_Button : BOOL;
END_VAR

VAR_OUTPUT
    Safety_Output : BOOL;
    Muting_Active : BOOL;
    Fault_Present : BOOL;
END_VAR

VAR
    Muting_Timer : TON;
    Fault_Memory : BOOL;
END_VAR

// Muting logic
Muting_Timer(IN := (Muting_Sensor_1 AND Muting_Sensor_2 AND Muting_Enable), PT := T#30S);
Muting_Active := Muting_Timer.Q;

// Safety output
Safety_Output := (Light_Curtain_OK OR Muting_Active) AND NOT Fault_Present;

// Fault handling
IF NOT Light_Curtain_OK AND NOT Muting_Active THEN
    Fault_Memory := TRUE;
END_IF;

IF Reset_Button AND Light_Curtain_OK THEN
    Fault_Memory := FALSE;
END_IF;

Fault_Present := Fault_Memory;

END_FUNCTION_BLOCK`
      }
    };

    const description = context.description.toLowerCase();
    let selectedExample = codeExamples['motor control'];
    
    if (description.includes('safety') || description.includes('light curtain')) {
      selectedExample = codeExamples['safety logic'];
    }

    return {
      content: selectedExample[context.language as keyof typeof selectedExample] || 'Generated code example.',
      confidence: 0.82,
      suggestions: [
        'Customize for your specific requirements',
        'Add appropriate safety certifications',
        'Test thoroughly before deployment'
      ],
      timeTaken: 2.1
    };
  }

  private generateSuggestions(context: any) {
    // Implementation unchanged
    const description = context.description.toLowerCase();
    let suggestions = [];

    if (description.includes('motor')) {
      suggestions = [
        { name: 'Motor_Start_PB', type: 'BOOL', description: 'Motor start pushbutton input' },
        { name: 'Motor_Stop_PB', type: 'BOOL', description: 'Motor stop pushbutton input' },
        { name: 'Motor_Run_Output', type: 'BOOL', description: 'Motor contactor output' },
        { name: 'Motor_Running_FB', type: 'BOOL', description: 'Motor running feedback' },
        { name: 'Motor_Fault', type: 'BOOL', description: 'Motor fault status' }
      ];
    } else if (description.includes('temperature')) {
      suggestions = [
        { name: 'Temp_Setpoint', type: 'REAL', description: 'Temperature setpoint value' },
        { name: 'Temp_Process_Value', type: 'REAL', description: 'Current temperature reading' },
        { name: 'Heater_Output', type: 'REAL', description: 'Heater control output (0-100%)' },
        { name: 'Temp_Alarm_High', type: 'BOOL', description: 'High temperature alarm' },
        { name: 'PID_Enable', type: 'BOOL', description: 'PID controller enable' }
      ];
    } else {
      suggestions = [
        { name: 'System_Enable', type: 'BOOL', description: 'System enable input' },
        { name: 'Process_Running', type: 'BOOL', description: 'Process running status' },
        { name: 'Fault_Reset', type: 'BOOL', description: 'Fault reset button' },
        { name: 'Emergency_Stop', type: 'BOOL', description: 'Emergency stop input' }
      ];
    }

    return {
      content: `Suggested tags for "${context.description}":

${suggestions.map(tag => `• **${tag.name}** (${tag.type}): ${tag.description}`).join('\n')}

These tags follow IEC 61131-3 naming conventions and include appropriate data types for the application.`,
      confidence: 0.88,
      suggestions: suggestions.map(tag => tag.name),
      timeTaken: 0.9
    };
  }

  private generateDebugHelp(context: any) {
    // Implementation unchanged
    return {
      content: `**Debug Analysis:**

Based on your ladder logic, here are potential issues and solutions:

1. **Signal Flow Check**:
   - Verify all input contacts are properly connected
   - Check for missing power rails or broken connections
   - Ensure output coils have proper addressing

2. **Logic Verification**:
   - Confirm normally open/closed contact configurations
   - Check for conflicting logic conditions
   - Verify timer and counter preset values

3. **I/O Status**:
   - Validate physical input wiring
   - Check output module status
   - Verify tag assignments match I/O configuration

4. **Common Issues**:
   - Missing seal-in circuits for maintained operations
   - Improper emergency stop integration
   - Inadequate safety interlocks

**Recommended Actions**:
- Use signal tracing to follow logic execution
- Monitor tag values in real-time
- Test with simulation before deploying to hardware`,
      confidence: 0.75,
      suggestions: [
        'Enable signal tracing',
        'Check I/O mapping',
        'Verify safety circuits',
        'Test in simulation mode'
      ],
      timeTaken: 1.8
    };
  }
}

export const aiClient = new AIClient();