import { AIRequest, AIResponse } from '../hooks/useAI';

class AIClient {
  private baseUrl = import.meta.env.VITE_AI_BACKEND_URL || 'http://localhost:3001/api/ai';
  private enableBackend = import.meta.env.VITE_ENABLE_AI_BACKEND !== 'false'; // Enabled by default

  async request(request: AIRequest): Promise<AIResponse> {
    // Try the backend first if enabled
    if (this.enableBackend) {
      try {
        const response = await fetch(`${this.baseUrl}/request`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.getAuthToken()}`
          },
          body: JSON.stringify({
            ...request,
            model: request.model || 'llama3' // Default to Ollama model
          })
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error('AI Backend request failed:', response.statusText, errorData);

          // Fallback to simulation on backend error
          console.log('Falling back to simulation mode...');
          return this.simulateResponse(request);
        }

        const data = await response.json();

        if (data.success && data.data) {
          console.log('✅ AI Backend response received');
          return data.data;
        } else {
          throw new Error(data.error || 'Unknown error from AI backend');
        }
      } catch (error) {
        console.error('AI Client error:', error);

        // Fallback to simulation
        console.log('🔄 Falling back to simulation mode...');
        return this.simulateResponse(request);
      }
    } else {
      // Backend disabled, use simulation
      return this.simulateResponse(request);
    }
  }

  async stream(request: AIRequest): Promise<ReadableStream> {
    if (this.enableBackend) {
      try {
        const response = await fetch(`${this.baseUrl}/stream`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.getAuthToken()}`
          },
          body: JSON.stringify({
            ...request,
            model: request.model || 'llama3'
          })
        });

        if (!response.ok || !response.body) {
          console.log('Streaming failed, falling back to simulation');
          return this.simulateStream(request);
        }

        return response.body;
      } catch (error) {
        console.error('Streaming error:', error);
        return this.simulateStream(request);
      }
    } else {
      return this.simulateStream(request);
    }
  }

  private async simulateResponse(request: AIRequest): Promise<AIResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const plcResponseTemplates = {
      ladder: `[SIMULATION] Ladder Logic Response:

Here's a basic motor start/stop ladder logic for your request:

|-[Start]-[/Stop]-[M1_Running]----------( M1_Running )-|
|                                                        |
|-[/E_Stop]-[M1_Running]----------------[Timer_Delay]---|
|                                                        |
|-[Timer_Delay/DN]----------------------( Motor_Output )-|

Components:
- Start: Momentary start button (NO contact)
- Stop: Momentary stop button (NC contact)
- E_Stop: Emergency stop (NC contact)
- M1_Running: Motor running memory bit
- Timer_Delay: On-delay timer for motor protection
- Motor_Output: Physical motor output

This is a simulated response. Connect the backend to get real AI assistance.`,

      structured_text: `[SIMULATION] Structured Text Response:

Here's a basic motor control in Structured Text:

PROGRAM Motor_Control
VAR
    Start_Button : BOOL;
    Stop_Button : BOOL;
    Motor_Running : BOOL;
    Motor_Output : BOOL;
    Safety_OK : BOOL;
END_VAR

(* Motor control logic *)
IF Start_Button AND NOT Stop_Button AND Safety_OK THEN
    Motor_Running := TRUE;
END_IF;

IF Stop_Button OR NOT Safety_OK THEN
    Motor_Running := FALSE;
END_IF;

Motor_Output := Motor_Running;

END_PROGRAM

This is a simulated response. Connect to Ollama for real AI assistance.`,

      function_block: `[SIMULATION] Function Block Response:

Here's a basic motor control function block:

FUNCTION_BLOCK FB_MotorControl
VAR_INPUT
    Start : BOOL;
    Stop : BOOL;
    Reset : BOOL;
END_VAR

VAR_OUTPUT
    Running : BOOL;
    Fault : BOOL;
END_VAR

VAR
    Internal_Run : BOOL;
END_VAR

(* Control logic *)
IF Start AND NOT Stop THEN
    Internal_Run := TRUE;
END_IF;

IF Stop OR Fault THEN
    Internal_Run := FALSE;
END_IF;

Running := Internal_Run;

END_FUNCTION_BLOCK

This is a simulated response. Enable the AI backend for real assistance.`
    };

    // Determine response type based on request
    let responseType = 'ladder';
    if (request.context?.type === 'structured_text' || request.prompt?.toLowerCase().includes('structured text')) {
      responseType = 'structured_text';
    } else if (request.context?.type === 'function_block' || request.prompt?.toLowerCase().includes('function block')) {
      responseType = 'function_block';
    }

    const templateResponse = plcResponseTemplates[responseType as keyof typeof plcResponseTemplates] || plcResponseTemplates.ladder;

    return {
      id: `sim_${Date.now()}`,
      content: templateResponse,
      confidence: 0.7,
      cached: false,
      timestamp: new Date(),
      model: request.model || 'simulation',
      usage: {
        inputTokens: request.prompt?.length || 0,
        outputTokens: templateResponse.length,
        totalTokens: (request.prompt?.length || 0) + templateResponse.length
      }
    };
  }

  private simulateStream(request: AIRequest): ReadableStream {
    const encoder = new TextEncoder();
    const response = `[SIMULATION STREAM] Processing "${request.prompt?.substring(0, 30)}..."\n\nGenerating PLC programming response...\n\nThis would be a real-time streaming response from Ollama in production mode.`;

    return new ReadableStream({
      start(controller) {
        let index = 0;
        const interval = setInterval(() => {
          if (index < response.length) {
            controller.enqueue(encoder.encode(response[index]));
            index++;
          } else {
            controller.close();
            clearInterval(interval);
          }
        }, 50);
      }
    });
  }

  private getAuthToken(): string {
    // For development with Ollama
    if (import.meta.env.NODE_ENV === 'development') {
      return 'dev-token';
    }

    // In production, get from your auth system
    return localStorage.getItem('authToken') || sessionStorage.getItem('authToken') || '';
  }

  // Test backend connectivity
  async testConnection(): Promise<{ backend: boolean; ollama: boolean }> {
    const results = { backend: false, ollama: false };

    try {
      const response = await fetch(`${this.baseUrl.replace('/api/ai', '')}/health`);
      results.backend = response.ok;
    } catch (error) {
      console.log('Backend not reachable:', error);
    }

    try {
      const response = await fetch('http://localhost:11434/api/tags');
      results.ollama = response.ok;
    } catch (error) {
      console.log('Ollama not reachable:', error);
    }

    return results;
  }
}

export const aiClient = new AIClient();