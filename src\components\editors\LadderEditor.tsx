import React, { useState, useCallback, useRef, useEffect } from 'react';
import { usePLCStore } from '../../store/plcStore';
import { LadderRung, LadderElement } from '../../types/plc';
import { 
  Plus, 
  Trash2, 
  Copy, 
  Move, 
  CheckCircle2, 
  AlertTriangle, 
  Info,
  X,
  Edit,
  Zap,
  Clock,
  Hash,
  Calculator,
  RotateCcw,
  GitBranch,
  Sparkles
} from 'lucide-react';
import { ladderValidator, ValidationResult, ValidationError } from './LadderValidation';
import { useAICompletion, CompletionContext } from '../../ai/hooks/useAICompletion';
import AIGhostBlock from '../../ai/components/AIGhostBlock';

const LadderEditor: React.FC = () => {
  const { currentProject, activeProgram, updateProgram } = usePLCStore();
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [draggedElement, setDraggedElement] = useState<string | null>(null);
  const [showValidation, setShowValidation] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [editingElement, setEditingElement] = useState<string | null>(null);
  const [editingTag, setEditingTag] = useState('');
  const [showAddRungModal, setShowAddRungModal] = useState(false);
  const [rungComment, setRungComment] = useState('');
  const [showExplanation, setShowExplanation] = useState(false);
  const [quickInputText, setQuickInputText] = useState('');
  const [showQuickInput, setShowQuickInput] = useState(false);
  const [activeRungId, setActiveRungId] = useState<string | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);

  // AI Completion integration
  const { 
    suggestion, 
    showSuggestion, 
    isLoading, 
    requestSuggestion, 
    acceptSuggestion, 
    dismissSuggestion,
    cycleAlternatives,
    alternatives,
    selectedAlternative
  } = useAICompletion();
  
  const [suggestionPosition, setSuggestionPosition] = useState({ x: 0, y: 0 });

  const program = currentProject?.programs.find(p => p.id === activeProgram);
  const rungs = (program?.content as LadderRung[]) || [];

  // Request AI suggestion when element is selected or rung is added
  useEffect(() => {
    if (!program || !selectedElement) return;
    
    const rung = rungs.find(r => r.elements.some(e => e.id === selectedElement));
    if (!rung) return;
    
    const element = rung.elements.find(e => e.id === selectedElement);
    if (!element) return;
    
    // Get element position for suggestion display
    const elementDom = document.getElementById(selectedElement);
    if (elementDom) {
      const rect = elementDom.getBoundingClientRect();
      const editorRect = editorRef.current?.getBoundingClientRect() || { left: 0, top: 0 };
      
      setSuggestionPosition({
        x: rect.right - editorRect.left + 20,
        y: rect.top - editorRect.top
      });
    }
    
    // Build context for AI suggestion
    const context: CompletionContext = {
      mode: 'ladder',
      programType: program.safetyProgram ? 'Safety' : 'Standard',
      previousBlocks: [element.tag || 'Unnamed'],
      scopeTags: currentProject?.globalTags.map(t => t.name) || [],
      rungComment: rung.comment,
      selectedElement: element
    };
    
    // Request suggestion with a small delay to avoid too many requests
    const timer = setTimeout(() => {
      requestSuggestion(context);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [selectedElement, program, rungs, currentProject, requestSuggestion]);

  const addRung = useCallback(() => {
    if (!program) return;
    
    setShowAddRungModal(true);
  }, [program]);

  const handleCreateRung = () => {
    if (!program) return;
    
    const newRung: LadderRung = {
      id: `rung-${Date.now()}`,
      number: rungs.length,
      elements: [],
      enabled: true,
      comment: rungComment
    };

    updateProgram(program.id, {
      content: [...rungs, newRung]
    });
    
    setShowAddRungModal(false);
    setRungComment('');
  };

  const deleteRung = useCallback((rungId: string) => {
    if (!program) return;
    
    const updatedRungs = rungs.filter(r => r.id !== rungId)
      .map((r, index) => ({ ...r, number: index }));
    
    updateProgram(program.id, {
      content: updatedRungs
    });
  }, [program, rungs, updateProgram]);

  const addElement = useCallback((rungId: string, elementType: LadderElement['type'], properties: Record<string, any> = {}) => {
    if (!program) return;
    
    const rung = rungs.find(r => r.id === rungId);
    if (!rung) return;
    
    const updatedRungs = rungs.map(r => {
      if (r.id === rungId) {
        // Determine position based on element type
        let position = { x: r.elements.length * 2 + 1, y: 0 };
        
        // For coils, always position at the far right
        if (elementType === 'coil') {
          position.x = 10; // Fixed position at the right side
        }
        
        const newElement: LadderElement = {
          id: `elem-${Date.now()}`,
          type: elementType,
          position,
          connections: [],
          properties: elementType === 'contact' 
            ? { normally: 'open', ...properties } 
            : properties
        };
        
        return {
          ...r,
          elements: [...r.elements, newElement]
        };
      }
      return r;
    });
    
    updateProgram(program.id, {
      content: updatedRungs
    });
  }, [program, rungs, updateProgram]);

  const updateElement = useCallback((rungId: string, elementId: string, updates: Partial<LadderElement>) => {
    if (!program) return;
    
    const updatedRungs = rungs.map(rung => {
      if (rung.id === rungId) {
        return {
          ...rung,
          elements: rung.elements.map(elem =>
            elem.id === elementId ? { ...elem, ...updates } : elem
          )
        };
      }
      return rung;
    });
    
    updateProgram(program.id, {
      content: updatedRungs
    });
  }, [program, rungs, updateProgram]);

  const deleteElement = useCallback((rungId: string, elementId: string) => {
    if (!program) return;
    
    const updatedRungs = rungs.map(rung => {
      if (rung.id === rungId) {
        return {
          ...rung,
          elements: rung.elements.filter(elem => elem.id !== elementId)
        };
      }
      return rung;
    });
    
    updateProgram(program.id, {
      content: updatedRungs
    });
  }, [program, rungs, updateProgram]);

  const addParallelBranch = useCallback((rungId: string, afterElementId: string) => {
    if (!program) return;
    
    const updatedRungs = rungs.map(rung => {
      if (rung.id === rungId) {
        const afterElement = rung.elements.find(e => e.id === afterElementId);
        if (!afterElement) return rung;

        const newElement: LadderElement = {
          id: `elem-${Date.now()}`,
          type: 'contact',
          position: { x: afterElement.position.x, y: afterElement.position.y + 1 },
          connections: [],
          properties: { normally: 'open' }
        };
        
        return {
          ...rung,
          elements: [...rung.elements, newElement]
        };
      }
      return rung;
    });
    
    updateProgram(program.id, {
      content: updatedRungs
    });
  }, [program, rungs, updateProgram]);

  const validateProgram = useCallback(() => {
    const result = ladderValidator.validateProgram(rungs);
    setValidationResult(result);
    setShowValidation(true);
  }, [rungs]);

  const handleElementClick = useCallback((elementId: string, rungId: string) => {
    setSelectedElement(elementId);
    setEditingElement(elementId);
    const element = rungs.find(r => r.id === rungId)?.elements.find(e => e.id === elementId);
    setEditingTag(element?.tag || '');
  }, [rungs]);

  const handleTagSave = useCallback(() => {
    if (editingElement && selectedElement) {
      const rung = rungs.find(r => r.elements.some(e => e.id === editingElement));
      if (rung) {
        updateElement(rung.id, editingElement, { tag: editingTag });
      }
    }
    setEditingElement(null);
    setEditingTag('');
  }, [editingElement, editingTag, rungs, updateElement]);

  const handleRungDoubleClick = useCallback((rungId: string, event: React.MouseEvent) => {
    setActiveRungId(rungId);
    setShowQuickInput(true);
    
    // Position the quick input near the click
    const rect = event.currentTarget.getBoundingClientRect();
    const editorRect = editorRef.current?.getBoundingClientRect() || { left: 0, top: 0 };
    
    setSuggestionPosition({
      x: event.clientX - editorRect.left,
      y: event.clientY - editorRect.top
    });
  }, []);

  const handleQuickInputSubmit = useCallback(() => {
    if (!activeRungId || !quickInputText.trim()) {
      setShowQuickInput(false);
      setQuickInputText('');
      return;
    }
    
    // Parse quick input text to create elements
    const elements = quickInputText.split(/\s+/);
    
    // Find the rung
    const rung = rungs.find(r => r.id === activeRungId);
    if (!rung) {
      setShowQuickInput(false);
      setQuickInputText('');
      return;
    }
    
    // Create elements
    const newElements: LadderElement[] = [];
    let xPosition = rung.elements.length * 2 + 1;
    
    elements.forEach((elem, index) => {
      const elementType = elem.toUpperCase();
      let type: LadderElement['type'] = 'contact';
      let properties: Record<string, any> = {};
      
      // Map text input to element types
      switch (elementType) {
        case 'NO':
          type = 'contact';
          properties = { normally: 'open' };
          break;
        case 'NC':
          type = 'contact';
          properties = { normally: 'closed' };
          break;
        case 'COIL':
          type = 'coil';
          xPosition = 10; // Position coil at the far right
          break;
        case 'TON':
          type = 'timer';
          properties = { timerType: 'TON', preset: 'T#5S' };
          break;
        case 'TOF':
          type = 'timer';
          properties = { timerType: 'TOF', preset: 'T#5S' };
          break;
        case 'CTU':
          type = 'counter';
          properties = { counterType: 'CTU', preset: 10 };
          break;
        case 'CTD':
          type = 'counter';
          properties = { counterType: 'CTD', preset: 10 };
          break;
        case 'CMP':
          type = 'function';
          properties = { functionType: 'compare', operator: '==' };
          break;
        case 'CALC':
          type = 'function';
          properties = { functionType: 'math', operator: '+' };
          break;
        default:
          type = 'contact';
          properties = { normally: 'open' };
      }
      
      // Create the element
      const newElement: LadderElement = {
        id: `elem-${Date.now()}-${index}`,
        type,
        position: { x: xPosition, y: 0 },
        connections: [],
        properties
      };
      
      newElements.push(newElement);
      
      // Only increment position for non-coil elements
      if (type !== 'coil') {
        xPosition += 2;
      }
    });
    
    // Update the rung with new elements
    const updatedRungs = rungs.map(r => {
      if (r.id === activeRungId) {
        return {
          ...r,
          elements: [...r.elements, ...newElements]
        };
      }
      return r;
    });
    
    updateProgram(program!.id, {
      content: updatedRungs
    });
    
    setShowQuickInput(false);
    setQuickInputText('');
  }, [activeRungId, quickInputText, rungs, program, updateProgram]);

  const handleAcceptSuggestion = useCallback(() => {
    const accepted = acceptSuggestion();
    if (!accepted) return;
    
    // Parse the suggestion content
    const content = accepted.content;
    
    if (content.includes('Add') && content.includes('element')) {
      // Extract element type and tag
      const match = content.match(/Add\s+(\w+)\s+element\s+with\s+tag\s+"([^"]+)"/i);
      if (match) {
        const [_, elementType, tagName] = match;
        
        // Find the rung of the selected element
        const selectedElementRung = rungs.find(r => 
          r.elements.some(e => e.id === selectedElement)
        );
        
        if (selectedElementRung) {
          // Determine element type
          let type: LadderElement['type'] = 'contact';
          let properties: Record<string, any> = {};
          
          switch (elementType.toLowerCase()) {
            case 'coil':
              type = 'coil';
              break;
            case 'contact':
            case 'normally':
              type = 'contact';
              properties = { normally: content.includes('Closed') ? 'closed' : 'open' };
              break;
            case 'timer':
              type = 'timer';
              properties = { 
                timerType: content.includes('TOF') ? 'TOF' : 'TON',
                preset: content.includes('preset') ? content.match(/T#\d+[sm]/i)?.[0] || 'T#5S' : 'T#5S'
              };
              break;
            case 'counter':
              type = 'counter';
              properties = { 
                counterType: content.includes('CTD') ? 'CTD' : 'CTU',
                preset: content.includes('preset') ? parseInt(content.match(/preset\s+(\d+)/i)?.[1] || '10') : 10
              };
              break;
            default:
              type = 'contact';
              properties = { normally: 'open' };
          }
          
          // Create new element
          const newElement: LadderElement = {
            id: `elem-${Date.now()}`,
            type,
            position: { 
              x: type === 'coil' ? 10 : selectedElementRung.elements.length * 2 + 1, 
              y: 0 
            },
            connections: [],
            properties,
            tag: tagName
          };
          
          // Add element to rung
          const updatedRungs = rungs.map(rung => {
            if (rung.id === selectedElementRung.id) {
              return {
                ...rung,
                elements: [...rung.elements, newElement]
              };
            }
            return rung;
          });
          
          updateProgram(program!.id, {
            content: updatedRungs
          });
        }
      }
    } else if (content.includes('Create') && content.includes('rung')) {
      // Create a new rung with the suggested elements
      const newRung: LadderRung = {
        id: `rung-${Date.now()}`,
        number: rungs.length,
        elements: [],
        enabled: true,
        comment: content.split(':')[0]
      };
      
      updateProgram(program!.id, {
        content: [...rungs, newRung]
      });
    }
  }, [acceptSuggestion, selectedElement, rungs, program, updateProgram]);

  // Function to render parallel branches correctly
  const renderParallelBranches = (rung: LadderRung) => {
    // Group elements by their y-position (row)
    const rowElements: { [key: number]: LadderElement[] } = {};
    
    rung.elements.forEach(element => {
      const y = element.position.y || 0;
      if (!rowElements[y]) {
        rowElements[y] = [];
      }
      rowElements[y].push(element);
    });
    
    // Sort rows by y-position
    const sortedRows = Object.keys(rowElements)
      .map(Number)
      .sort((a, b) => a - b);
    
    return (
      <div className="flex flex-col space-y-4">
        {sortedRows.map((rowY, rowIndex) => (
          <div key={rowY} className="relative flex items-center">
            {/* Vertical connection lines between rows */}
            {rowIndex > 0 && (
              <div 
                className="absolute left-4 w-px bg-blue-500" 
                style={{ 
                  top: '-24px', 
                  height: '24px'
                }}
              ></div>
            )}
            
            {/* Left Power Rail for each row */}
            <div className="w-4 h-px bg-blue-500"></div>
            
            {/* Elements in this row */}
            <div className="flex items-center">
              {rowElements[rowY]
                .sort((a, b) => a.position.x - b.position.x)
                .map(element => renderElement(element, rung.id))}
            </div>
            
            {/* Right Power Rail for each row */}
            <div className="flex-1 h-px bg-blue-500 ml-2"></div>
          </div>
        ))}
      </div>
    );
  };

  const renderElement = (element: LadderElement, rungId: string) => {
    const isSelected = selectedElement === element.id;
    const isEditing = editingElement === element.id;
    
    const getElementStyle = () => {
      switch (element.type) {
        case 'contact':
          return element.properties?.normally === 'closed' 
            ? 'border-2 border-red-400 text-red-400' 
            : 'border-2 border-green-400 text-green-400';
        case 'coil':
          return 'border-2 border-blue-400 text-blue-400 bg-blue-400/10';
        case 'timer':
          return 'border-2 border-yellow-400 text-yellow-400 bg-yellow-400/10';
        case 'counter':
          return 'border-2 border-purple-400 text-purple-400 bg-purple-400/10';
        case 'function':
          return 'border-2 border-orange-400 text-orange-400 bg-orange-400/10';
        default:
          return 'border-2 border-gray-400 text-gray-400';
      }
    };

    const getElementSymbol = () => {
      switch (element.type) {
        case 'contact':
          return element.properties?.normally === 'closed' ? '/]' : '][';
        case 'coil':
          return '( )';
        case 'timer':
          return element.properties?.timerType || 'TON';
        case 'counter':
          return element.properties?.counterType || 'CTU';
        case 'function':
          return element.properties?.functionType === 'compare' ? 'CMP' : 
                 element.properties?.functionType === 'math' ? 'CALC' : 'FUN';
        default:
          return '???';
      }
    };

    return (
      <div
        id={element.id}
        key={element.id}
        className={`relative inline-block mx-2 px-3 py-2 rounded cursor-pointer transition-all ${getElementStyle()} ${
          isSelected ? 'ring-2 ring-white/50' : ''
        }`}
        onClick={() => handleElementClick(element.id, rungId)}
        draggable
        onDragStart={() => setDraggedElement(element.id)}
        onDragEnd={() => setDraggedElement(null)}
        onContextMenu={(e) => {
          e.preventDefault();
          // Context menu for element operations
        }}
      >
        <div className="text-center">
          <div className="font-mono text-sm font-bold">{getElementSymbol()}</div>
          <div className="text-xs mt-1 truncate max-w-20">
            {element.tag || 'Unassigned'}
          </div>
        </div>
        
        {isEditing && (
          <div className="absolute -top-12 left-0 bg-gray-800 border border-gray-600 rounded px-2 py-1 text-xs whitespace-nowrap z-10">
            <input
              type="text"
              value={editingTag}
              onChange={(e) => setEditingTag(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter') handleTagSave();
                if (e.key === 'Escape') {
                  setEditingElement(null);
                  setEditingTag('');
                }
              }}
              onBlur={handleTagSave}
              placeholder="Tag name"
              className="bg-transparent text-white outline-none w-20"
              autoFocus
            />
          </div>
        )}

        {isSelected && (
          <div className="absolute -bottom-8 left-0 flex space-x-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                addParallelBranch(rungId, element.id);
              }}
              className="p-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs"
              title="Add Parallel Branch"
            >
              <GitBranch className="w-3 h-3" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                deleteElement(rungId, element.id);
              }}
              className="p-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs"
              title="Delete Element"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        )}
      </div>
    );
  };

  const renderRung = (rung: LadderRung) => (
    <div key={rung.id} className="bg-gray-900/50 rounded-lg border border-gray-800 mb-4">
      <div className="flex items-center justify-between p-2 border-b border-gray-800">
        <div className="flex items-center space-x-3">
          <span className="text-gray-500 text-sm font-mono w-12">
            {rung.number.toString().padStart(4, '0')}
          </span>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => addElement(rung.id, 'contact')}
              className="p-1 text-green-400 hover:bg-green-400/20 rounded transition-colors"
              title="Add Contact"
            >
              ][
            </button>
            <button
              onClick={() => addElement(rung.id, 'coil')}
              className="p-1 text-blue-400 hover:bg-blue-400/20 rounded transition-colors"
              title="Add Coil"
            >
              ( )
            </button>
            <button
              onClick={() => addElement(rung.id, 'timer')}
              className="p-1 text-yellow-400 hover:bg-yellow-400/20 rounded transition-colors"
              title="Add Timer"
            >
              <Clock className="w-4 h-4" />
            </button>
            <button
              onClick={() => addElement(rung.id, 'counter')}
              className="p-1 text-purple-400 hover:bg-purple-400/20 rounded transition-colors"
              title="Add Counter"
            >
              <Hash className="w-4 h-4" />
            </button>
            <button
              onClick={() => addElement(rung.id, 'function')}
              className="p-1 text-orange-400 hover:bg-orange-400/20 rounded transition-colors"
              title="Add Function"
            >
              <Calculator className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => deleteRung(rung.id)}
            className="p-1 text-red-400 hover:bg-red-400/20 rounded transition-colors"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div 
        className="p-4 min-h-16"
        onDoubleClick={(e) => handleRungDoubleClick(rung.id, e)}
      >
        {/* Render parallel branches if they exist, otherwise render normal row */}
        {rung.elements.some(e => e.position.y > 0) 
          ? renderParallelBranches(rung)
          : (
            <div className="flex items-center">
              {/* Left Power Rail */}
              <div className="w-4 h-px bg-blue-500"></div>
              
              {/* Elements */}
              <div className="flex items-center">
                {rung.elements
                  .sort((a, b) => a.position.x - b.position.x)
                  .map(element => renderElement(element, rung.id))}
              </div>
              
              {/* Right Power Rail */}
              <div className="flex-1 h-px bg-blue-500 ml-2"></div>
            </div>
          )
        }
        
        {rung.comment && (
          <div className="mt-2 text-xs text-gray-400 italic">
            {rung.comment}
          </div>
        )}
      </div>
    </div>
  );

  const renderValidationPanel = () => {
    if (!showValidation || !validationResult) return null;

    const renderValidationItem = (item: ValidationError) => {
      const getIcon = () => {
        switch (item.type) {
          case 'error': return <AlertTriangle className="w-4 h-4 text-red-400" />;
          case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
          case 'info': return <Info className="w-4 h-4 text-blue-400" />;
        }
      };

      const getColor = () => {
        switch (item.type) {
          case 'error': return 'border-red-400/30 bg-red-400/10';
          case 'warning': return 'border-yellow-400/30 bg-yellow-400/10';
          case 'info': return 'border-blue-400/30 bg-blue-400/10';
        }
      };

      return (
        <div key={item.id} className={`p-3 rounded border ${getColor()}`}>
          <div className="flex items-start space-x-2">
            {getIcon()}
            <div className="flex-1">
              <div className="text-white text-sm">{item.message}</div>
              {item.suggestion && (
                <div className="text-gray-400 text-xs mt-1">{item.suggestion}</div>
              )}
              {item.rungId && (
                <div className="text-gray-500 text-xs mt-1">
                  Rung {rungs.find(r => r.id === item.rungId)?.number}
                </div>
              )}
            </div>
          </div>
        </div>
      );
    };

    return (
      <div className="fixed inset-y-0 right-0 w-96 bg-gray-800 border-l border-gray-700 z-50 overflow-y-auto">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-white">Validation Results</h3>
            <button
              onClick={() => setShowValidation(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className="text-sm text-gray-400 mt-1">
            {ladderValidator.getValidationSummary(validationResult)}
          </div>
        </div>
        
        <div className="p-4 space-y-3">
          {validationResult.errors.map(renderValidationItem)}
          {validationResult.warnings.map(renderValidationItem)}
          {validationResult.info.map(renderValidationItem)}
          
          {validationResult.errors.length === 0 && 
           validationResult.warnings.length === 0 && 
           validationResult.info.length === 0 && (
            <div className="text-center py-8 text-green-400">
              <CheckCircle2 className="w-12 h-12 mx-auto mb-2" />
              <p>No validation issues found!</p>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderAddRungModal = () => {
    if (!showAddRungModal) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md">
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Add New Rung</h3>
            <button
              onClick={() => setShowAddRungModal(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Rung Comment (Optional)</label>
              <textarea
                value={rungComment}
                onChange={(e) => setRungComment(e.target.value)}
                placeholder="Enter a comment for this rung"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white h-20 resize-none"
              />
            </div>
            
            <div className="pt-4 border-t border-gray-700 flex justify-end">
              <button
                onClick={() => setShowAddRungModal(false)}
                className="text-gray-400 hover:text-white px-4 py-2 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateRung}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
              >
                Add Rung
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderQuickInputModal = () => {
    if (!showQuickInput) return null;
    
    return (
      <div 
        className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center"
        onClick={() => setShowQuickInput(false)}
      >
        <div 
          className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md p-4"
          onClick={e => e.stopPropagation()}
        >
          <h3 className="text-white font-medium mb-3">Quick Element Input</h3>
          <p className="text-sm text-gray-400 mb-3">
            Enter element types separated by spaces: NO, NC, COIL, TON, TOF, CTU, CTD, CMP, CALC
          </p>
          <input
            type="text"
            value={quickInputText}
            onChange={(e) => setQuickInputText(e.target.value)}
            placeholder="Example: NO NC COIL"
            className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white mb-4"
            autoFocus
            onKeyPress={(e) => {
              if (e.key === 'Enter') handleQuickInputSubmit();
            }}
          />
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setShowQuickInput(false)}
              className="px-3 py-1 text-gray-400 hover:text-white"
            >
              Cancel
            </button>
            <button
              onClick={handleQuickInputSubmit}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded"
            >
              Add Elements
            </button>
          </div>
        </div>
      </div>
    );
  };

  if (!program) {
    return (
      <div className="h-full flex items-center justify-center text-gray-400">
        <div className="text-center">
          <div className="text-lg mb-2">No Program Selected</div>
          <div className="text-sm">Select a program from the project navigator</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-950 overflow-auto relative" ref={editorRef}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">{program.name}</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={validateProgram}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors"
            >
              <CheckCircle2 className="w-4 h-4" />
              <span>Validate</span>
            </button>
            <button
              onClick={addRung}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add Rung</span>
            </button>
          </div>
        </div>
        
        <div className="space-y-4">
          {rungs.length === 0 ? (
            <div className="text-center py-12 text-gray-400">
              <div className="text-lg mb-2">Empty Program</div>
              <div className="text-sm mb-4">Start by adding your first rung</div>
              <button
                onClick={addRung}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded transition-colors"
              >
                Add First Rung
              </button>
            </div>
          ) : (
            rungs.map(renderRung)
          )}
        </div>
      </div>

      {renderValidationPanel()}
      {renderAddRungModal()}
      {renderQuickInputModal()}
      
      {/* AI Suggestion Ghost Block */}
      {showSuggestion && suggestion && (
        <AIGhostBlock
          suggestion={suggestion}
          position={suggestionPosition}
          onAccept={handleAcceptSuggestion}
          onDismiss={dismissSuggestion}
          onCycleAlternative={cycleAlternatives}
          mode="ladder"
          alternativeIndex={selectedAlternative}
          alternativesCount={alternatives.length}
          showExplanation={showExplanation}
          onToggleExplanation={() => setShowExplanation(!showExplanation)}
        />
      )}
    </div>
  );
};

export default LadderEditor;