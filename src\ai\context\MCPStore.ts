import { create } from 'zustand';

export type ContextItemType = 'tag' | 'program' | 'rung' | 'safety' | 'doc' | 'version' | 'standard';

export interface ContextItem {
  id: string;
  type: ContextItemType;
  source: string; // 'auto' | 'user' | 'system'
  value: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface MCPState {
  contextItems: ContextItem[];
  addContextItem: (item: ContextItem) => void;
  removeContextItem: (id: string) => void;
  clearContext: () => void;
  getContextByType: (type: ContextItemType) => ContextItem[];
  serializeForPrompt: () => string;
  getContextConfidence: () => number;
}

export const useMCPStore = create<MCPState>((set, get) => ({
  contextItems: [],
  
  addContextItem: (item) => 
    set((state) => {
      // Check if item with same ID already exists
      const exists = state.contextItems.some(i => i.id === item.id);
      if (exists) {
        // Update existing item
        return {
          contextItems: state.contextItems.map(i => 
            i.id === item.id ? { ...item, timestamp: new Date().toISOString() } : i
          )
        };
      }
      // Add new item
      return { 
        contextItems: [...state.contextItems, {
          ...item,
          timestamp: item.timestamp || new Date().toISOString()
        }]
      };
    }),
  
  removeContextItem: (id) =>
    set((state) => ({
      contextItems: state.contextItems.filter(item => item.id !== id)
    })),
  
  clearContext: () => set({ contextItems: [] }),
  
  getContextByType: (type) => {
    const { contextItems } = get();
    return contextItems.filter(item => item.type === type);
  },
  
  serializeForPrompt: () => {
    const { contextItems } = get();
    
    // Group items by type for better organization
    const groupedItems: Record<string, ContextItem[]> = {};
    
    contextItems.forEach(item => {
      if (!groupedItems[item.type]) {
        groupedItems[item.type] = [];
      }
      groupedItems[item.type].push(item);
    });
    
    // Build the prompt with sections for each type
    let prompt = '';
    
    // Add standards and versions first as they're global context
    if (groupedItems['standard']) {
      prompt += '### Standards\n';
      groupedItems['standard'].forEach(item => {
        prompt += `- ${item.value}\n`;
      });
      prompt += '\n';
    }
    
    if (groupedItems['version']) {
      prompt += '### Versions\n';
      groupedItems['version'].forEach(item => {
        prompt += `- ${item.value}\n`;
      });
      prompt += '\n';
    }
    
    // Add safety context next as it's critical
    if (groupedItems['safety']) {
      prompt += '### Safety Context\n';
      groupedItems['safety'].forEach(item => {
        prompt += `- ${item.value}\n`;
      });
      prompt += '\n';
    }
    
    // Add program context
    if (groupedItems['program']) {
      prompt += '### Program Context\n';
      groupedItems['program'].forEach(item => {
        prompt += `- ${item.value}\n`;
      });
      prompt += '\n';
    }
    
    // Add rung context
    if (groupedItems['rung']) {
      prompt += '### Rung Context\n';
      groupedItems['rung'].forEach(item => {
        prompt += `- ${item.value}\n`;
      });
      prompt += '\n';
    }
    
    // Add tag context
    if (groupedItems['tag']) {
      prompt += '### Tags\n';
      groupedItems['tag'].forEach(item => {
        prompt += `- ${item.value}\n`;
      });
      prompt += '\n';
    }
    
    // Add documentation context
    if (groupedItems['doc']) {
      prompt += '### Documentation\n';
      groupedItems['doc'].forEach(item => {
        prompt += `- ${item.value}\n`;
      });
      prompt += '\n';
    }
    
    return prompt;
  },
  
  getContextConfidence: () => {
    const { contextItems } = get();
    
    // Calculate confidence based on context completeness
    // This is a simplified model - in production, you'd have a more sophisticated algorithm
    
    // Define weights for different context types
    const weights = {
      program: 0.3,
      tag: 0.2,
      rung: 0.2,
      safety: 0.15,
      standard: 0.1,
      version: 0.05
    };
    
    // Check which context types are present
    const presentTypes = new Set(contextItems.map(item => item.type));
    
    // Calculate confidence score
    let confidence = 0;
    let totalWeight = 0;
    
    Object.entries(weights).forEach(([type, weight]) => {
      totalWeight += weight;
      if (presentTypes.has(type as ContextItemType)) {
        confidence += weight;
      }
    });
    
    // Normalize to 0-1 range
    return totalWeight > 0 ? confidence / totalWeight : 0;
  }
}));

export function useMCPContext() {
  const contextItems = useMCPStore((s) => s.contextItems);
  const addContextItem = useMCPStore((s) => s.addContextItem);
  const removeContextItem = useMCPStore((s) => s.removeContextItem);
  const clearContext = useMCPStore((s) => s.clearContext);
  const getContextByType = useMCPStore((s) => s.getContextByType);
  const serializeForPrompt = useMCPStore((s) => s.serializeForPrompt);
  const getContextConfidence = useMCPStore((s) => s.getContextConfidence);
  
  return { 
    contextItems, 
    addContextItem, 
    removeContextItem, 
    clearContext, 
    getContextByType,
    serializeForPrompt,
    getContextConfidence
  };
}