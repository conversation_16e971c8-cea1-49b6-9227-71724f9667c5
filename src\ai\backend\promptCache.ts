import crypto from 'crypto';

interface CacheEntry {
  response: string;
  confidence: number;
  timestamp: Date;
  hitCount: number;
  metadata: any;
}

class PromptCache {
  private cache: Map<string, CacheEntry> = new Map();
  private maxSize = 1000;
  private defaultTTL = 3600; // 1 hour in seconds

  async getCachedResponse(prompt: string, context: any): Promise<CacheEntry | null> {
    const hash = this.hashPrompt(prompt, context);
    const entry = this.cache.get(hash);

    if (!entry) return null;

    // Check if entry has expired
    const age = (Date.now() - entry.timestamp.getTime()) / 1000;
    if (age > this.defaultTTL) {
      this.cache.delete(hash);
      return null;
    }

    // Increment hit count
    entry.hitCount++;
    return entry;
  }

  async cacheResponse(
    prompt: string, 
    context: any, 
    response: string, 
    confidence: number,
    metadata: any = {},
    ttl: number = this.defaultTTL
  ): Promise<void> {
    const hash = this.hashPrompt(prompt, context);
    
    // Ensure cache doesn't exceed max size
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }

    const entry: CacheEntry = {
      response,
      confidence,
      timestamp: new Date(),
      hitCount: 0,
      metadata: { ...metadata, ttl }
    };

    this.cache.set(hash, entry);
  }

  async invalidatePattern(pattern: string): Promise<number> {
    let invalidated = 0;
    for (const [hash, entry] of this.cache.entries()) {
      if (entry.response.includes(pattern) || 
          JSON.stringify(entry.metadata).includes(pattern)) {
        this.cache.delete(hash);
        invalidated++;
      }
    }
    return invalidated;
  }

  async clear(): Promise<void> {
    this.cache.clear();
  }

  getStats(): {
    size: number;
    hitRate: number;
    totalHits: number;
    averageAge: number;
  } {
    const entries = Array.from(this.cache.values());
    const totalHits = entries.reduce((sum, entry) => sum + entry.hitCount, 0);
    const totalRequests = entries.length + totalHits;
    const hitRate = totalRequests > 0 ? totalHits / totalRequests : 0;
    
    const now = Date.now();
    const averageAge = entries.length > 0 
      ? entries.reduce((sum, entry) => sum + (now - entry.timestamp.getTime()), 0) / entries.length / 1000
      : 0;

    return {
      size: this.cache.size,
      hitRate,
      totalHits,
      averageAge
    };
  }

  private hashPrompt(prompt: string, context: any): string {
    const data = JSON.stringify({ prompt, context });
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private evictOldest(): void {
    // Find the oldest entry (least recently used)
    let oldestHash = '';
    let oldestTime = Date.now();

    for (const [hash, entry] of this.cache.entries()) {
      const lastUsed = entry.timestamp.getTime() + (entry.hitCount * 1000); // Factor in usage
      if (lastUsed < oldestTime) {
        oldestTime = lastUsed;
        oldestHash = hash;
      }
    }

    if (oldestHash) {
      this.cache.delete(oldestHash);
    }
  }

  // Cleanup expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [hash, entry] of this.cache.entries()) {
      const age = (now - entry.timestamp.getTime()) / 1000;
      const ttl = entry.metadata.ttl || this.defaultTTL;
      if (age > ttl) {
        this.cache.delete(hash);
      }
    }
  }
}

export const promptCache = new PromptCache();

// Cleanup expired entries every 10 minutes
setInterval(() => {
  promptCache.cleanup();
}, 10 * 60 * 1000);