import Redis from 'ioredis';
import crypto from 'crypto';

export class PromptCache {
  private redis: Redis;
  private ttl = 3600; // 1 hour

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
  }

  async getCachedResponse(prompt: string, context: any) {
    const key = this.generateKey(prompt, context);
    const cached = await this.redis.get(key);

    if (cached) {
      const parsed = JSON.parse(cached);
      await this.redis.incr(`${key}:hits`);
      return parsed;
    }

    return null;
  }

  async cacheResponse(prompt: string, context: any, response: string, confidence: number, metadata: any = {}) {
    const key = this.generateKey(prompt, context);
    const cacheData = {
      response,
      confidence,
      metadata,
      timestamp: new Date().toISOString()
    };

    await this.redis.setex(key, this.ttl, JSON.stringify(cacheData));
    await this.redis.setex(`${key}:hits`, this.ttl, '0');
  }

  private generateKey(prompt: string, context: any): string {
    const combined = prompt + JSON.stringify(context);
    return `prompt_cache:${crypto.createHash('sha256').update(combined).digest('hex')}`;
  }

  async getStats() {
    // Implement stats retrieval logic here
    return {};
  }

  async clear() {
    // Implement cache clearing logic here
    await this.redis.flushdb();
  }
}

export const promptCache = new PromptCache();