import React, { useState, useEffect } from 'react';
import { useMCPContext } from '../context/MCPStore';
import { 
  Sparkles, 
  Edit, 
  Save, 
  X, 
  Check, 
  Copy, 
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';

interface PromptBuilderProps {
  userPrompt: string;
  onPromptChange?: (prompt: string) => void;
  onSubmit?: (finalPrompt: string) => void;
  readOnly?: boolean;
  className?: string;
}

const PromptBuilder: React.FC<PromptBuilderProps> = ({
  userPrompt,
  onPromptChange,
  onSubmit,
  readOnly = false,
  className = ''
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedPrompt, setEditedPrompt] = useState(userPrompt);
  const [showContextPreview, setShowContextPreview] = useState(false);
  
  const { serializeForPrompt, getContextConfidence } = useMCPContext();
  
  // Update edited prompt when userPrompt changes
  useEffect(() => {
    setEditedPrompt(userPrompt);
  }, [userPrompt]);
  
  const contextPrompt = serializeForPrompt();
  const contextConfidence = getContextConfidence();
  
  const handleSave = () => {
    if (onPromptChange) {
      onPromptChange(editedPrompt);
    }
    setIsEditing(false);
  };
  
  const handleCancel = () => {
    setEditedPrompt(userPrompt);
    setIsEditing(false);
  };
  
  const handleSubmit = () => {
    if (onSubmit) {
      // Combine context with user prompt
      const finalPrompt = `${contextPrompt}\n\nUser Request: ${editedPrompt}`;
      onSubmit(finalPrompt);
    }
  };
  
  const copyToClipboard = () => {
    const finalPrompt = `${contextPrompt}\n\nUser Request: ${userPrompt}`;
    navigator.clipboard.writeText(finalPrompt);
    alert('Prompt copied to clipboard');
  };
  
  return (
    <div className={`bg-gray-800 rounded-lg border border-gray-700 overflow-hidden ${className}`}>
      <div className="p-4 border-b border-gray-700 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Sparkles className="w-4 h-4 text-purple-400" />
          <h3 className="text-white font-medium">AI Prompt Builder</h3>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowContextPreview(!showContextPreview)}
            className={`p-1 rounded transition-colors ${
              showContextPreview ? 'text-blue-400 bg-blue-400/10' : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
            title={showContextPreview ? 'Hide Context' : 'Show Context'}
          >
            {showContextPreview ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          </button>
          {!readOnly && (
            <button
              onClick={() => setIsEditing(true)}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Edit Prompt"
            >
              <Edit className="w-4 h-4" />
            </button>
          )}
          <button
            onClick={copyToClipboard}
            className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title="Copy Full Prompt"
          >
            <Copy className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div className="p-4">
        {/* Context Preview */}
        {showContextPreview && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm text-white font-medium">Context Information</h4>
              <div className="flex items-center space-x-1 text-xs">
                <span className={`${
                  contextConfidence >= 0.8 ? 'text-green-400' :
                  contextConfidence >= 0.5 ? 'text-yellow-400' :
                  'text-red-400'
                }`}>
                  {Math.round(contextConfidence * 100)}% complete
                </span>
                <RefreshCw className="w-3 h-3 text-gray-400" />
              </div>
            </div>
            <div className="bg-gray-900 rounded p-3 max-h-40 overflow-y-auto">
              <pre className="text-xs text-gray-300 whitespace-pre-wrap">{contextPrompt || 'No context available'}</pre>
            </div>
          </div>
        )}
        
        {/* User Prompt */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm text-white font-medium">Your Request</h4>
            {isEditing && (
              <div className="flex items-center space-x-1">
                <button
                  onClick={handleSave}
                  className="p-1 text-green-400 hover:bg-green-400/20 rounded transition-colors"
                  title="Save"
                >
                  <Check className="w-3 h-3" />
                </button>
                <button
                  onClick={handleCancel}
                  className="p-1 text-red-400 hover:bg-red-400/20 rounded transition-colors"
                  title="Cancel"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
          
          {isEditing ? (
            <textarea
              value={editedPrompt}
              onChange={(e) => setEditedPrompt(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded p-3 text-white text-sm resize-none h-24 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your request to the AI assistant..."
            />
          ) : (
            <div className="bg-gray-700 rounded p-3 min-h-[6rem]">
              <p className="text-sm text-white">{userPrompt || 'No prompt specified'}</p>
            </div>
          )}
        </div>
        
        {/* Submit Button */}
        {!readOnly && (
          <button
            onClick={handleSubmit}
            className="w-full mt-4 bg-purple-600 hover:bg-purple-700 text-white py-2 rounded flex items-center justify-center space-x-2"
            disabled={!userPrompt.trim()}
          >
            <Sparkles className="w-4 h-4" />
            <span>Generate with AI</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default PromptBuilder;