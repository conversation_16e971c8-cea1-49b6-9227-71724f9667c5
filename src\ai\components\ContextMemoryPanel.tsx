import React, { useState, useEffect } from 'react';
import { useMCPContext, ContextItem, ContextItemType } from '../context/MCPStore';
import { usePLCStore } from '../../store/plcStore';
import { 
  Tag, 
  FileText, 
  Code, 
  Shield, 
  Clock, 
  Trash2, 
  Plus, 
  Info, 
  RefreshCw,
  CheckCircle2,
  AlertTriangle,
  HelpCircle
} from 'lucide-react';

const ContextMemoryPanel: React.FC = () => {
  const { 
    contextItems, 
    addContextItem, 
    removeContextItem, 
    clearContext,
    getContextConfidence
  } = useMCPContext();
  
  const { currentProject, activeProgram } = usePLCStore();
  const [activeTab, setActiveTab] = useState<ContextItemType>('program');
  const [showHelp, setShowHelp] = useState(false);
  
  // Auto-populate context from current project
  useEffect(() => {
    if (currentProject && activeProgram) {
      const program = currentProject.programs.find(p => p.id === activeProgram);
      
      if (program) {
        // Add program context
        addContextItem({
          id: `program-${program.id}`,
          type: 'program',
          source: 'auto',
          value: `Program: ${program.name}, Type: ${program.type}`,
          timestamp: new Date().toISOString(),
          metadata: {
            programId: program.id,
            programName: program.name,
            programType: program.type
          }
        });
        
        // Add safety context if applicable
        if (program.safetyProgram) {
          addContextItem({
            id: `safety-${program.id}`,
            type: 'safety',
            source: 'auto',
            value: `Safety Program: ${program.name}, SIL Rating: ${program.silRating || 'Not specified'}`,
            timestamp: new Date().toISOString(),
            metadata: {
              programId: program.id,
              silRating: program.silRating
            }
          });
        }
        
        // Add standard context
        addContextItem({
          id: 'standard-iec61131',
          type: 'standard',
          source: 'auto',
          value: 'IEC 61131-3 Standard for PLC Programming',
          timestamp: new Date().toISOString()
        });
      }
    }
  }, [currentProject, activeProgram, addContextItem]);
  
  // Add relevant tags to context
  useEffect(() => {
    if (currentProject && activeProgram) {
      // Clear existing tag context
      contextItems
        .filter(item => item.type === 'tag' && item.source === 'auto')
        .forEach(item => removeContextItem(item.id));
      
      // Add global tags (limit to 10 most relevant)
      currentProject.globalTags.slice(0, 10).forEach(tag => {
        addContextItem({
          id: `tag-${tag.id}`,
          type: 'tag',
          source: 'auto',
          value: `${tag.name} (${tag.type}): ${tag.description || 'No description'}`,
          timestamp: new Date().toISOString(),
          metadata: {
            tagId: tag.id,
            tagName: tag.name,
            tagType: tag.type,
            tagScope: tag.scope
          }
        });
      });
    }
  }, [currentProject, activeProgram, addContextItem, removeContextItem, contextItems]);
  
  const getTypeIcon = (type: ContextItemType) => {
    switch (type) {
      case 'tag': return <Tag className="w-4 h-4" />;
      case 'program': return <FileText className="w-4 h-4" />;
      case 'rung': return <Code className="w-4 h-4" />;
      case 'safety': return <Shield className="w-4 h-4" />;
      case 'doc': return <FileText className="w-4 h-4" />;
      case 'version': return <Clock className="w-4 h-4" />;
      case 'standard': return <Info className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };
  
  const getTypeColor = (type: ContextItemType) => {
    switch (type) {
      case 'tag': return 'text-blue-400';
      case 'program': return 'text-green-400';
      case 'rung': return 'text-yellow-400';
      case 'safety': return 'text-red-400';
      case 'doc': return 'text-purple-400';
      case 'version': return 'text-gray-400';
      case 'standard': return 'text-teal-400';
      default: return 'text-gray-400';
    }
  };
  
  const getSourceBadge = (source: string) => {
    if (source === 'auto') {
      return <span className="text-xs bg-blue-500/20 text-blue-400 px-1.5 py-0.5 rounded">Auto</span>;
    }
    if (source === 'user') {
      return <span className="text-xs bg-purple-500/20 text-purple-400 px-1.5 py-0.5 rounded">User</span>;
    }
    return null;
  };
  
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-400';
    if (confidence >= 0.5) return 'text-yellow-400';
    return 'text-red-400';
  };
  
  const getConfidenceIcon = (confidence: number) => {
    if (confidence >= 0.8) return <CheckCircle2 className="w-4 h-4" />;
    if (confidence >= 0.5) return <AlertTriangle className="w-4 h-4" />;
    return <AlertTriangle className="w-4 h-4" />;
  };
  
  const confidence = getContextConfidence();
  
  const filteredItems = contextItems.filter(item => 
    activeTab === 'all' || item.type === activeTab
  );
  
  const handleAddCustomContext = () => {
    const type = prompt('Enter context type (tag, program, rung, safety, doc, version, standard):') as ContextItemType;
    if (!type || !['tag', 'program', 'rung', 'safety', 'doc', 'version', 'standard'].includes(type)) {
      alert('Invalid context type');
      return;
    }
    
    const value = prompt('Enter context value:');
    if (!value) return;
    
    addContextItem({
      id: `custom-${Date.now()}`,
      type,
      source: 'user',
      value,
      timestamp: new Date().toISOString()
    });
  };
  
  const handleRefreshContext = () => {
    // Clear auto-generated context
    contextItems
      .filter(item => item.source === 'auto')
      .forEach(item => removeContextItem(item.id));
      
    // Re-trigger the effects to regenerate context
    const program = currentProject?.programs.find(p => p.id === activeProgram);
    if (program) {
      addContextItem({
        id: `program-${program.id}`,
        type: 'program',
        source: 'auto',
        value: `Program: ${program.name}, Type: ${program.type}`,
        timestamp: new Date().toISOString(),
        metadata: {
          programId: program.id,
          programName: program.name,
          programType: program.type
        }
      });
    }
  };
  
  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-white font-semibold">Context Memory</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefreshContext}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Refresh Context"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowHelp(!showHelp)}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Help"
            >
              <HelpCircle className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {/* Confidence Meter */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-400">Context Confidence</span>
            <div className={`flex items-center space-x-1 ${getConfidenceColor(confidence)}`}>
              {getConfidenceIcon(confidence)}
              <span>{Math.round(confidence * 100)}%</span>
            </div>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all ${
                confidence >= 0.8 ? 'bg-green-500' :
                confidence >= 0.5 ? 'bg-yellow-500' :
                'bg-red-500'
              }`}
              style={{ width: `${confidence * 100}%` }}
            ></div>
          </div>
        </div>
        
        {/* Help Panel */}
        {showHelp && (
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3 mb-4">
            <h4 className="text-blue-400 font-medium mb-2">About Context Memory</h4>
            <p className="text-sm text-gray-300 mb-2">
              Context memory helps the AI assistant understand your project better. It collects relevant information about your programs, tags, and safety requirements.
            </p>
            <ul className="text-xs text-gray-400 space-y-1 list-disc pl-4">
              <li>Higher context confidence leads to better AI responses</li>
              <li>Auto-collected context is updated as you work</li>
              <li>You can add custom context items manually</li>
              <li>Context is used to generate AI prompts</li>
            </ul>
          </div>
        )}
        
        {/* Tabs */}
        <div className="flex space-x-1 overflow-x-auto">
          {(['all', 'program', 'tag', 'rung', 'safety', 'standard'] as const).map(type => (
            <button
              key={type}
              onClick={() => setActiveTab(type)}
              className={`px-3 py-1 text-xs rounded-lg transition-colors ${
                activeTab === type
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </button>
          ))}
        </div>
      </div>
      
      {/* Context Items */}
      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {filteredItems.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Info className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No {activeTab === 'all' ? 'context' : activeTab} items available</p>
            <button
              onClick={handleAddCustomContext}
              className="mt-2 text-blue-400 hover:text-blue-300 text-sm"
            >
              + Add custom context
            </button>
          </div>
        ) : (
          filteredItems.map(item => (
            <div 
              key={item.id} 
              className="bg-gray-800 rounded-lg p-3 border border-gray-700 hover:border-blue-500 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className={`mt-1 ${getTypeColor(item.type)}`}>
                    {getTypeIcon(item.type)}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <span className="text-white text-sm">{item.type.charAt(0).toUpperCase() + item.type.slice(1)}</span>
                      {getSourceBadge(item.source)}
                    </div>
                    <p className="text-gray-300 text-sm mt-1">{item.value}</p>
                    <div className="text-xs text-gray-500 mt-1">
                      Added {new Date(item.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => removeContextItem(item.id)}
                  className="p-1 text-gray-500 hover:text-red-400 hover:bg-gray-700 rounded transition-colors"
                  title="Remove from context"
                >
                  <Trash2 className="w-3 h-3" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>
      
      {/* Footer */}
      <div className="bg-gray-800 border-t border-gray-700 p-3 flex items-center justify-between">
        <div className="text-xs text-gray-400">
          {contextItems.length} context items
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleAddCustomContext}
            className="flex items-center space-x-1 text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded transition-colors"
          >
            <Plus className="w-3 h-3" />
            <span>Add</span>
          </button>
          <button
            onClick={clearContext}
            className="flex items-center space-x-1 text-xs bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded transition-colors"
          >
            <Trash2 className="w-3 h-3" />
            <span>Clear All</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContextMemoryPanel;