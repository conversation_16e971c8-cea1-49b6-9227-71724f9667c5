import { createClient } from '@supabase/supabase-js';

export interface FeedbackEntry {
  id: string;
  userId: string;
  suggestionId: string;
  rating: 'thumbs_up' | 'thumbs_down';
  category: 'accuracy' | 'safety' | 'performance' | 'usability';
  comment?: string;
  timestamp: Date;
  context: {
    model: string;
    feature: string;
    confidence: number;
    promptLength: number;
    responseLength: number;
  };
}

export interface FeedbackAnalytics {
  totalFeedback: number;
  positiveRating: number;
  negativeRating: number;
  averageRating: number;
  categoryBreakdown: Record<string, { positive: number; negative: number }>;
  modelPerformance: Record<string, { positive: number; negative: number }>;
  featurePerformance: Record<string, { positive: number; negative: number }>;
  commonIssues: string[];
  improvementSuggestions: string[];
}

class FeedbackCollector {
  private supabase;
  private tableName = 'feedback';

  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL || '',
      process.env.SUPABASE_ANON_KEY || ''
    );
  }

  async submitFeedback(feedback: Omit<FeedbackEntry, 'id' | 'timestamp'>): Promise<string> {
    const entry: FeedbackEntry = {
      ...feedback,
      id: `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    const { error } = await this.supabase
      .from(this.tableName)
      .insert([entry]);

    if (error) {
      console.error('Failed to submit feedback to Supabase:', error);
      throw new Error('Failed to submit feedback');
    }

    return entry.id;
  }

  async getFeedbackAnalytics(
    startDate?: Date,
    endDate?: Date,
    userId?: string
  ): Promise<FeedbackAnalytics> {
    let query = this.supabase
      .from(this.tableName)
      .select('*');

    if (startDate) {
      query = query.gte('timestamp', startDate.toISOString());
    }
    if (endDate) {
      query = query.lte('timestamp', endDate.toISOString());
    }
    if (userId) {
      query = query.eq('userId', userId);
    }

    const { data: feedbackEntries, error } = await query;

    if (error) {
      console.error('Failed to get feedback from Supabase:', error);
      throw new Error('Failed to get feedback');
    }

    const totalFeedback = feedbackEntries.length;
    const positiveRating = feedbackEntries.filter((f: FeedbackEntry) => f.rating === 'thumbs_up').length;
    const negativeRating = feedbackEntries.filter((f: FeedbackEntry) => f.rating === 'thumbs_down').length;
    const averageRating = totalFeedback > 0 ? positiveRating / totalFeedback : 0;

    // Category breakdown
    const categoryBreakdown: Record<string, { positive: number; negative: number }> = {};
    const modelPerformance: Record<string, { positive: number; negative: number }> = {};
    const featurePerformance: Record<string, { positive: number; negative: number }> = {};

    feedbackEntries.forEach((entry: FeedbackEntry) => {
      // Category breakdown
      if (!categoryBreakdown[entry.category]) {
        categoryBreakdown[entry.category] = { positive: 0, negative: 0 };
      }
      if (entry.rating === 'thumbs_up') {
        categoryBreakdown[entry.category].positive++;
      } else {
        categoryBreakdown[entry.category].negative++;
      }

      // Model performance
      if (!modelPerformance[entry.context.model]) {
        modelPerformance[entry.context.model] = { positive: 0, negative: 0 };
      }
      if (entry.rating === 'thumbs_up') {
        modelPerformance[entry.context.model].positive++;
      } else {
        modelPerformance[entry.context.model].negative++;
      }

      // Feature performance
      if (!featurePerformance[entry.context.feature]) {
        featurePerformance[entry.context.feature] = { positive: 0, negative: 0 };
      }
      if (entry.rating === 'thumbs_up') {
        featurePerformance[entry.context.feature].positive++;
      } else {
        featurePerformance[entry.context.feature].negative++;
      }
    });

    // Extract common issues and suggestions from comments
    const negativeComments = feedbackEntries
      .filter((f: FeedbackEntry) => f.rating === 'thumbs_down' && f.comment)
      .map((f: FeedbackEntry) => f.comment!);

    const commonIssues = this.extractCommonThemes(negativeComments);
    const improvementSuggestions = this.generateImprovementSuggestions(feedbackEntries);

    return {
      totalFeedback,
      positiveRating,
      negativeRating,
      averageRating,
      categoryBreakdown,
      modelPerformance,
      featurePerformance,
      commonIssues,
      improvementSuggestions
    };
  }

  async exportFeedback(startDate?: Date, endDate?: Date): Promise<string> {
    let query = this.supabase
      .from(this.tableName)
      .select('*');

    if (startDate) {
      query = query.gte('timestamp', startDate.toISOString());
    }
    if (endDate) {
      query = query.lte('timestamp', endDate.toISOString());
    }

    const { data: feedbackEntries, error } = await query;

    if (error) {
      console.error('Failed to get feedback from Supabase:', error);
      throw new Error('Failed to get feedback');
    }

    const exportData = {
      exportDate: new Date().toISOString(),
      totalEntries: feedbackEntries.length,
      feedback: feedbackEntries.map(entry => ({
        ...entry,
        timestamp: entry.timestamp.toISOString()
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  private extractCommonThemes(comments: string[]): string[] {
    // Simple keyword extraction for common issues
    const keywords = ['safety', 'incorrect', 'slow', 'confusing', 'error', 'bug', 'wrong'];
    const themes: Record<string, number> = {};

    comments.forEach(comment => {
      const lowerComment = comment.toLowerCase();
      keywords.forEach(keyword => {
        if (lowerComment.includes(keyword)) {
          themes[keyword] = (themes[keyword] || 0) + 1;
        }
      });
    });

    return Object.entries(themes)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([theme]) => theme);
  }

  private generateImprovementSuggestions(feedback: FeedbackEntry[]): string[] {
    const suggestions: string[] = [];

    // Analyze patterns in negative feedback
    const negativeFeedback = feedback.filter(f => f.rating === 'thumbs_down');

    if (negativeFeedback.length > 0) {
      const lowConfidenceIssues = negativeFeedback.filter(f => f.context.confidence < 0.7);
      if (lowConfidenceIssues.length > negativeFeedback.length * 0.3) {
        suggestions.push('Improve confidence scoring algorithm');
      }

      const safetyIssues = negativeFeedback.filter(f => f.category === 'safety');
      if (safetyIssues.length > 0) {
        suggestions.push('Enhance safety validation rules');
      }

      const accuracyIssues = negativeFeedback.filter(f => f.category === 'accuracy');
      if (accuracyIssues.length > negativeFeedback.length * 0.4) {
        suggestions.push('Retrain model with more domain-specific data');
      }
    }

    return suggestions;
  }

  private async openIndexedDB(): Promise<IDBDatabase> {
    return null as any;
  }
}

export const feedbackCollector = new FeedbackCollector();