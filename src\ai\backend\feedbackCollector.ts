export interface FeedbackEntry {
  id: string;
  userId: string;
  suggestionId: string;
  rating: 'thumbs_up' | 'thumbs_down';
  category: 'accuracy' | 'safety' | 'performance' | 'usability';
  comment?: string;
  timestamp: Date;
  context: {
    model: string;
    feature: string;
    confidence: number;
    promptLength: number;
    responseLength: number;
  };
}

export interface FeedbackAnalytics {
  totalFeedback: number;
  positiveRating: number;
  negativeRating: number;
  averageRating: number;
  categoryBreakdown: Record<string, { positive: number; negative: number }>;
  modelPerformance: Record<string, { positive: number; negative: number }>;
  featurePerformance: Record<string, { positive: number; negative: number }>;
  commonIssues: string[];
  improvementSuggestions: string[];
}

class FeedbackCollector {
  private feedback: Map<string, FeedbackEntry> = new Map();

  async submitFeedback(feedback: Omit<FeedbackEntry, 'id' | 'timestamp'>): Promise<string> {
    const entry: FeedbackEntry = {
      ...feedback,
      id: `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    this.feedback.set(entry.id, entry);
    
    // In production, persist to database
    await this.persistFeedback(entry);
    
    return entry.id;
  }

  async getFeedbackAnalytics(
    startDate?: Date,
    endDate?: Date,
    userId?: string
  ): Promise<FeedbackAnalytics> {
    let feedbackEntries = Array.from(this.feedback.values());

    // Filter by date range
    if (startDate) {
      feedbackEntries = feedbackEntries.filter(f => f.timestamp >= startDate);
    }
    if (endDate) {
      feedbackEntries = feedbackEntries.filter(f => f.timestamp <= endDate);
    }
    if (userId) {
      feedbackEntries = feedbackEntries.filter(f => f.userId === userId);
    }

    const totalFeedback = feedbackEntries.length;
    const positiveRating = feedbackEntries.filter(f => f.rating === 'thumbs_up').length;
    const negativeRating = feedbackEntries.filter(f => f.rating === 'thumbs_down').length;
    const averageRating = totalFeedback > 0 ? positiveRating / totalFeedback : 0;

    // Category breakdown
    const categoryBreakdown: Record<string, { positive: number; negative: number }> = {};
    const modelPerformance: Record<string, { positive: number; negative: number }> = {};
    const featurePerformance: Record<string, { positive: number; negative: number }> = {};

    feedbackEntries.forEach(entry => {
      // Category breakdown
      if (!categoryBreakdown[entry.category]) {
        categoryBreakdown[entry.category] = { positive: 0, negative: 0 };
      }
      if (entry.rating === 'thumbs_up') {
        categoryBreakdown[entry.category].positive++;
      } else {
        categoryBreakdown[entry.category].negative++;
      }

      // Model performance
      if (!modelPerformance[entry.context.model]) {
        modelPerformance[entry.context.model] = { positive: 0, negative: 0 };
      }
      if (entry.rating === 'thumbs_up') {
        modelPerformance[entry.context.model].positive++;
      } else {
        modelPerformance[entry.context.model].negative++;
      }

      // Feature performance
      if (!featurePerformance[entry.context.feature]) {
        featurePerformance[entry.context.feature] = { positive: 0, negative: 0 };
      }
      if (entry.rating === 'thumbs_up') {
        featurePerformance[entry.context.feature].positive++;
      } else {
        featurePerformance[entry.context.feature].negative++;
      }
    });

    // Extract common issues and suggestions from comments
    const negativeComments = feedbackEntries
      .filter(f => f.rating === 'thumbs_down' && f.comment)
      .map(f => f.comment!);

    const commonIssues = this.extractCommonThemes(negativeComments);
    const improvementSuggestions = this.generateImprovementSuggestions(feedbackEntries);

    return {
      totalFeedback,
      positiveRating,
      negativeRating,
      averageRating,
      categoryBreakdown,
      modelPerformance,
      featurePerformance,
      commonIssues,
      improvementSuggestions
    };
  }

  async exportFeedback(startDate?: Date, endDate?: Date): Promise<string> {
    let feedbackEntries = Array.from(this.feedback.values());

    if (startDate) {
      feedbackEntries = feedbackEntries.filter(f => f.timestamp >= startDate);
    }
    if (endDate) {
      feedbackEntries = feedbackEntries.filter(f => f.timestamp <= endDate);
    }

    const exportData = {
      exportDate: new Date().toISOString(),
      totalEntries: feedbackEntries.length,
      feedback: feedbackEntries.map(entry => ({
        ...entry,
        timestamp: entry.timestamp.toISOString()
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  private async persistFeedback(feedback: FeedbackEntry): Promise<void> {
    // In production, this would persist to database
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['feedback'], 'readwrite');
      const store = transaction.objectStore('feedback');
      await store.add(feedback);
    } catch (error) {
      console.error('Failed to persist feedback:', error);
    }
  }

  private extractCommonThemes(comments: string[]): string[] {
    // Simple keyword extraction for common issues
    const keywords = ['safety', 'incorrect', 'slow', 'confusing', 'error', 'bug', 'wrong'];
    const themes: Record<string, number> = {};

    comments.forEach(comment => {
      const lowerComment = comment.toLowerCase();
      keywords.forEach(keyword => {
        if (lowerComment.includes(keyword)) {
          themes[keyword] = (themes[keyword] || 0) + 1;
        }
      });
    });

    return Object.entries(themes)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([theme]) => theme);
  }

  private generateImprovementSuggestions(feedback: FeedbackEntry[]): string[] {
    const suggestions: string[] = [];

    // Analyze patterns in negative feedback
    const negativeFeedback = feedback.filter(f => f.rating === 'thumbs_down');
    
    if (negativeFeedback.length > 0) {
      const lowConfidenceIssues = negativeFeedback.filter(f => f.context.confidence < 0.7);
      if (lowConfidenceIssues.length > negativeFeedback.length * 0.3) {
        suggestions.push('Improve confidence scoring algorithm');
      }

      const safetyIssues = negativeFeedback.filter(f => f.category === 'safety');
      if (safetyIssues.length > 0) {
        suggestions.push('Enhance safety validation rules');
      }

      const accuracyIssues = negativeFeedback.filter(f => f.category === 'accuracy');
      if (accuracyIssues.length > negativeFeedback.length * 0.4) {
        suggestions.push('Retrain model with more domain-specific data');
      }
    }

    return suggestions;
  }

  private async openIndexedDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PLCFeedbackDB', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        if (!db.objectStoreNames.contains('feedback')) {
          const store = db.createObjectStore('feedback', { keyPath: 'id' });
          store.createIndex('timestamp', 'timestamp');
          store.createIndex('userId', 'userId');
          store.createIndex('rating', 'rating');
        }
      };
    });
  }
}

export const feedbackCollector = new FeedbackCollector();