version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"
    volumes:
      - ./:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_SUPABASE_URL=${VITE_SUPABASE_URL}
      - VITE_SUPABASE_ANON_KEY=${VITE_SUPABASE_ANON_KEY}
      - VITE_OPENAI_API_KEY=${VITE_OPENAI_API_KEY}
      - VITE_CLAUDE_API_KEY=${VITE_CLAUDE_API_KEY}
    depends_on:
      - ai-backend

  ai-backend:
    build:
      context: .
      dockerfile: src/ai/backend/Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - FRONTEND_URL=http://localhost:5173
      - OPENAI_API_KEY=${VITE_OPENAI_API_KEY}
      - CLAUDE_API_KEY=${VITE_CLAUDE_API_KEY}
      - REDIS_URL=redis://redis:6379
      - POSTGRES_URL=********************************************/lureon_ai
    volumes:
      - ./src/ai:/app/src/ai
    depends_on:
      - redis
      - postgres

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  postgres:
    image: postgres:15-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=lureon_ai
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d

volumes:
  redis_data:
  postgres_data: