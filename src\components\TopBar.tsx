import React, { useState } from 'react';
import { 
  Menu, 
  Search, 
  Bell, 
  Settings, 
  User, 
  Wifi,
  WifiOff,
  Circle,
  Zap,
  Users,
  Download,
  Upload,
  Save,
  FolderOpen,
  Plus,
  LogOut,
  UserCircle,
  Shield,
  HelpCircle,
  Moon,
  Sun,
  Globe,
  Book
} from 'lucide-react';
import SyncStatusBar from './workspace/SyncStatusBar';
import CollaborationPanel from './collaboration/CollaborationPanel';
import { usePLCStore } from '../store/plcStore';

interface TopBarProps {
  onCommandPaletteOpen: () => void;
  onOpenUserGuide: () => void;
}

const LureonLogo: React.FC<{ className?: string }> = ({ className = "w-8 h-8" }) => (
  <svg 
    width="32" 
    height="32" 
    viewBox="0 0 40 40" 
    xmlns="http://www.w3.org/2000/svg" 
    fill="none"
    className={className}
  >
    <rect x="0" y="0" width="40" height="40" rx="10" fill="#00B8A9"/>
    <rect x="12" y="10" width="12" height="12" rx="2" fill="#111213"/>
    <rect x="6" y="30" width="4" height="10" rx="1" fill="#00B8A9"/>
    <rect x="16" y="30" width="4" height="10" rx="1" fill="#00B8A9"/>
    <rect x="26" y="30" width="4" height="10" rx="1" fill="#00B8A9"/>
  </svg>
);

const TopBar: React.FC<TopBarProps> = ({ onCommandPaletteOpen, onOpenUserGuide }) => {
  const [plcConnected, setPlcConnected] = useState(true);
  const [showCollaboration, setShowCollaboration] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showFileMenu, setShowFileMenu] = useState(false);
  const [showSettingsMenu, setShowSettingsMenu] = useState(false);
  const [showHelpMenu, setShowHelpMenu] = useState(false);
  const [theme, setTheme] = useState<'dark' | 'light'>('dark');
  
  const { currentProject, saveProject, createProject } = usePLCStore();

  const notifications = [
    { id: 1, type: 'warning', message: 'PLC scan time increased to 3.2ms', time: '2 min ago' },
    { id: 2, type: 'info', message: 'Project backup completed successfully', time: '15 min ago' },
    { id: 3, type: 'error', message: 'Connection lost to I/O module slot 3', time: '1 hour ago' }
  ];

  const handleNewProject = () => {
    const name = prompt('Enter project name:');
    if (name) {
      createProject(name, 'New PLC automation project');
    }
    setShowFileMenu(false);
  };

  const handleOpenProject = () => {
    // In a real app, this would open a file dialog
    alert('Open project dialog would appear here');
    setShowFileMenu(false);
  };

  const handleSaveProject = () => {
    saveProject();
    setShowFileMenu(false);
  };

  const handleExportProject = () => {
    if (currentProject) {
      const dataStr = JSON.stringify(currentProject, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${currentProject.name}.json`;
      link.click();
      URL.revokeObjectURL(url);
    }
    setShowFileMenu(false);
  };

  const handleImportProject = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const project = JSON.parse(e.target?.result as string);
            // In a real app, this would load the project
            console.log('Imported project:', project);
            alert('Project imported successfully');
          } catch (error) {
            alert('Error importing project');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
    setShowFileMenu(false);
  };

  const handleUserProfile = () => {
    alert('User profile settings would open here');
    setShowUserMenu(false);
  };

  const handleChangePassword = () => {
    alert('Change password dialog would open here');
    setShowUserMenu(false);
  };

  const handleLogout = () => {
    if (confirm('Are you sure you want to logout?')) {
      alert('Logout functionality would be implemented here');
    }
    setShowUserMenu(false);
  };

  const handleThemeToggle = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
    // In a real app, this would update the global theme
    document.documentElement.classList.toggle('light', newTheme === 'light');
    setShowSettingsMenu(false);
  };

  const handleLanguageChange = () => {
    alert('Language selection dialog would open here');
    setShowSettingsMenu(false);
  };

  const handlePreferences = () => {
    alert('Preferences panel would open here');
    setShowSettingsMenu(false);
  };

  const handleAbout = () => {
    alert('About LUREON dialog would open here');
    setShowHelpMenu(false);
  };

  const handleHelp = () => {
    window.open('https://docs.lureon.com', '_blank');
    setShowHelpMenu(false);
  };

  const handleUserGuide = () => {
    onOpenUserGuide();
    setShowHelpMenu(false);
  };

  const handleKeyboardShortcuts = () => {
    alert('Keyboard shortcuts dialog would open here');
    setShowHelpMenu(false);
  };

  const handleTutorials = () => {
    alert('Tutorials page would open here');
    setShowHelpMenu(false);
  };

  const handleSupportTicket = () => {
    alert('Support ticket form would open here');
    setShowHelpMenu(false);
  };

  return (
    <div className="h-14 bg-base-dark border-b border-control-600 flex items-center justify-between px-6 relative">
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        {/* File Menu */}
        <div className="relative">
          <button
            onClick={() => setShowFileMenu(!showFileMenu)}
            className="p-2 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            title="File Menu"
          >
            <Menu className="w-4 h-4" />
          </button>
          
          {showFileMenu && (
            <div className="absolute top-full left-0 mt-1 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50">
              <div className="py-1">
                <button
                  onClick={handleNewProject}
                  className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  <span>New Project</span>
                </button>
                <button
                  onClick={handleOpenProject}
                  className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                >
                  <FolderOpen className="w-4 h-4" />
                  <span>Open Project</span>
                </button>
                <button
                  onClick={handleSaveProject}
                  className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                >
                  <Save className="w-4 h-4" />
                  <span>Save Project</span>
                </button>
                <hr className="my-1 border-gray-700" />
                <button
                  onClick={handleImportProject}
                  className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                >
                  <Upload className="w-4 h-4" />
                  <span>Import Project</span>
                </button>
                <button
                  onClick={handleExportProject}
                  className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>Export Project</span>
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-3">
          <LureonLogo className="w-8 h-8" />
          <div>
            <h1 className="text-lg font-semibold text-neutral">LUREON</h1>
            <p className="text-xs text-control-400">Industrial Automation IDE</p>
          </div>
        </div>
        
        <div className="h-6 w-px bg-control-600"></div>
        
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-control-400">Project:</span>
          <span className="text-neutral font-medium">{currentProject?.name || 'No Project'}</span>
        </div>
      </div>

      {/* Center Section */}
      <div className="flex-1 max-w-md mx-8">
        <button
          onClick={onCommandPaletteOpen}
          className="w-full bg-control-800 hover:bg-control-700 border border-control-600 rounded-lg px-4 py-2 flex items-center space-x-3 text-left transition-colors"
        >
          <Search className="w-4 h-4 text-control-400" />
          <span className="text-control-400 flex-1">Search commands...</span>
          <div className="flex items-center space-x-1">
            <kbd className="bg-control-700 text-control-300 px-1.5 py-0.5 rounded text-xs font-mono">⌘</kbd>
            <kbd className="bg-control-700 text-control-300 px-1.5 py-0.5 rounded text-xs font-mono">K</kbd>
          </div>
        </button>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-4">
        {/* Collaboration Button */}
        <button
          onClick={() => setShowCollaboration(!showCollaboration)}
          className={`p-2 rounded transition-colors relative ${
            showCollaboration 
              ? 'text-accent bg-accent/20' 
              : 'text-control-400 hover:text-neutral hover:bg-control-800'
          }`}
          title="Collaboration"
        >
          <Users className="w-4 h-4" />
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-success rounded-full text-xs flex items-center justify-center text-base-dark">
            3
          </span>
        </button>

        {/* Sync Status */}
        <SyncStatusBar />

        {/* PLC Status */}
        <div className="flex items-center space-x-2 text-sm">
          {plcConnected ? (
            <>
              <Wifi className="w-4 h-4 text-success" />
              <span className="text-success">S7-1500</span>
              <Circle className="w-2 h-2 bg-success rounded-full animate-pulse" />
            </>
          ) : (
            <>
              <WifiOff className="w-4 h-4 text-error" />
              <span className="text-error">Disconnected</span>
            </>
          )}
        </div>

        <div className="h-6 w-px bg-control-600 mx-2"></div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2">
          {/* Notifications */}
          <div className="relative">
            <button 
              onClick={() => setShowNotifications(!showNotifications)}
              className="p-2 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors relative"
            >
              <Bell className="w-4 h-4" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-error rounded-full text-xs flex items-center justify-center text-neutral">
                {notifications.length}
              </span>
            </button>
            
            {showNotifications && (
              <div className="absolute top-full right-0 mt-1 w-80 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-[80vh] overflow-y-auto">
                <div className="p-4 border-b border-gray-700">
                  <h3 className="text-white font-semibold">Notifications</h3>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {notifications.map(notification => (
                    <div key={notification.id} className="p-3 border-b border-gray-700 last:border-b-0 hover:bg-gray-700/50">
                      <div className="flex items-start space-x-3">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          notification.type === 'error' ? 'bg-red-400' :
                          notification.type === 'warning' ? 'bg-yellow-400' :
                          'bg-blue-400'
                        }`}></div>
                        <div className="flex-1">
                          <p className="text-white text-sm">{notification.message}</p>
                          <p className="text-gray-400 text-xs mt-1">{notification.time}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-3 border-t border-gray-700">
                  <button className="text-blue-400 hover:text-blue-300 text-sm transition-colors">
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>
          
          {/* Help Menu */}
          <div className="relative">
            <button 
              onClick={() => setShowHelpMenu(!showHelpMenu)}
              className="p-2 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            >
              <HelpCircle className="w-4 h-4" />
            </button>
            
            {showHelpMenu && (
              <div className="absolute top-full right-0 mt-1 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-[80vh] overflow-y-auto">
                <div className="py-1">
                  <button
                    onClick={handleUserGuide}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <Book className="w-4 h-4" />
                    <span>User Guide</span>
                  </button>
                  <button
                    onClick={handleTutorials}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <Play className="w-4 h-4" />
                    <span>Tutorials</span>
                  </button>
                  <button
                    onClick={handleKeyboardShortcuts}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <Command className="w-4 h-4" />
                    <span>Keyboard Shortcuts</span>
                  </button>
                  <hr className="my-1 border-gray-700" />
                  <button
                    onClick={handleSupportTicket}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <MessageSquare className="w-4 h-4" />
                    <span>Support</span>
                  </button>
                  <button
                    onClick={handleHelp}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <ExternalLink className="w-4 h-4" />
                    <span>Documentation</span>
                  </button>
                  <hr className="my-1 border-gray-700" />
                  <button
                    onClick={handleAbout}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <Shield className="w-4 h-4" />
                    <span>About LUREON</span>
                  </button>
                </div>
              </div>
            )}
          </div>
          
          {/* Settings Menu */}
          <div className="relative">
            <button 
              onClick={() => setShowSettingsMenu(!showSettingsMenu)}
              className="p-2 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            >
              <Settings className="w-4 h-4" />
            </button>
            
            {showSettingsMenu && (
              <div className="absolute top-full right-0 mt-1 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-[80vh] overflow-y-auto">
                <div className="py-1">
                  <button
                    onClick={handlePreferences}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <Settings className="w-4 h-4" />
                    <span>Preferences</span>
                  </button>
                  <button
                    onClick={handleThemeToggle}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    {theme === 'dark' ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
                    <span>Toggle Theme</span>
                  </button>
                  <button
                    onClick={handleLanguageChange}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <Globe className="w-4 h-4" />
                    <span>Language</span>
                  </button>
                </div>
              </div>
            )}
          </div>
          
          {/* User Menu */}
          <div className="relative">
            <button 
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="p-2 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            >
              <User className="w-4 h-4" />
            </button>
            
            {showUserMenu && (
              <div className="absolute top-full right-0 mt-1 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-[80vh] overflow-y-auto">
                <div className="p-3 border-b border-gray-700">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-semibold">JE</span>
                    </div>
                    <div>
                      <p className="text-white text-sm font-medium">John Engineer</p>
                      <p className="text-gray-400 text-xs"><EMAIL></p>
                    </div>
                  </div>
                </div>
                <div className="py-1">
                  <button
                    onClick={handleUserProfile}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <UserCircle className="w-4 h-4" />
                    <span>Profile Settings</span>
                  </button>
                  <button
                    onClick={handleChangePassword}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-white hover:bg-gray-700 transition-colors"
                  >
                    <Shield className="w-4 h-4" />
                    <span>Change Password</span>
                  </button>
                  <hr className="my-1 border-gray-700" />
                  <button
                    onClick={handleLogout}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-red-400 hover:bg-gray-700 transition-colors"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Logout</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Collaboration Panel */}
      {showCollaboration && (
        <div className="absolute top-full right-0 z-50">
          <CollaborationPanel />
        </div>
      )}
    </div>
  );
};

// Add missing ExternalLink icon
const ExternalLink = ({ className }: { className?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
    <polyline points="15 3 21 3 21 9"></polyline>
    <line x1="10" y1="14" x2="21" y2="3"></line>
  </svg>
);

// Add missing MessageSquare icon
const MessageSquare = ({ className }: { className?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
  </svg>
);

// Add missing Command icon
const Command = ({ className }: { className?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M18 3a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3H6a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3V6a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3h12a3 3 0 0 0 3-3 3 3 0 0 0-3-3z"></path>
  </svg>
);

// Add missing Play icon
const Play = ({ className }: { className?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <polygon points="5 3 19 12 5 21 5 3"></polygon>
  </svg>
);

export default TopBar;