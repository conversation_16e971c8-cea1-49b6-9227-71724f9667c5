import { useState, useCallback } from 'react';
import { aiClient } from '../services/aiClient';
import { mcpService } from '../services/mcpService';
import { useMCPContext } from '../context/MCPStore';

export interface AIRequest {
  type: 'explain' | 'refactor' | 'generate' | 'suggest' | 'debug' | 'predict_next';
  context: any;
  prompt?: string;
}

export interface AIResponse {
  id: string;
  content: string;
  confidence: number;
  suggestions?: any[];
  timestamp: Date;
  prompt?: string;
  timeTaken?: number;
  accepted?: boolean;
}

export interface AIMetrics {
  timeSaved: number;
  linesGenerated: number;
  suggestionsAccepted: number;
  errorsAvoided: number;
}

export const useAI = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [history, setHistory] = useState<AIResponse[]>([]);
  const [metrics, setMetrics] = useState<AIMetrics>({
    timeSaved: 0,
    linesGenerated: 0,
    suggestionsAccepted: 0,
    errorsAvoided: 0
  });
  const [lastPrompt, setLastPrompt] = useState<string>('');
  const [requireReview, setRequireReview] = useState(false);
  
  // Get MCP context
  const { serializeForPrompt, getContextConfidence } = useMCPContext();

  const request = useCallback(async (request: AIRequest): Promise<AIResponse> => {
    setIsLoading(true);
    setError(null);
    const startTime = Date.now();

    try {
      // Try to enhance the prompt with MCP service
      let enhancedPrompt = '';
      if (request.prompt) {
        try {
          const enhancementResponse = await mcpService.enhancePrompt({
            prompt: request.prompt,
            contextTypes: ['program', 'tag', 'safety', 'standard']
          });
          
          enhancedPrompt = enhancementResponse.enhancedPrompt;
          
          // Update the request with the enhanced prompt
          request.prompt = enhancedPrompt;
        } catch (error) {
          console.warn('Failed to enhance prompt with MCP service, using original prompt');
        }
      }
      
      const response = await aiClient.request(request);
      const timeTaken = (Date.now() - startTime) / 1000;
      
      // Adjust confidence based on context quality
      const contextConfidence = getContextConfidence();
      const adjustedConfidence = (response.confidence + contextConfidence) / 2;
      
      const enhancedResponse = {
        ...response,
        confidence: adjustedConfidence,
        prompt: request.prompt,
        timeTaken
      };
      
      setHistory(prev => [...prev, enhancedResponse]);
      setLastPrompt(request.prompt || '');
      
      return enhancedResponse;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'AI request failed';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [serializeForPrompt, getContextConfidence]);

  const explainCode = useCallback(async (code: string, language: 'ladder' | 'st') => {
    return request({
      type: 'explain',
      context: { code, language },
      prompt: `Explain this ${language} code in simple terms for a PLC engineer`
    });
  }, [request]);

  const suggestRefactor = useCallback(async (code: string, language: 'ladder' | 'st') => {
    return request({
      type: 'refactor',
      context: { code, language },
      prompt: 'Suggest improvements for better performance, safety, and maintainability'
    });
  }, [request]);

  const generateCode = useCallback(async (description: string, language: 'ladder' | 'st') => {
    return request({
      type: 'generate',
      context: { description, language },
      prompt: `Generate ${language} code for: ${description}`
    });
  }, [request]);

  const suggestTags = useCallback(async (description: string) => {
    return request({
      type: 'suggest',
      context: { description },
      prompt: `Suggest appropriate PLC tag names and data types for: ${description}`
    });
  }, [request]);

  const debugLogic = useCallback(async (elements: any[], issue: string) => {
    return request({
      type: 'debug',
      context: { elements, issue },
      prompt: `Help debug this ladder logic issue: ${issue}`
    });
  }, [request]);

  const acceptSuggestion = useCallback((responseId: string, estimatedTimeSaved: number = 0) => {
    setMetrics(prev => ({
      ...prev,
      suggestionsAccepted: prev.suggestionsAccepted + 1,
      timeSaved: prev.timeSaved + estimatedTimeSaved
    }));
    
    // Update history to mark as accepted
    setHistory(prev => prev.map(item => 
      item.id === responseId ? { ...item, accepted: true } : item
    ));
  }, []);

  const rejectSuggestion = useCallback((responseId: string) => {
    // Update history to mark as rejected
    setHistory(prev => prev.map(item => 
      item.id === responseId ? { ...item, accepted: false } : item
    ));
  }, []);

  const updateMetrics = useCallback((updates: Partial<AIMetrics>) => {
    setMetrics(prev => ({ ...prev, ...updates }));
  }, []);

  return {
    request,
    explainCode,
    suggestRefactor,
    generateCode,
    suggestTags,
    debugLogic,
    acceptSuggestion,
    rejectSuggestion,
    updateMetrics,
    isLoading,
    error,
    history,
    metrics,
    lastPrompt,
    requireReview,
    setRequireReview,
    clearHistory: () => setHistory([]),
    clearMetrics: () => setMetrics({
      timeSaved: 0,
      linesGenerated: 0,
      suggestionsAccepted: 0,
      errorsAvoided: 0
    })
  };
};