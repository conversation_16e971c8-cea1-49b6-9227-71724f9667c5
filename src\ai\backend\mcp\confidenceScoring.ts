import { ContextItem, ContextItemType, ConfidenceRequest, ConfidenceResponse } from '../../types/mcp';
import { db } from '../backend/db';

export class ConfidenceScoringService {
  async calculateConfidence(request: ConfidenceRequest): Promise<ConfidenceResponse> {
    try {
      // Get context items
      const contextItems = await this.getContextItems(request.contextItems);
      
      // Calculate different aspects of confidence
      const completeness = this.calculateCompleteness(contextItems, request.promptType);
      const freshness = this.calculateFreshness(contextItems);
      const relevance = this.calculateRelevance(contextItems, request.promptType);
      const quality = this.calculateQuality(contextItems);
      
      // Combine scores with weights
      const weights = {
        completeness: 0.4,
        freshness: 0.1,
        relevance: 0.3,
        quality: 0.2
      };
      
      const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0);
      
      const confidence = (
        weights.completeness * completeness +
        weights.freshness * freshness +
        weights.relevance * relevance +
        weights.quality * quality
      ) / totalWeight;
      
      // Generate suggestions for improving context
      const suggestions = this.generateSuggestions(
        contextItems, 
        request.promptType,
        { completeness, freshness, relevance, quality }
      );
      
      // Record usage for analytics
      await this.recordContextUsage(contextItems, request.promptType, confidence);
      
      return {
        confidence,
        breakdown: {
          completeness,
          freshness,
          relevance,
          quality
        },
        suggestions
      };
    } catch (error) {
      console.error('Error calculating confidence:', error);
      return {
        confidence: 0.5, // Default moderate confidence
        breakdown: {
          completeness: 0.5,
          freshness: 0.5,
          relevance: 0.5,
          quality: 0.5
        },
        suggestions: ['Add more context to improve confidence']
      };
    }
  }
  
  private async getContextItems(contextItemIds: string[]): Promise<ContextItem[]> {
    try {
      if (contextItemIds.length === 0) {
        return [];
      }
      
      const placeholders = contextItemIds.map((_, i) => `$${i + 1}`).join(', ');
      const query = `
        SELECT * FROM mcp_context_items 
        WHERE id IN (${placeholders})
      `;
      
      const result = await db.query(query, contextItemIds);
      
      return result.rows.map((row: any) => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : {}
      }));
    } catch (error) {
      console.error('Error getting context items:', error);
      return [];
    }
  }
  
  private calculateCompleteness(contextItems: ContextItem[], promptType: string): number {
    // Different prompt types require different context
    const requiredTypes: Record<string, ContextItemType[]> = {
      'generate': ['program', 'tag', 'safety', 'standard'],
      'explain': ['program', 'rung', 'tag'],
      'debug': ['program', 'rung', 'tag', 'safety'],
      'refactor': ['program', 'tag', 'standard'],
      'suggest': ['program', 'tag']
    };
    
    const required = requiredTypes[promptType] || ['program', 'tag'];
    const presentTypes = new Set(contextItems.map(item => item.type));
    
    let score = 0;
    for (const type of required) {
      if (presentTypes.has(type)) {
        score += 1 / required.length;
      }
    }
    
    return score;
  }
  
  private calculateFreshness(contextItems: ContextItem[]): number {
    if (contextItems.length === 0) return 0;
    
    // Calculate average age of context items
    const now = new Date();
    const ages = contextItems.map(item => {
      const timestamp = new Date(item.timestamp);
      return (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60); // Age in hours
    });
    
    const averageAge = ages.reduce((sum, age) => sum + age, 0) / ages.length;
    
    // Convert to score (newer is better)
    // 0 hours = 1.0, 24 hours = 0.5, 168 hours (1 week) = 0.0
    return Math.max(0, 1 - (averageAge / 168));
  }
  
  private calculateRelevance(contextItems: ContextItem[], promptType: string): number {
    if (contextItems.length === 0) return 0;
    
    // Define relevance weights for different context types based on prompt type
    const relevanceWeights: Record<string, Record<ContextItemType, number>> = {
      'generate': {
        program: 0.8,
        tag: 0.9,
        rung: 0.7,
        safety: 0.9,
        doc: 0.6,
        version: 0.5,
        standard: 0.8
      },
      'explain': {
        program: 0.9,
        tag: 0.7,
        rung: 0.9,
        safety: 0.6,
        doc: 0.8,
        version: 0.5,
        standard: 0.7
      },
      'debug': {
        program: 0.9,
        tag: 0.8,
        rung: 0.9,
        safety: 0.8,
        doc: 0.6,
        version: 0.5,
        standard: 0.7
      },
      'refactor': {
        program: 0.9,
        tag: 0.7,
        rung: 0.8,
        safety: 0.7,
        doc: 0.6,
        version: 0.5,
        standard: 0.9
      },
      'suggest': {
        program: 0.8,
        tag: 0.9,
        rung: 0.7,
        safety: 0.6,
        doc: 0.7,
        version: 0.5,
        standard: 0.8
      }
    };
    
    const weights = relevanceWeights[promptType] || {
      program: 0.8,
      tag: 0.8,
      rung: 0.8,
      safety: 0.8,
      doc: 0.7,
      version: 0.6,
      standard: 0.8
    };
    
    // Calculate weighted relevance
    let totalRelevance = 0;
    let totalWeight = 0;
    
    contextItems.forEach(item => {
      const weight = weights[item.type] || 0.5;
      totalRelevance += weight;
      totalWeight += 1;
    });
    
    return totalWeight > 0 ? totalRelevance / totalWeight : 0;
  }
  
  private calculateQuality(contextItems: ContextItem[]): number {
    if (contextItems.length === 0) return 0;
    
    // Assess quality based on content length, metadata presence, and source
    let totalQuality = 0;
    
    contextItems.forEach(item => {
      let itemQuality = 0;
      
      // Content length (longer is generally better, up to a point)
      const contentLength = item.value.length;
      if (contentLength > 200) itemQuality += 0.3;
      else if (contentLength > 100) itemQuality += 0.2;
      else if (contentLength > 50) itemQuality += 0.1;
      
      // Metadata presence
      if (item.metadata && Object.keys(item.metadata).length > 0) {
        itemQuality += 0.3;
      }
      
      // Source quality
      if (item.source === 'user') itemQuality += 0.3; // User-provided context is often high quality
      else if (item.source === 'auto') itemQuality += 0.2; // Auto-collected is good
      else itemQuality += 0.1; // System or other sources
      
      totalQuality += itemQuality / 0.9; // Normalize to 0-1 range
    });
    
    return totalQuality / contextItems.length;
  }
  
  private generateSuggestions(
    contextItems: ContextItem[], 
    promptType: string,
    scores: { completeness: number; freshness: number; relevance: number; quality: number }
  ): string[] {
    const suggestions: string[] = [];
    const presentTypes = new Set(contextItems.map(item => item.type));
    
    // Suggest missing context types
    const requiredTypes: Record<string, ContextItemType[]> = {
      'generate': ['program', 'tag', 'safety', 'standard'],
      'explain': ['program', 'rung', 'tag'],
      'debug': ['program', 'rung', 'tag', 'safety'],
      'refactor': ['program', 'tag', 'standard'],
      'suggest': ['program', 'tag']
    };
    
    const required = requiredTypes[promptType] || ['program', 'tag'];
    
    for (const type of required) {
      if (!presentTypes.has(type)) {
        switch (type) {
          case 'program':
            suggestions.push('Add program context for better code generation');
            break;
          case 'tag':
            suggestions.push('Include tag definitions to improve variable handling');
            break;
          case 'rung':
            suggestions.push('Add specific rung context for more targeted assistance');
            break;
          case 'safety':
            suggestions.push('Include safety requirements for compliant code generation');
            break;
          case 'standard':
            suggestions.push('Specify industry standards for compliant code');
            break;
          default:
            suggestions.push(`Add ${type} context to improve results`);
        }
      }
    }
    
    // Suggest improvements based on scores
    if (scores.freshness < 0.5) {
      suggestions.push('Update context with more recent information');
    }
    
    if (scores.quality < 0.6) {
      suggestions.push('Provide more detailed context descriptions');
    }
    
    return suggestions;
  }
  
  private async recordContextUsage(
    contextItems: ContextItem[], 
    promptType: string, 
    confidence: number
  ): Promise<void> {
    try {
      // Create a unique request ID
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      // Record usage for each context item
      for (const item of contextItems) {
        await db.query(
          `INSERT INTO mcp_context_usage 
           (context_id, request_id, prompt_type, confidence_contribution, timestamp)
           VALUES ($1, $2, $3, $4, NOW())`,
          [
            item.id,
            requestId,
            promptType,
            confidence / contextItems.length // Distribute confidence equally
          ]
        );
      }
    } catch (error) {
      console.error('Error recording context usage:', error);
    }
  }
}

export const confidenceScoringService = new ConfidenceScoringService();