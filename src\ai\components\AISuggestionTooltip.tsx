import React, { useState } from 'react';
import { AIResponse } from '../hooks/useAI';
import { 
  Lightbulb, 
  Check, 
  X, 
  Edit, 
  Info, 
  Zap,
  AlertTriangle
} from 'lucide-react';

interface AISuggestionTooltipProps {
  suggestion: AIResponse;
  position: { x: number; y: number };
  onApply: () => void;
  onEdit: () => void;
  onDismiss: () => void;
  onExplain: () => void;
}

const AISuggestionTooltip: React.FC<AISuggestionTooltipProps> = ({
  suggestion,
  position,
  onApply,
  onEdit,
  onDismiss,
  onExplain
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-400';
    if (confidence >= 0.6) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getConfidenceIcon = (confidence: number) => {
    if (confidence >= 0.8) return <Check className="w-3 h-3" />;
    if (confidence >= 0.6) return <AlertTriangle className="w-3 h-3" />;
    return <X className="w-3 h-3" />;
  };

  return (
    <div 
      className="absolute z-50 bg-gray-800 border border-blue-500 rounded-lg shadow-xl max-w-md"
      style={{ 
        left: position.x, 
        top: position.y,
        transform: 'translate(-50%, -100%)'
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <div className="flex items-center space-x-2">
          <Lightbulb className="w-4 h-4 text-blue-400" />
          <span className="text-white font-medium">AI Suggestion</span>
          <div className={`flex items-center space-x-1 ${getConfidenceColor(suggestion.confidence)}`}>
            {getConfidenceIcon(suggestion.confidence)}
            <span className="text-xs">{Math.round(suggestion.confidence * 100)}%</span>
          </div>
        </div>
        <button
          onClick={onDismiss}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* Content */}
      <div className="p-3">
        <div className={`text-gray-300 text-sm ${isExpanded ? '' : 'line-clamp-3'}`}>
          {suggestion.content}
        </div>
        
        {suggestion.content.length > 150 && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-blue-400 hover:text-blue-300 text-xs mt-1 transition-colors"
          >
            {isExpanded ? 'Show less' : 'Show more'}
          </button>
        )}

        {suggestion.suggestions && suggestion.suggestions.length > 0 && (
          <div className="mt-3">
            <div className="text-xs text-gray-400 mb-1">Quick actions:</div>
            <div className="flex flex-wrap gap-1">
              {suggestion.suggestions.slice(0, 3).map((item, index) => (
                <span
                  key={index}
                  className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded"
                >
                  {item}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between p-3 border-t border-gray-700 bg-gray-800/50">
        <div className="flex items-center space-x-2">
          <button
            onClick={onApply}
            className="flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors"
          >
            <Check className="w-3 h-3" />
            <span>Apply</span>
          </button>
          
          <button
            onClick={onEdit}
            className="flex items-center space-x-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs transition-colors"
          >
            <Edit className="w-3 h-3" />
            <span>Edit</span>
          </button>
        </div>
        
        <button
          onClick={onExplain}
          className="flex items-center space-x-1 text-gray-400 hover:text-white px-2 py-1 rounded text-xs transition-colors"
        >
          <Info className="w-3 h-3" />
          <span>Explain</span>
        </button>
      </div>
    </div>
  );
};

export default AISuggestionTooltip;