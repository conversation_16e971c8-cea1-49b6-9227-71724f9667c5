import React, { useState, useEffect } from 'react';
import { SystemConfig, License } from '../../types/enterprise';
import { enterpriseServices } from '../../services/enterpriseServices';
import { Settings, Shield, Database, Server, Key, Users, Activity, AlertTriangle, CheckCircle2, HardDrive, Network, Clock, FileText, BarChart3, Cpu, MemoryStick as Memory, Wifi, Globe, Lock, Zap } from 'lucide-react';
import UserManagement from './UserManagement';
import AuditTrail from './AuditTrail';

const SystemAdmin: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [systemConfig, setSystemConfig] = useState<SystemConfig>({
    version: '1.0.0',
    environment: 'production',
    features: [
      { name: 'ai_assistant', enabled: true, rolloutPercentage: 100 },
      { name: 'collaboration', enabled: true, rolloutPercentage: 100 },
      { name: 'safety_validation', enabled: true, rolloutPercentage: 100 },
      { name: 'cloud_sync', enabled: false, rolloutPercentage: 0 },
      { name: 'advanced_simulation', enabled: true, rolloutPercentage: 85 },
      { name: 'signal_tracing', enabled: true, rolloutPercentage: 100 }
    ],
    security: {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        maxAge: 90
      },
      sessionTimeout: 3600,
      maxLoginAttempts: 5,
      lockoutDuration: 900,
      encryptionKey: 'encrypted_key_placeholder'
    },
    database: {
      type: 'postgresql',
      host: 'localhost',
      port: 5432,
      database: 'relay_plc',
      username: 'relay_user',
      password: 'encrypted_password',
      ssl: true,
      poolSize: 20
    },
    logging: {
      level: 'info',
      format: 'json',
      outputs: [
        { type: 'console', configuration: {} },
        { type: 'file', configuration: { path: '/var/log/relay' } },
        { type: 'database', configuration: { table: 'audit_logs' } }
      ],
      retention: 2555 // 7 years for compliance
    },
    backup: {
      enabled: true,
      schedule: '0 2 * * *', // Daily at 2 AM
      retention: 30,
      destination: '/backup/relay',
      encryption: true
    }
  });

  const [license, setLicense] = useState<License>({
    id: 'license-1',
    type: 'enterprise',
    features: [
      { name: 'unlimited_projects', enabled: true },
      { name: 'advanced_simulation', enabled: true },
      { name: 'safety_certification', enabled: true },
      { name: 'cloud_deployment', enabled: true },
      { name: 'priority_support', enabled: true },
      { name: 'ai_assistant', enabled: true },
      { name: 'collaboration', enabled: true },
      { name: 'audit_trail', enabled: true }
    ],
    limits: {
      maxProjects: -1, // unlimited
      maxPrograms: -1,
      maxTags: -1,
      maxTargets: -1,
      cloudStorage: 1000, // 1TB
      supportLevel: 'enterprise'
    },
    validFrom: new Date('2024-01-01'),
    validUntil: new Date('2025-01-01'),
    seats: 50,
    usedSeats: 23,
    organization: 'Industrial Automation Corp',
    contactEmail: '<EMAIL>',
    status: 'active'
  });

  const [systemStats, setSystemStats] = useState({
    uptime: '99.9%',
    cpuUsage: 15,
    memoryUsage: 34,
    diskUsage: 67,
    networkLatency: 12,
    activeConnections: 156,
    requestsPerMinute: 2340,
    errorRate: 0.02
  });

  const tabs = [
    { id: 'overview', name: 'System Overview', icon: Activity },
    { id: 'users', name: 'User Management', icon: Users },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'database', name: 'Database', icon: Database },
    { id: 'license', name: 'License', icon: Key },
    { id: 'audit', name: 'Audit Trail', icon: FileText },
    { id: 'backup', name: 'Backup & Recovery', icon: HardDrive },
    { id: 'monitoring', name: 'System Monitoring', icon: BarChart3 }
  ];

  useEffect(() => {
    // Simulate real-time system stats updates
    const interval = setInterval(() => {
      setSystemStats(prev => ({
        ...prev,
        cpuUsage: Math.max(5, Math.min(95, prev.cpuUsage + (Math.random() - 0.5) * 10)),
        memoryUsage: Math.max(20, Math.min(80, prev.memoryUsage + (Math.random() - 0.5) * 5)),
        networkLatency: Math.max(5, Math.min(50, prev.networkLatency + (Math.random() - 0.5) * 5)),
        activeConnections: Math.max(100, Math.min(300, prev.activeConnections + Math.floor((Math.random() - 0.5) * 20))),
        requestsPerMinute: Math.max(1000, Math.min(5000, prev.requestsPerMinute + Math.floor((Math.random() - 0.5) * 200)))
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const renderOverview = () => (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-semibold text-white">System Overview</h2>
      
      {/* System Status Cards */}
      <div className="grid grid-cols-4 gap-4">
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <Server className="w-5 h-5 text-green-400" />
            <CheckCircle2 className="w-4 h-4 text-green-400" />
          </div>
          <div className="text-2xl font-bold text-white">{systemStats.uptime}</div>
          <div className="text-sm text-gray-400">System Uptime</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <Users className="w-5 h-5 text-blue-400" />
            <span className="text-sm text-blue-400">{license.usedSeats}/{license.seats}</span>
          </div>
          <div className="text-2xl font-bold text-white">{license.usedSeats}</div>
          <div className="text-sm text-gray-400">Active Users</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <Database className="w-5 h-5 text-purple-400" />
            <CheckCircle2 className="w-4 h-4 text-green-400" />
          </div>
          <div className="text-2xl font-bold text-white">2.3GB</div>
          <div className="text-sm text-gray-400">Database Size</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <HardDrive className="w-5 h-5 text-yellow-400" />
            <CheckCircle2 className="w-4 h-4 text-green-400" />
          </div>
          <div className="text-2xl font-bold text-white">Daily</div>
          <div className="text-sm text-gray-400">Backup Status</div>
        </div>
      </div>

      {/* Real-time System Metrics */}
      <div className="grid grid-cols-2 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">System Performance</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-400">CPU Usage</span>
                <span className="text-white">{systemStats.cpuUsage}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    systemStats.cpuUsage > 80 ? 'bg-red-500' : 
                    systemStats.cpuUsage > 60 ? 'bg-yellow-500' : 'bg-green-500'
                  }`}
                  style={{ width: `${systemStats.cpuUsage}%` }}
                />
              </div>
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-400">Memory Usage</span>
                <span className="text-white">{systemStats.memoryUsage}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    systemStats.memoryUsage > 80 ? 'bg-red-500' : 
                    systemStats.memoryUsage > 60 ? 'bg-yellow-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${systemStats.memoryUsage}%` }}
                />
              </div>
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-400">Disk Usage</span>
                <span className="text-white">{systemStats.diskUsage}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    systemStats.diskUsage > 80 ? 'bg-red-500' : 
                    systemStats.diskUsage > 60 ? 'bg-yellow-500' : 'bg-purple-500'
                  }`}
                  style={{ width: `${systemStats.diskUsage}%` }}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Network & Activity</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Network className="w-5 h-5 text-green-400" />
              </div>
              <div className="text-xl font-bold text-white">{systemStats.networkLatency}ms</div>
              <div className="text-xs text-gray-400">Network Latency</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Wifi className="w-5 h-5 text-blue-400" />
              </div>
              <div className="text-xl font-bold text-white">{systemStats.activeConnections}</div>
              <div className="text-xs text-gray-400">Active Connections</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Activity className="w-5 h-5 text-purple-400" />
              </div>
              <div className="text-xl font-bold text-white">{systemStats.requestsPerMinute}</div>
              <div className="text-xs text-gray-400">Requests/min</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <AlertTriangle className="w-5 h-5 text-yellow-400" />
              </div>
              <div className="text-xl font-bold text-white">{systemStats.errorRate}%</div>
              <div className="text-xs text-gray-400">Error Rate</div>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Flags */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Feature Flags</h3>
        <div className="grid grid-cols-2 gap-4">
          {systemConfig.features.map(feature => (
            <div key={feature.name} className="flex items-center justify-between p-3 bg-gray-700/50 rounded">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${feature.enabled ? 'bg-green-400' : 'bg-gray-500'}`}></div>
                <span className="text-white capitalize">{feature.name.replace('_', ' ')}</span>
              </div>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-400">{feature.rolloutPercentage}%</span>
                <button
                  onClick={() => {
                    const updatedFeatures = systemConfig.features.map(f =>
                      f.name === feature.name ? { ...f, enabled: !f.enabled } : f
                    );
                    setSystemConfig({ ...systemConfig, features: updatedFeatures });
                  }}
                  className={`px-3 py-1 rounded text-sm transition-colors ${
                    feature.enabled 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : 'bg-gray-600 hover:bg-gray-700 text-white'
                  }`}
                >
                  {feature.enabled ? 'Enabled' : 'Disabled'}
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Recent System Activity</h3>
        <div className="space-y-3">
          <div className="flex items-center space-x-3 text-sm">
            <CheckCircle2 className="w-4 h-4 text-green-400" />
            <span className="text-gray-400">2 minutes ago</span>
            <span className="text-white">System backup completed successfully</span>
          </div>
          <div className="flex items-center space-x-3 text-sm">
            <Users className="w-4 h-4 text-blue-400" />
            <span className="text-gray-400">15 minutes ago</span>
            <span className="text-white">New user john.engineer logged in</span>
          </div>
          <div className="flex items-center space-x-3 text-sm">
            <Activity className="w-4 h-4 text-purple-400" />
            <span className="text-gray-400">1 hour ago</span>
            <span className="text-white">Project deployed to PLC target S7-1500</span>
          </div>
          <div className="flex items-center space-x-3 text-sm">
            <AlertTriangle className="w-4 h-4 text-yellow-400" />
            <span className="text-gray-400">3 hours ago</span>
            <span className="text-white">License usage at 85% capacity</span>
          </div>
          <div className="flex items-center space-x-3 text-sm">
            <Zap className="w-4 h-4 text-green-400" />
            <span className="text-gray-400">6 hours ago</span>
            <span className="text-white">AI assistant processed 1,234 requests</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecurity = () => (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-semibold text-white">Security Configuration</h2>
      
      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Password Policy</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-2">Minimum Length</label>
            <input
              type="number"
              value={systemConfig.security.passwordPolicy.minLength}
              onChange={(e) => setSystemConfig({
                ...systemConfig,
                security: {
                  ...systemConfig.security,
                  passwordPolicy: {
                    ...systemConfig.security.passwordPolicy,
                    minLength: parseInt(e.target.value)
                  }
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-2">Max Age (days)</label>
            <input
              type="number"
              value={systemConfig.security.passwordPolicy.maxAge}
              onChange={(e) => setSystemConfig({
                ...systemConfig,
                security: {
                  ...systemConfig.security,
                  passwordPolicy: {
                    ...systemConfig.security.passwordPolicy,
                    maxAge: parseInt(e.target.value)
                  }
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            />
          </div>
        </div>
        
        <div className="mt-4 space-y-2">
          {[
            { key: 'requireUppercase', label: 'Require Uppercase' },
            { key: 'requireLowercase', label: 'Require Lowercase' },
            { key: 'requireNumbers', label: 'Require Numbers' },
            { key: 'requireSpecialChars', label: 'Require Special Characters' }
          ].map(({ key, label }) => (
            <div key={key} className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={systemConfig.security.passwordPolicy[key as keyof typeof systemConfig.security.passwordPolicy] as boolean}
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  security: {
                    ...systemConfig.security,
                    passwordPolicy: {
                      ...systemConfig.security.passwordPolicy,
                      [key]: e.target.checked
                    }
                  }
                })}
                className="rounded"
              />
              <label className="text-white">{label}</label>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Session Management</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-2">Session Timeout (seconds)</label>
            <input
              type="number"
              value={systemConfig.security.sessionTimeout}
              onChange={(e) => setSystemConfig({
                ...systemConfig,
                security: {
                  ...systemConfig.security,
                  sessionTimeout: parseInt(e.target.value)
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-2">Max Login Attempts</label>
            <input
              type="number"
              value={systemConfig.security.maxLoginAttempts}
              onChange={(e) => setSystemConfig({
                ...systemConfig,
                security: {
                  ...systemConfig.security,
                  maxLoginAttempts: parseInt(e.target.value)
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            />
          </div>
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Security Status</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center justify-between p-3 bg-green-600/20 border border-green-600/30 rounded">
            <div className="flex items-center space-x-2">
              <Lock className="w-5 h-5 text-green-400" />
              <span className="text-white">SSL/TLS</span>
            </div>
            <CheckCircle2 className="w-5 h-5 text-green-400" />
          </div>
          
          <div className="flex items-center justify-between p-3 bg-green-600/20 border border-green-600/30 rounded">
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-green-400" />
              <span className="text-white">Firewall</span>
            </div>
            <CheckCircle2 className="w-5 h-5 text-green-400" />
          </div>
          
          <div className="flex items-center justify-between p-3 bg-green-600/20 border border-green-600/30 rounded">
            <div className="flex items-center space-x-2">
              <Key className="w-5 h-5 text-green-400" />
              <span className="text-white">Encryption</span>
            </div>
            <CheckCircle2 className="w-5 h-5 text-green-400" />
          </div>
          
          <div className="flex items-center justify-between p-3 bg-yellow-600/20 border border-yellow-600/30 rounded">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-yellow-400" />
              <span className="text-white">2FA</span>
            </div>
            <span className="text-yellow-400 text-sm">Optional</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLicense = () => (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-semibold text-white">License Management</h2>
      
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Current License</h3>
          <span className={`px-3 py-1 rounded text-sm ${
            license.status === 'active' ? 'bg-green-600/20 text-green-400' :
            license.status === 'expired' ? 'bg-red-600/20 text-red-400' :
            'bg-yellow-600/20 text-yellow-400'
          }`}>
            {license.status.toUpperCase()}
          </span>
        </div>
        
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-1">License Type</label>
              <span className="text-white capitalize">{license.type}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Organization</label>
              <span className="text-white">{license.organization}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Contact Email</label>
              <span className="text-white">{license.contactEmail}</span>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-1">Valid From</label>
              <span className="text-white">{license.validFrom.toLocaleDateString()}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Valid Until</label>
              <span className="text-white">{license.validUntil.toLocaleDateString()}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Seat Usage</label>
              <div className="flex items-center space-x-2">
                <span className="text-white">{license.usedSeats} / {license.seats}</span>
                <div className="flex-1 bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full" 
                    style={{ width: `${(license.usedSeats / license.seats) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Licensed Features</h3>
        <div className="grid grid-cols-2 gap-4">
          {license.features.map(feature => (
            <div key={feature.name} className="flex items-center justify-between">
              <span className="text-white capitalize">{feature.name.replace('_', ' ')}</span>
              <span className={`px-2 py-1 rounded text-xs ${
                feature.enabled ? 'bg-green-600/20 text-green-400' : 'bg-red-600/20 text-red-400'
              }`}>
                {feature.enabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Usage Limits</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-1">Max Projects</label>
            <span className="text-white">{license.limits.maxProjects === -1 ? 'Unlimited' : license.limits.maxProjects}</span>
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-1">Max Programs</label>
            <span className="text-white">{license.limits.maxPrograms === -1 ? 'Unlimited' : license.limits.maxPrograms}</span>
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-1">Cloud Storage</label>
            <span className="text-white">{license.limits.cloudStorage} GB</span>
          </div>
          <div>
            <label className="block text-sm text-gray-400 mb-1">Support Level</label>
            <span className="text-white capitalize">{license.limits.supportLevel}</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMonitoring = () => (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-semibold text-white">System Monitoring</h2>
      
      <div className="grid grid-cols-3 gap-6">
        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">CPU Usage</h3>
            <Cpu className="w-5 h-5 text-blue-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">{systemStats.cpuUsage}%</div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                systemStats.cpuUsage > 80 ? 'bg-red-500' : 
                systemStats.cpuUsage > 60 ? 'bg-yellow-500' : 'bg-blue-500'
              }`}
              style={{ width: `${systemStats.cpuUsage}%` }}
            />
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Memory Usage</h3>
            <Memory className="w-5 h-5 text-green-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">{systemStats.memoryUsage}%</div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                systemStats.memoryUsage > 80 ? 'bg-red-500' : 
                systemStats.memoryUsage > 60 ? 'bg-yellow-500' : 'bg-green-500'
              }`}
              style={{ width: `${systemStats.memoryUsage}%` }}
            />
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Network</h3>
            <Network className="w-5 h-5 text-purple-400" />
          </div>
          <div className="text-3xl font-bold text-white mb-2">{systemStats.networkLatency}ms</div>
          <div className="text-sm text-gray-400">Average Latency</div>
        </div>
      </div>

      <div className="bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">System Health Checks</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center justify-between p-3 bg-green-600/20 border border-green-600/30 rounded">
            <span className="text-white">Database Connection</span>
            <CheckCircle2 className="w-5 h-5 text-green-400" />
          </div>
          <div className="flex items-center justify-between p-3 bg-green-600/20 border border-green-600/30 rounded">
            <span className="text-white">Redis Cache</span>
            <CheckCircle2 className="w-5 h-5 text-green-400" />
          </div>
          <div className="flex items-center justify-between p-3 bg-green-600/20 border border-green-600/30 rounded">
            <span className="text-white">File System</span>
            <CheckCircle2 className="w-5 h-5 text-green-400" />
          </div>
          <div className="flex items-center justify-between p-3 bg-green-600/20 border border-green-600/30 rounded">
            <span className="text-white">External APIs</span>
            <CheckCircle2 className="w-5 h-5 text-green-400" />
          </div>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview': return renderOverview();
      case 'users': return <UserManagement />;
      case 'security': return renderSecurity();
      case 'license': return renderLicense();
      case 'audit': return <AuditTrail />;
      case 'monitoring': return renderMonitoring();
      case 'database':
      case 'backup':
        return (
          <div className="p-6 text-center text-gray-400">
            <Settings className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg mb-2">{tabs.find(t => t.id === activeTab)?.name}</p>
            <p className="text-sm">Advanced configuration panel with full database management, automated backup scheduling, and disaster recovery options.</p>
          </div>
        );
      default: return renderOverview();
    }
  };

  return (
    <div className="h-full bg-gray-900 flex">
      {/* Sidebar */}
      <div className="w-64 bg-gray-800 border-r border-gray-700">
        <div className="p-4 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white">System Administration</h2>
          <div className="text-sm text-gray-400 mt-1">LUREON Enterprise</div>
        </div>
        
        <nav className="p-2">
          {tabs.map(tab => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <IconComponent className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>
      
      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        {renderContent()}
      </div>
    </div>
  );
};

export default SystemAdmin;