-- Database initialization for L<PERSON><PERSON>ON AI Backend

-- Create audit logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255),
    prompt_hash VARCHAR(64) NOT NULL,
    response_hash VARCHAR(64) NOT NULL,
    model_used VARCHAR(100) NOT NULL,
    confidence_score DECIMAL(3,2),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address INET,
    request_type VARCHAR(50),
    safety_validated BOOLEAN DEFAULT FALSE,
    compliance_issues JSONB DEFAULT '[]'::jsonb
);

-- Create feedback table
CREATE TABLE IF NOT EXISTS feedback (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    suggestion_id VARCHAR(255) NOT NULL,
    rating VARCHAR(20) CHECK (rating IN ('thumbs_up', 'thumbs_down')),
    category VARCHAR(50) CHECK (category IN ('accuracy', 'safety', 'performance', 'usability')),
    comment TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    context JSONB
);

-- Create usage metrics table
CREATE TABLE IF NOT EXISTS usage_metrics (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    organization_id VARCHAR(255),
    period VARCHAR(10) CHECK (period IN ('daily', 'monthly')),
    ai_requests INTEGER DEFAULT 0,
    tokens_consumed INTEGER DEFAULT 0,
    cost_estimate DECIMAL(10,4) DEFAULT 0,
    model_usage JSONB DEFAULT '{}'::jsonb,
    feature_usage JSONB DEFAULT '{}'::jsonb,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, period, DATE_TRUNC(period, timestamp))
);

-- Create cache table
CREATE TABLE IF NOT EXISTS prompt_cache (
    hash VARCHAR(64) PRIMARY KEY,
    response TEXT NOT NULL,
    confidence DECIMAL(3,2),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    hit_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create digital signatures table
CREATE TABLE IF NOT EXISTS digital_signatures (
    id VARCHAR(255) PRIMARY KEY,
    content_hash VARCHAR(64) NOT NULL,
    signature TEXT NOT NULL,
    algorithm VARCHAR(50) NOT NULL,
    key_id VARCHAR(100) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_model_used ON audit_logs(model_used);

CREATE INDEX IF NOT EXISTS idx_feedback_user_id ON feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_feedback_timestamp ON feedback(timestamp);
CREATE INDEX IF NOT EXISTS idx_feedback_rating ON feedback(rating);

CREATE INDEX IF NOT EXISTS idx_usage_metrics_user_id ON usage_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_metrics_timestamp ON usage_metrics(timestamp);

CREATE INDEX IF NOT EXISTS idx_prompt_cache_timestamp ON prompt_cache(timestamp);

-- Create function to clean up old data
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Clean up cache entries older than 24 hours
    DELETE FROM prompt_cache WHERE timestamp < NOW() - INTERVAL '24 hours';
    
    -- Clean up usage metrics older than 1 year
    DELETE FROM usage_metrics WHERE timestamp < NOW() - INTERVAL '1 year';
    
    -- Archive audit logs older than 7 years (compliance requirement)
    -- In production, this would move to archive storage instead of delete
    -- DELETE FROM audit_logs WHERE timestamp < NOW() - INTERVAL '7 years';
END;
$$ LANGUAGE plpgsql;

-- Create scheduled job to run cleanup (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data();');

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO lureon_ai_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO lureon_ai_user;