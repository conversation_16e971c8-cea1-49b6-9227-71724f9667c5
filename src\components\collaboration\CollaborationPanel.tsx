import React, { useState, useEffect } from 'react';
import { collaborationEngine, CollaborationUser, Comment } from '../../services/collaborationEngine';
import { 
  Users, 
  MessageSquare, 
  Video, 
  Share2, 
  Eye,
  Edit,
  MessageCircle,
  CheckCircle2,
  Clock,
  User
} from 'lucide-react';

const CollaborationPanel: React.FC = () => {
  const [activeUsers, setActiveUsers] = useState<CollaborationUser[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [currentUser, setCurrentUser] = useState<CollaborationUser | null>(null);
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');

  useEffect(() => {
    // Initialize collaboration
    const user: CollaborationUser = {
      id: 'user-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      color: '#3b82f6'
    };

    collaborationEngine.connect('current-project', user);
    setCurrentUser(user);

    // Set up event listeners
    collaborationEngine.on('user_joined', (user: CollaborationUser) => {
      setActiveUsers(prev => [...prev.filter(u => u.id !== user.id), user]);
    });

    collaborationEngine.on('user_left', (userId: string) => {
      setActiveUsers(prev => prev.filter(u => u.id !== userId));
    });

    collaborationEngine.on('comment_added', (comment: Comment) => {
      setComments(prev => [...prev, comment]);
    });

    collaborationEngine.on('comment_resolved', (comment: Comment) => {
      setComments(prev => prev.map(c => c.id === comment.id ? comment : c));
    });

    // Update connection status
    const updateStatus = () => {
      setIsConnected(collaborationEngine.isConnected());
      setActiveUsers(collaborationEngine.getActiveUsers());
      setComments(collaborationEngine.getComments());
    };

    const interval = setInterval(updateStatus, 1000);

    return () => {
      clearInterval(interval);
      collaborationEngine.disconnect();
    };
  }, []);

  const handleAddComment = () => {
    if (newComment.trim()) {
      collaborationEngine.addComment(
        newComment,
        'program',
        'main-program',
        { x: 100, y: 100 }
      );
      setNewComment('');
    }
  };

  const handleResolveComment = (commentId: string) => {
    collaborationEngine.resolveComment(commentId);
  };

  const getUserColor = (userId: string) => {
    const user = activeUsers.find(u => u.id === userId);
    return user?.color || '#6b7280';
  };

  const renderUserAvatar = (user: CollaborationUser, size: 'sm' | 'md' = 'md') => {
    const sizeClasses = size === 'sm' ? 'w-6 h-6 text-xs' : 'w-8 h-8 text-sm';
    
    return (
      <div 
        className={`${sizeClasses} rounded-full flex items-center justify-center text-white font-semibold`}
        style={{ backgroundColor: user.color }}
        title={user.name}
      >
        {user.name.charAt(0).toUpperCase()}
      </div>
    );
  };

  const renderComment = (comment: Comment) => (
    <div key={comment.id} className={`bg-gray-800/50 rounded-lg p-3 ${comment.resolved ? 'opacity-60' : ''}`}>
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-2">
          {renderUserAvatar(
            activeUsers.find(u => u.id === comment.author) || {
              id: comment.author,
              name: comment.author,
              email: '',
              color: '#6b7280'
            },
            'sm'
          )}
          <div>
            <span className="text-white text-sm font-medium">
              {activeUsers.find(u => u.id === comment.author)?.name || comment.author}
            </span>
            <div className="text-xs text-gray-400">
              {comment.timestamp.toLocaleString()}
            </div>
          </div>
        </div>
        
        {!comment.resolved && (
          <button
            onClick={() => handleResolveComment(comment.id)}
            className="text-gray-400 hover:text-green-400 transition-colors"
            title="Resolve comment"
          >
            <CheckCircle2 className="w-4 h-4" />
          </button>
        )}
      </div>
      
      <p className="text-gray-300 text-sm mb-2">{comment.content}</p>
      
      {comment.resolved && (
        <div className="flex items-center space-x-1 text-xs text-green-400">
          <CheckCircle2 className="w-3 h-3" />
          <span>Resolved</span>
        </div>
      )}
      
      {comment.replies.length > 0 && (
        <div className="mt-3 pl-4 border-l-2 border-gray-700 space-y-2">
          {comment.replies.map(reply => (
            <div key={reply.id} className="text-sm">
              <div className="flex items-center space-x-2 mb-1">
                {renderUserAvatar(
                  activeUsers.find(u => u.id === reply.author) || {
                    id: reply.author,
                    name: reply.author,
                    email: '',
                    color: '#6b7280'
                  },
                  'sm'
                )}
                <span className="text-white font-medium">
                  {activeUsers.find(u => u.id === reply.author)?.name || reply.author}
                </span>
                <span className="text-xs text-gray-400">
                  {reply.timestamp.toLocaleString()}
                </span>
              </div>
              <p className="text-gray-300">{reply.content}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className="w-80 bg-gray-900 border-l border-gray-800 flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-white">Collaboration</h3>
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowComments(false)}
            className={`flex items-center space-x-2 px-3 py-1 rounded text-sm transition-colors ${
              !showComments ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
            }`}
          >
            <Users className="w-4 h-4" />
            <span>Users ({activeUsers.length})</span>
          </button>
          
          <button
            onClick={() => setShowComments(true)}
            className={`flex items-center space-x-2 px-3 py-1 rounded text-sm transition-colors ${
              showComments ? 'bg-blue-600 text-white' : 'text-gray-400 hover:text-white'
            }`}
          >
            <MessageSquare className="w-4 h-4" />
            <span>Comments ({comments.filter(c => !c.resolved).length})</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {!showComments ? (
          /* Active Users */
          <div className="p-4 space-y-3">
            {currentUser && (
              <div className="bg-blue-600/20 border border-blue-600/30 rounded-lg p-3">
                <div className="flex items-center space-x-3">
                  {renderUserAvatar(currentUser)}
                  <div>
                    <div className="text-white font-medium">{currentUser.name}</div>
                    <div className="text-xs text-blue-400">You</div>
                  </div>
                </div>
              </div>
            )}
            
            {activeUsers.filter(u => u.id !== currentUser?.id).map(user => (
              <div key={user.id} className="bg-gray-800/50 rounded-lg p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {renderUserAvatar(user)}
                    <div>
                      <div className="text-white font-medium">{user.name}</div>
                      <div className="text-xs text-gray-400">{user.email}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {user.cursor && (
                      <Eye className="w-4 h-4 text-green-400" title="Currently viewing" />
                    )}
                    {user.selection && (
                      <Edit className="w-4 h-4 text-blue-400" title="Currently editing" />
                    )}
                  </div>
                </div>
                
                {user.cursor && (
                  <div className="mt-2 text-xs text-gray-400">
                    Viewing: {user.cursor.resourceType} {user.cursor.resourceId}
                  </div>
                )}
              </div>
            ))}
            
            {activeUsers.length === 0 && (
              <div className="text-center py-8 text-gray-400">
                <Users className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No other users online</p>
              </div>
            )}
          </div>
        ) : (
          /* Comments */
          <div className="p-4 space-y-3">
            {comments.map(renderComment)}
            
            {comments.length === 0 && (
              <div className="text-center py-8 text-gray-400">
                <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No comments yet</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Add Comment */}
      {showComments && (
        <div className="border-t border-gray-800 p-4">
          <div className="flex items-end space-x-2">
            <div className="flex-1">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add a comment..."
                className="w-full bg-gray-800 border border-gray-700 rounded px-3 py-2 text-white text-sm resize-none"
                rows={2}
              />
            </div>
            <button
              onClick={handleAddComment}
              disabled={!newComment.trim()}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white p-2 rounded transition-colors"
            >
              <MessageCircle className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Connection Status */}
      {!isConnected && (
        <div className="bg-red-600/20 border-t border-red-600/30 p-3">
          <div className="flex items-center space-x-2 text-red-400 text-sm">
            <Clock className="w-4 h-4" />
            <span>Reconnecting...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CollaborationPanel;