import React, { useState, useEffect } from 'react';
import { usePLCStore } from '../../store/plcStore';
import { IOModule, IOChannel, PLCTag } from '../../types/plc';
import { 
  Cpu, 
  Zap, 
  Circle, 
  AlertTriangle, 
  CheckCircle2,
  Cable,
  Settings,
  Link,
  Unlink,
  Plus,
  Trash2,
  RefreshCw,
  Edit,
  Save,
  Search,
  Filter,
  HelpCircle,
  Download,
  Upload
} from 'lucide-react';

const IOMapping: React.FC = () => {
  const { currentProject, updateTagIOMapping, updateTag } = usePLCStore();
  const [selectedSlot, setSelectedSlot] = useState<number | null>(null);
  const [draggedTag, setDraggedTag] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterScope, setFilterScope] = useState<string>('all');
  const [showModuleConfig, setShowModuleConfig] = useState(false);
  const [selectedModule, setSelectedModule] = useState<IOModule | null>(null);
  const [showTagDetails, setShowTagDetails] = useState(false);
  const [selectedTag, setSelectedTag] = useState<PLCTag | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [modules, setModules] = useState<IOModule[]>([]);

  const target = currentProject?.targets[0];

  // Initialize modules from target
  useEffect(() => {
    if (target?.ioModules) {
      setModules(target.ioModules);
    }
  }, [target]);

  // Filter tags based on search and scope
  const filteredTags = currentProject?.globalTags
    .filter(tag => {
      const matchesSearch = tag.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesScope = filterScope === 'all' || tag.scope === filterScope;
      return matchesSearch && matchesScope;
    }) || [];

  // Simulate refreshing I/O status
  const handleRefreshIO = () => {
    setRefreshing(true);
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };

  useEffect(() => {
    if (refreshing) {
      const timer = setTimeout(() => {
        setRefreshing(false);
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [refreshing]);

  const getChannelStatusColor = (status: string) => {
    switch (status) {
      case 'ok': return 'text-green-400';
      case 'fault': return 'text-red-400';
      case 'forced': return 'text-amber-400';
      default: return 'text-gray-400';
    }
  };

  const getChannelStatusIcon = (status: string) => {
    switch (status) {
      case 'ok': return <CheckCircle2 className="w-3 h-3" />;
      case 'fault': return <AlertTriangle className="w-3 h-3" />;
      case 'forced': return <Circle className="w-3 h-3" />;
      default: return <Circle className="w-3 h-3" />;
    }
  };

  const handleTagDrop = (channelId: string, tagName: string) => {
    const tag = currentProject?.globalTags.find(t => t.name === tagName);
    if (!tag) return;
    
    let moduleSlot = 0;
    let channelInfo: IOChannel | undefined;
    
    // Find the channel and its module
    for (const module of modules) {
      const channel = module.channels.find(c => c.id === channelId);
      if (channel) {
        moduleSlot = module.slot;
        channelInfo = channel;
        break;
      }
    }
    
    if (!channelInfo) return;
    
    // Update tag's IO mapping
    updateTagIOMapping(tag.id, {
      slot: moduleSlot,
      channel: channelInfo.channel,
      physicalAddress: channelInfo.physicalAddress,
      deviceType: channelInfo.type,
      status: 'connected',
      terminalBlock: channelInfo.terminalBlock
    });
    
    // Also update the channel to reference this tag
    const updatedModules = modules.map(module => {
      if (module.slot === moduleSlot) {
        return {
          ...module,
          channels: module.channels.map(ch => {
            if (ch.id === channelId) {
              return {
                ...ch,
                tag: tag.name,
                value: tag.value
              };
            }
            return ch;
          })
        };
      }
      return module;
    });
    
    setModules(updatedModules);
  };

  const handleUnlinkTag = (channelId: string, tagName: string) => {
    const tag = currentProject?.globalTags.find(t => t.name === tagName);
    if (!tag) return;
    
    // Remove the IO mapping from the tag
    updateTagIOMapping(tag.id, undefined);
    
    // Also update the channel to remove the tag reference
    const updatedModules = modules.map(module => {
      return {
        ...module,
        channels: module.channels.map(ch => {
          if (ch.id === channelId) {
            return {
              ...ch,
              tag: undefined,
              value: undefined
            };
          }
          return ch;
        })
      };
    });
    
    setModules(updatedModules);
  };

  const handleEditTag = (tag: PLCTag) => {
    setSelectedTag(tag);
    setShowTagDetails(true);
  };

  const handleSaveTagDetails = () => {
    if (!selectedTag) return;
    
    // Update the tag
    updateTag(selectedTag.id, selectedTag);
    setShowTagDetails(false);
  };

  const handleModuleClick = (module: IOModule) => {
    setSelectedModule(module);
    setShowModuleConfig(true);
  };

  const handleAddModule = () => {
    const newModule: IOModule = {
      id: `module-${Date.now()}`,
      slot: modules.length + 1,
      type: 'DI',
      partNumber: 'New Module',
      status: 'ok',
      channels: Array.from({ length: 8 }, (_, i) => ({
        id: `ch-new-${i}`,
        channel: i,
        type: 'DI',
        status: 'ok',
        physicalAddress: `I:${modules.length + 1}/${i}`
      }))
    };
    
    setModules([...modules, newModule]);
  };

  const renderIOModule = (module: IOModule) => (
    <div 
      key={module.id} 
      className={`bg-gray-800 rounded-lg border ${selectedModule?.id === module.id ? 'border-blue-500' : 'border-gray-700'} overflow-hidden cursor-pointer transition-all hover:border-blue-500/50`}
      onClick={() => handleModuleClick(module)}
    >
      <div className="bg-gray-700 p-3 border-b border-gray-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Cpu className="w-5 h-5 text-blue-400" />
            <div>
              <h3 className="text-white font-semibold">Slot {module.slot}</h3>
              <p className="text-xs text-gray-400">{module.partNumber}</p>
            </div>
          </div>
          <div className={`px-2 py-1 rounded text-xs ${
            module.status === 'ok' ? 'bg-green-600/20 text-green-400' :
            module.status === 'fault' ? 'bg-red-600/20 text-red-400' :
            'bg-gray-600/20 text-gray-400'
          }`}>
            {module.status.toUpperCase()}
          </div>
        </div>
      </div>
      
      <div className="p-3">
        <div className="grid grid-cols-4 gap-2">
          {module.channels.map(channel => (
            <div
              key={channel.id}
              className={`relative p-2 rounded-lg border-2 border-dashed transition-all cursor-pointer ${
                channel.tag 
                  ? 'border-green-500 bg-green-500/10' 
                  : 'border-gray-600 hover:border-blue-500'
              }`}
              onDragOver={(e) => e.preventDefault()}
              onDrop={(e) => {
                e.preventDefault();
                if (draggedTag) {
                  handleTagDrop(channel.id, draggedTag);
                  setDraggedTag(null);
                }
              }}
            >
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <span className={`text-xs font-mono ${getChannelStatusColor(channel.status)}`}>
                    {channel.type}{channel.channel}
                  </span>
                  <span className={`ml-1 ${getChannelStatusColor(channel.status)}`}>
                    {getChannelStatusIcon(channel.status)}
                  </span>
                </div>
                
                {channel.tag ? (
                  <div className="space-y-1">
                    <div className="text-xs text-white font-medium truncate max-w-20">
                      {channel.tag}
                    </div>
                    <div className="text-xs text-gray-400">
                      {channel.value?.toString() || 'N/A'}
                    </div>
                    {channel.terminalBlock && (
                      <div className="text-xs text-blue-400">
                        TB{channel.terminalBlock}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-xs text-gray-500">
                    Drop tag here
                  </div>
                )}
              </div>
              
              {channel.tag && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleUnlinkTag(channel.id, channel.tag!);
                  }}
                  className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white text-xs"
                >
                  ×
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderTagList = () => (
    <div className="bg-gray-800 rounded-lg border border-gray-700">
      <div className="bg-gray-700 p-3 border-b border-gray-600">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-white font-semibold flex items-center">
            <Cable className="w-4 h-4 mr-2" />
            Available Tags
          </h3>
          <button
            onClick={() => {
              const scope = prompt('Enter tag scope (INPUT, OUTPUT, GLOBAL):');
              if (scope && ['INPUT', 'OUTPUT', 'GLOBAL'].includes(scope)) {
                const name = prompt('Enter tag name:');
                if (name) {
                  const tag = {
                    name,
                    type: 'BOOL',
                    value: false,
                    scope: scope as 'INPUT' | 'OUTPUT' | 'GLOBAL',
                    description: `New ${scope.toLowerCase()} tag`
                  };
                  
                  if (currentProject) {
                    const { createTag } = usePLCStore.getState();
                    createTag(tag);
                  }
                }
              }
            }}
            className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded transition-colors"
            title="Add New Tag"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded pl-8 pr-2 py-1 text-sm text-white"
            />
          </div>
          
          <select
            value={filterScope}
            onChange={(e) => setFilterScope(e.target.value)}
            className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-sm text-white"
          >
            <option value="all">All</option>
            <option value="INPUT">Input</option>
            <option value="OUTPUT">Output</option>
            <option value="GLOBAL">Global</option>
          </select>
        </div>
      </div>
      
      <div className="p-3 space-y-2 max-h-96 overflow-y-auto">
        {filteredTags.length === 0 ? (
          <div className="text-center py-4 text-gray-400">
            <p className="text-sm">No tags found</p>
            <p className="text-xs mt-1">Try adjusting your search or filter</p>
          </div>
        ) : (
          filteredTags.map(tag => (
            <div
              key={tag.id}
              draggable
              onDragStart={() => setDraggedTag(tag.name)}
              className={`p-2 rounded border cursor-move transition-all ${
                tag.ioMapping 
                  ? 'border-green-500 bg-green-500/10' 
                  : 'border-gray-600 hover:border-blue-500 bg-gray-700/50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-white text-sm font-medium">{tag.name}</span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    tag.scope === 'INPUT' ? 'bg-blue-600/20 text-blue-400' :
                    tag.scope === 'OUTPUT' ? 'bg-green-600/20 text-green-400' :
                    'bg-gray-600/20 text-gray-400'
                  }`}>
                    {tag.scope}
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  {tag.ioMapping ? (
                    <Link className="w-4 h-4 text-green-400" />
                  ) : (
                    <Unlink className="w-4 h-4 text-gray-400" />
                  )}
                  <span className="text-xs text-gray-400">{tag.type}</span>
                  <button
                    onClick={() => handleEditTag(tag)}
                    className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded transition-colors"
                    title="Edit Tag"
                  >
                    <Edit className="w-3 h-3" />
                  </button>
                </div>
              </div>
              
              {tag.ioMapping && (
                <div className="mt-1 text-xs text-gray-400">
                  Slot {tag.ioMapping.slot}, Ch {tag.ioMapping.channel} → {tag.ioMapping.physicalAddress}
                </div>
              )}
            </div>
          ))
        )}
      </div>
      
      <div className="p-3 border-t border-gray-700">
        <button
          onClick={() => {
            const scope = prompt('Enter tag scope (INPUT, OUTPUT, GLOBAL):');
            if (scope && ['INPUT', 'OUTPUT', 'GLOBAL'].includes(scope)) {
              const name = prompt('Enter tag name:');
              if (name) {
                const tag = {
                  name,
                  type: 'BOOL',
                  value: false,
                  scope: scope as 'INPUT' | 'OUTPUT' | 'GLOBAL',
                  description: `New ${scope.toLowerCase()} tag`
                };
                
                if (currentProject) {
                  const { createTag } = usePLCStore.getState();
                  createTag(tag);
                }
              }
            }
          }}
          className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add New Tag</span>
        </button>
      </div>
    </div>
  );

  const renderModuleConfig = () => {
    if (!selectedModule) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-2xl">
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">I/O Module Configuration</h3>
            <button
              onClick={() => setShowModuleConfig(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="p-6 space-y-6">
            <div className="grid grid-cols-2 gap-6">
              <div>
                <label className="block text-sm text-gray-400 mb-2">Module Type</label>
                <select
                  value={selectedModule.type}
                  onChange={(e) => {
                    setSelectedModule({
                      ...selectedModule,
                      type: e.target.value as IOModule['type']
                    });
                  }}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                >
                  <option value="DI">Digital Input (DI)</option>
                  <option value="DO">Digital Output (DO)</option>
                  <option value="AI">Analog Input (AI)</option>
                  <option value="AO">Analog Output (AO)</option>
                  <option value="MIXED">Mixed I/O</option>
                </select>
              </div>
              <div>
                <label className="block text-sm text-gray-400 mb-2">Part Number</label>
                <input
                  type="text"
                  value={selectedModule.partNumber}
                  onChange={(e) => {
                    setSelectedModule({
                      ...selectedModule,
                      partNumber: e.target.value
                    });
                  }}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                />
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm text-gray-400">Channels</label>
                <button
                  onClick={() => {
                    const newChannel: IOChannel = {
                      id: `ch-${selectedModule.id}-${selectedModule.channels.length}`,
                      channel: selectedModule.channels.length,
                      type: selectedModule.type === 'MIXED' ? 'DI' : selectedModule.type,
                      status: 'ok',
                      physicalAddress: `${selectedModule.type === 'DO' || selectedModule.type === 'AO' ? 'O' : 'I'}:${selectedModule.slot}/${selectedModule.channels.length}`
                    };
                    
                    setSelectedModule({
                      ...selectedModule,
                      channels: [...selectedModule.channels, newChannel]
                    });
                  }}
                  className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded transition-colors"
                >
                  Add Channel
                </button>
              </div>
              <div className="bg-gray-900 rounded-lg border border-gray-700 overflow-hidden">
                <table className="w-full text-sm">
                  <thead className="bg-gray-800">
                    <tr>
                      <th className="px-4 py-2 text-left text-gray-400">Channel</th>
                      <th className="px-4 py-2 text-left text-gray-400">Type</th>
                      <th className="px-4 py-2 text-left text-gray-400">Address</th>
                      <th className="px-4 py-2 text-left text-gray-400">Terminal</th>
                      <th className="px-4 py-2 text-left text-gray-400">Tag</th>
                      <th className="px-4 py-2 text-left text-gray-400">Status</th>
                      <th className="px-4 py-2 text-left text-gray-400">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {selectedModule.channels.map((channel, index) => (
                      <tr key={channel.id} className="border-t border-gray-700">
                        <td className="px-4 py-2 text-white">{channel.channel}</td>
                        <td className="px-4 py-2 text-white">
                          <select
                            value={channel.type}
                            onChange={(e) => {
                              const updatedChannels = [...selectedModule.channels];
                              updatedChannels[index] = {
                                ...channel,
                                type: e.target.value as IOChannel['type']
                              };
                              setSelectedModule({
                                ...selectedModule,
                                channels: updatedChannels
                              });
                            }}
                            className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs"
                          >
                            <option value="DI">DI</option>
                            <option value="DO">DO</option>
                            <option value="AI">AI</option>
                            <option value="AO">AO</option>
                          </select>
                        </td>
                        <td className="px-4 py-2 text-white">
                          <input
                            type="text"
                            value={channel.physicalAddress}
                            onChange={(e) => {
                              const updatedChannels = [...selectedModule.channels];
                              updatedChannels[index] = {
                                ...channel,
                                physicalAddress: e.target.value
                              };
                              setSelectedModule({
                                ...selectedModule,
                                channels: updatedChannels
                              });
                            }}
                            className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs w-24"
                          />
                        </td>
                        <td className="px-4 py-2 text-white">
                          <input
                            type="text"
                            value={channel.terminalBlock || ''}
                            onChange={(e) => {
                              const updatedChannels = [...selectedModule.channels];
                              updatedChannels[index] = {
                                ...channel,
                                terminalBlock: e.target.value
                              };
                              setSelectedModule({
                                ...selectedModule,
                                channels: updatedChannels
                              });
                            }}
                            className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs w-16"
                            placeholder="TB#"
                          />
                        </td>
                        <td className="px-4 py-2 text-white">
                          {channel.tag || '-'}
                        </td>
                        <td className="px-4 py-2">
                          <select
                            value={channel.status}
                            onChange={(e) => {
                              const updatedChannels = [...selectedModule.channels];
                              updatedChannels[index] = {
                                ...channel,
                                status: e.target.value as IOChannel['status']
                              };
                              setSelectedModule({
                                ...selectedModule,
                                channels: updatedChannels
                              });
                            }}
                            className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs"
                          >
                            <option value="ok">OK</option>
                            <option value="fault">FAULT</option>
                            <option value="forced">FORCED</option>
                          </select>
                        </td>
                        <td className="px-4 py-2">
                          <div className="flex items-center space-x-1">
                            <button
                              onClick={() => {
                                const updatedChannels = [...selectedModule.channels];
                                updatedChannels.splice(index, 1);
                                setSelectedModule({
                                  ...selectedModule,
                                  channels: updatedChannels
                                });
                              }}
                              className="p-1 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded transition-colors"
                              title="Delete Channel"
                            >
                              <Trash2 className="w-3 h-3" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            
            <div className="pt-4 border-t border-gray-700 flex justify-between">
              <button
                className="flex items-center space-x-2 text-red-400 hover:text-red-300"
                onClick={() => {
                  if (confirm('Are you sure you want to delete this module?')) {
                    setModules(modules.filter(m => m.id !== selectedModule.id));
                    setShowModuleConfig(false);
                  }
                }}
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete Module</span>
              </button>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowModuleConfig(false)}
                  className="text-gray-400 hover:text-white px-4 py-2"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // Update the module in the modules list
                    setModules(modules.map(m => 
                      m.id === selectedModule.id ? selectedModule : m
                    ));
                    setShowModuleConfig(false);
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderTagDetails = () => {
    if (!selectedTag) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md">
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Tag Details</h3>
            <button
              onClick={() => setShowTagDetails(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Tag Name</label>
              <input
                type="text"
                value={selectedTag.name}
                onChange={(e) => setSelectedTag({...selectedTag, name: e.target.value})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Data Type</label>
              <select
                value={selectedTag.type}
                onChange={(e) => setSelectedTag({...selectedTag, type: e.target.value as any})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="BOOL">BOOL</option>
                <option value="INT">INT</option>
                <option value="DINT">DINT</option>
                <option value="REAL">REAL</option>
                <option value="TIME">TIME</option>
                <option value="STRING">STRING</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Scope</label>
              <select
                value={selectedTag.scope}
                onChange={(e) => setSelectedTag({...selectedTag, scope: e.target.value as any})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="INPUT">INPUT</option>
                <option value="OUTPUT">OUTPUT</option>
                <option value="LOCAL">LOCAL</option>
                <option value="GLOBAL">GLOBAL</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Description</label>
              <textarea
                value={selectedTag.description || ''}
                onChange={(e) => setSelectedTag({...selectedTag, description: e.target.value})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white h-20 resize-none"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedTag.safetyRated || false}
                onChange={(e) => setSelectedTag({...selectedTag, safetyRated: e.target.checked})}
                className="rounded"
              />
              <label className="text-sm text-white">Safety Rated</label>
            </div>
            
            {selectedTag.safetyRated && (
              <div>
                <label className="block text-sm text-gray-400 mb-2">SIL Level</label>
                <select
                  value={selectedTag.silLevel || 'SIL1'}
                  onChange={(e) => setSelectedTag({...selectedTag, silLevel: e.target.value as any})}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                >
                  <option value="SIL1">SIL1</option>
                  <option value="SIL2">SIL2</option>
                  <option value="SIL3">SIL3</option>
                </select>
              </div>
            )}
            
            <div className="pt-4 border-t border-gray-700 flex justify-between">
              <button
                className="flex items-center space-x-2 text-red-400 hover:text-red-300"
                onClick={() => {
                  if (confirm('Are you sure you want to delete this tag?')) {
                    const { deleteTag } = usePLCStore.getState();
                    deleteTag(selectedTag.id);
                    setShowTagDetails(false);
                  }
                }}
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete Tag</span>
              </button>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowTagDetails(false)}
                  className="text-gray-400 hover:text-white px-4 py-2"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveTagDetails}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (!currentProject) {
    return (
      <div className="h-full flex items-center justify-center text-gray-400">
        <div className="text-center">
          <Cable className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No project loaded</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Cable className="w-5 h-5 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">I/O Mapping</h2>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefreshIO}
              className={`p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors ${refreshing ? 'animate-spin text-blue-400' : ''}`}
              title="Refresh I/O Status"
              disabled={refreshing}
            >
              <RefreshCw className="w-4 h-4" />
            </button>
            <button
              onClick={() => {
                // Scan for new I/O modules
                setRefreshing(true);
                setTimeout(() => {
                  // Simulate finding a new module
                  const newModule: IOModule = {
                    id: `module-${Date.now()}`,
                    slot: modules.length + 1,
                    type: 'DI',
                    partNumber: 'Auto-Detected Module',
                    status: 'ok',
                    channels: Array.from({ length: 8 }, (_, i) => ({
                      id: `ch-auto-${i}`,
                      channel: i,
                      type: 'DI',
                      status: 'ok',
                      physicalAddress: `I:${modules.length + 1}/${i}`
                    }))
                  };
                  
                  setModules([...modules, newModule]);
                  setRefreshing(false);
                }, 2000);
              }}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Scan for I/O"
            >
              <Search className="w-4 h-4" />
            </button>
            <button
              onClick={handleAddModule}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add Module</span>
            </button>
          </div>
        </div>
        
        <p className="text-gray-400 text-sm">
          Drag tags from the list to I/O channels to create physical mappings. Click on a module to configure it.
        </p>
      </div>
      
      {/* Main Content */}
      <div className="flex-1 p-6 overflow-hidden">
        <div className="grid grid-cols-3 gap-6 h-full">
          <div className="col-span-2 space-y-4 overflow-y-auto pr-2">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold text-white">Hardware Configuration</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => {
                    alert('I/O configuration exported');
                  }}
                  className="flex items-center space-x-1 text-xs bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded transition-colors"
                >
                  <Upload className="w-3 h-3" />
                  <span>Import</span>
                </button>
                <button
                  onClick={() => {
                    alert('I/O configuration exported');
                  }}
                  className="flex items-center space-x-1 text-xs bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded transition-colors"
                >
                  <Download className="w-3 h-3" />
                  <span>Export</span>
                </button>
              </div>
            </div>
            
            {modules.length === 0 ? (
              <div className="text-center py-12 text-gray-400 bg-gray-800/50 rounded-lg border border-gray-700">
                <Cpu className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No I/O modules configured</p>
                <p className="text-sm">Add I/O modules to your PLC target</p>
                <button
                  onClick={handleAddModule}
                  className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
                >
                  Add I/O Module
                </button>
              </div>
            ) : (
              modules.map(renderIOModule)
            )}
          </div>
          
          <div className="overflow-y-auto">
            {renderTagList()}
          </div>
        </div>
      </div>
      
      {/* Status Bar */}
      <div className="bg-gray-800 border-t border-gray-700 p-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span className="text-gray-400">Modules:</span>
            <span className="text-white">{modules.length}</span>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-gray-400">Mapped Tags:</span>
            <span className="text-white">
              {currentProject.globalTags.filter(t => t.ioMapping).length} / {currentProject.globalTags.length}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <HelpCircle className="w-4 h-4 text-gray-400" />
            <span className="text-gray-400">
              Drag tags to channels to map them
            </span>
          </div>
        </div>
      </div>
      
      {showModuleConfig && renderModuleConfig()}
      {showTagDetails && renderTagDetails()}
    </div>
  );
};

// X icon component
const X = ({ className }: { className?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M18 6 6 18"></path>
    <path d="m6 6 12 12"></path>
  </svg>
);

export default IOMapping;