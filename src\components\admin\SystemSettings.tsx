import React, { useState, useEffect } from 'react';
import { 
  Settings, 
  Save, 
  RefreshCw, 
  Mail, 
  Database, 
  HardDrive, 
  FileText, 
  Bell, 
  Clock, 
  Server, 
  Globe, 
  Shield, 
  X, 
  CheckCircle2, 
  AlertTriangle,
  Send,
  Download,
  Upload,
  Trash2,
  Play,
  Pause,
  Info,
  Edit
} from 'lucide-react';

interface SystemSettingsData {
  general: {
    siteName: string;
    siteUrl: string;
    adminEmail: string;
    companyName: string;
    timezone: string;
    dateFormat: string;
    language: string;
  };
  email: {
    smtpServer: string;
    smtpPort: number;
    smtpUsername: string;
    smtpPassword: string;
    smtpSecurity: 'none' | 'ssl' | 'tls';
    fromEmail: string;
    fromName: string;
    enabled: boolean;
  };
  storage: {
    provider: 'local' | 's3' | 'azure' | 'gcp';
    localPath?: string;
    s3Bucket?: string;
    s3Region?: string;
    s3AccessKey?: string;
    s3SecretKey?: string;
    azureContainer?: string;
    azureConnectionString?: string;
    gcpBucket?: string;
    gcpCredentials?: string;
  };
  database: {
    type: 'postgresql' | 'mysql' | 'sqlite';
    host: string;
    port: number;
    name: string;
    username: string;
    password: string;
    maxConnections: number;
    sslEnabled: boolean;
  };
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    file: boolean;
    filePath: string;
    console: boolean;
    database: boolean;
    retention: number;
  };
  notifications: {
    emailEnabled: boolean;
    inAppEnabled: boolean;
    pushEnabled: boolean;
    slackEnabled: boolean;
    slackWebhook?: string;
    events: {
      userCreated: boolean;
      userLogin: boolean;
      deploymentSuccess: boolean;
      deploymentFailure: boolean;
      securityAlert: boolean;
    };
  };
  maintenance: {
    backupEnabled: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
    backupTime: string;
    backupRetention: number;
    cleanupEnabled: boolean;
    cleanupFrequency: 'daily' | 'weekly' | 'monthly';
    cleanupTime: string;
    cleanupOlderThan: number;
  };
}

const SystemSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isTestingEmail, setIsTestingEmail] = useState(false);
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [showBackupModal, setShowBackupModal] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [settings, setSettings] = useState<SystemSettingsData>({
    general: {
      siteName: 'LUREON Industrial Automation IDE',
      siteUrl: 'https://app.lureon.com',
      adminEmail: '<EMAIL>',
      companyName: 'LUREON Technologies',
      timezone: 'UTC',
      dateFormat: 'YYYY-MM-DD',
      language: 'en'
    },
    email: {
      smtpServer: 'smtp.example.com',
      smtpPort: 587,
      smtpUsername: '<EMAIL>',
      smtpPassword: '••••••••••••',
      smtpSecurity: 'tls',
      fromEmail: '<EMAIL>',
      fromName: 'LUREON Notifications',
      enabled: true
    },
    storage: {
      provider: 's3',
      s3Bucket: 'lureon-storage',
      s3Region: 'us-west-2',
      s3AccessKey: 'AKIAXXXXXXXXXXXXXXXX',
      s3SecretKey: '••••••••••••••••••••••••••••••••••',
      localPath: '/var/lureon/storage'
    },
    database: {
      type: 'postgresql',
      host: 'db.lureon.internal',
      port: 5432,
      name: 'lureon_production',
      username: 'lureon_app',
      password: '••••••••••••',
      maxConnections: 20,
      sslEnabled: true
    },
    logging: {
      level: 'info',
      file: true,
      filePath: '/var/log/lureon',
      console: true,
      database: true,
      retention: 90
    },
    notifications: {
      emailEnabled: true,
      inAppEnabled: true,
      pushEnabled: false,
      slackEnabled: true,
      slackWebhook: '*****************************************************************************',
      events: {
        userCreated: true,
        userLogin: false,
        deploymentSuccess: false,
        deploymentFailure: true,
        securityAlert: true
      }
    },
    maintenance: {
      backupEnabled: true,
      backupFrequency: 'daily',
      backupTime: '02:00',
      backupRetention: 30,
      cleanupEnabled: true,
      cleanupFrequency: 'weekly',
      cleanupTime: '03:00',
      cleanupOlderThan: 90
    }
  });

  // Simulate loading settings
  useEffect(() => {
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  }, []);

  const handleSaveSettings = () => {
    setIsSaving(true);
    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      // Show success message
      alert('Settings saved successfully');
    }, 1500);
  };

  const handleTestEmail = () => {
    setIsTestingEmail(true);
    // Simulate API call
    setTimeout(() => {
      setIsTestingEmail(false);
      // Show success message
      alert('Test email sent successfully');
    }, 2000);
  };

  const handleTestDatabaseConnection = () => {
    setIsTestingConnection(true);
    // Simulate API call
    setTimeout(() => {
      setIsTestingConnection(false);
      // Show success message
      alert('Database connection successful');
    }, 1500);
  };

  const handleBackupNow = () => {
    // Simulate backup process
    alert('Backup process started. This may take a few minutes.');
    setShowBackupModal(false);
  };

  const handleRestoreBackup = () => {
    // Simulate restore process
    alert('Restore process started. This may take a few minutes.');
    setShowRestoreModal(false);
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">General Settings</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-2">Site Name</label>
            <input
              type="text"
              value={settings.general.siteName}
              onChange={(e) => setSettings({
                ...settings,
                general: {
                  ...settings.general,
                  siteName: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">Site URL</label>
            <input
              type="text"
              value={settings.general.siteUrl}
              onChange={(e) => setSettings({
                ...settings,
                general: {
                  ...settings.general,
                  siteUrl: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">Admin Email</label>
            <input
              type="email"
              value={settings.general.adminEmail}
              onChange={(e) => setSettings({
                ...settings,
                general: {
                  ...settings.general,
                  adminEmail: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">Company Name</label>
            <input
              type="text"
              value={settings.general.companyName}
              onChange={(e) => setSettings({
                ...settings,
                general: {
                  ...settings.general,
                  companyName: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Localization</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-2">Timezone</label>
            <select
              value={settings.general.timezone}
              onChange={(e) => setSettings({
                ...settings,
                general: {
                  ...settings.general,
                  timezone: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="UTC">UTC</option>
              <option value="America/New_York">Eastern Time (ET)</option>
              <option value="America/Chicago">Central Time (CT)</option>
              <option value="America/Denver">Mountain Time (MT)</option>
              <option value="America/Los_Angeles">Pacific Time (PT)</option>
              <option value="Europe/London">London (GMT)</option>
              <option value="Europe/Paris">Paris (CET)</option>
              <option value="Asia/Tokyo">Tokyo (JST)</option>
            </select>
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">Date Format</label>
            <select
              value={settings.general.dateFormat}
              onChange={(e) => setSettings({
                ...settings,
                general: {
                  ...settings.general,
                  dateFormat: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
              <option value="DD.MM.YYYY">DD.MM.YYYY</option>
            </select>
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">Language</label>
            <select
              value={settings.general.language}
              onChange={(e) => setSettings({
                ...settings,
                general: {
                  ...settings.general,
                  language: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="en">English</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="es">Spanish</option>
              <option value="ja">Japanese</option>
              <option value="zh">Chinese</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderEmailSettings = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Email Server</h3>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded text-xs ${settings.email.enabled ? 'bg-green-500/20 text-green-400 border border-green-500/30' : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'}`}>
              {settings.email.enabled ? 'Enabled' : 'Disabled'}
            </span>
            <button
              onClick={() => setSettings({
                ...settings,
                email: {
                  ...settings.email,
                  enabled: !settings.email.enabled
                }
              })}
              className={`p-1 rounded ${settings.email.enabled ? 'text-green-400 hover:bg-green-400/10' : 'text-gray-400 hover:bg-gray-700'}`}
            >
              {settings.email.enabled ? <CheckCircle2 className="w-5 h-5" /> : <X className="w-5 h-5" />}
            </button>
          </div>
        </div>
        
        <div className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-2">SMTP Server</label>
            <input
              type="text"
              value={settings.email.smtpServer}
              onChange={(e) => setSettings({
                ...settings,
                email: {
                  ...settings.email,
                  smtpServer: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-300 mb-2">SMTP Port</label>
              <input
                type="number"
                value={settings.email.smtpPort}
                onChange={(e) => setSettings({
                  ...settings,
                  email: {
                    ...settings.email,
                    smtpPort: parseInt(e.target.value)
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-gray-300 mb-2">Security</label>
              <select
                value={settings.email.smtpSecurity}
                onChange={(e) => setSettings({
                  ...settings,
                  email: {
                    ...settings.email,
                    smtpSecurity: e.target.value as any
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="none">None</option>
                <option value="ssl">SSL</option>
                <option value="tls">TLS</option>
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">SMTP Username</label>
            <input
              type="text"
              value={settings.email.smtpUsername}
              onChange={(e) => setSettings({
                ...settings,
                email: {
                  ...settings.email,
                  smtpUsername: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">SMTP Password</label>
            <input
              type="password"
              value={settings.email.smtpPassword}
              onChange={(e) => setSettings({
                ...settings,
                email: {
                  ...settings.email,
                  smtpPassword: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Email Settings</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-2">From Email</label>
            <input
              type="email"
              value={settings.email.fromEmail}
              onChange={(e) => setSettings({
                ...settings,
                email: {
                  ...settings.email,
                  fromEmail: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">From Name</label>
            <input
              type="text"
              value={settings.email.fromName}
              onChange={(e) => setSettings({
                ...settings,
                email: {
                  ...settings.email,
                  fromName: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="pt-4">
            <button
              onClick={handleTestEmail}
              disabled={isTestingEmail || !settings.email.enabled}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors"
            >
              {isTestingEmail ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
              <span>Send Test Email</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderStorageSettings = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Storage Provider</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-2">Provider</label>
            <select
              value={settings.storage.provider}
              onChange={(e) => setSettings({
                ...settings,
                storage: {
                  ...settings.storage,
                  provider: e.target.value as any
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="local">Local Storage</option>
              <option value="s3">Amazon S3</option>
              <option value="azure">Azure Blob Storage</option>
              <option value="gcp">Google Cloud Storage</option>
            </select>
          </div>
          
          {settings.storage.provider === 'local' && (
            <div>
              <label className="block text-gray-300 mb-2">Local Storage Path</label>
              <input
                type="text"
                value={settings.storage.localPath}
                onChange={(e) => setSettings({
                  ...settings,
                  storage: {
                    ...settings.storage,
                    localPath: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
          
          {settings.storage.provider === 's3' && (
            <>
              <div>
                <label className="block text-gray-300 mb-2">S3 Bucket</label>
                <input
                  type="text"
                  value={settings.storage.s3Bucket}
                  onChange={(e) => setSettings({
                    ...settings,
                    storage: {
                      ...settings.storage,
                      s3Bucket: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">S3 Region</label>
                <input
                  type="text"
                  value={settings.storage.s3Region}
                  onChange={(e) => setSettings({
                    ...settings,
                    storage: {
                      ...settings.storage,
                      s3Region: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">S3 Access Key</label>
                <input
                  type="text"
                  value={settings.storage.s3AccessKey}
                  onChange={(e) => setSettings({
                    ...settings,
                    storage: {
                      ...settings.storage,
                      s3AccessKey: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">S3 Secret Key</label>
                <input
                  type="password"
                  value={settings.storage.s3SecretKey}
                  onChange={(e) => setSettings({
                    ...settings,
                    storage: {
                      ...settings.storage,
                      s3SecretKey: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </>
          )}
          
          {settings.storage.provider === 'azure' && (
            <>
              <div>
                <label className="block text-gray-300 mb-2">Azure Container</label>
                <input
                  type="text"
                  value={settings.storage.azureContainer}
                  onChange={(e) => setSettings({
                    ...settings,
                    storage: {
                      ...settings.storage,
                      azureContainer: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Azure Connection String</label>
                <input
                  type="password"
                  value={settings.storage.azureConnectionString}
                  onChange={(e) => setSettings({
                    ...settings,
                    storage: {
                      ...settings.storage,
                      azureConnectionString: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </>
          )}
          
          {settings.storage.provider === 'gcp' && (
            <>
              <div>
                <label className="block text-gray-300 mb-2">GCP Bucket</label>
                <input
                  type="text"
                  value={settings.storage.gcpBucket}
                  onChange={(e) => setSettings({
                    ...settings,
                    storage: {
                      ...settings.storage,
                      gcpBucket: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">GCP Credentials (JSON)</label>
                <textarea
                  value={settings.storage.gcpCredentials}
                  onChange={(e) => setSettings({
                    ...settings,
                    storage: {
                      ...settings.storage,
                      gcpCredentials: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-32 resize-none"
                  placeholder="Paste GCP service account JSON here"
                ></textarea>
              </div>
            </>
          )}
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Storage Management</h3>
        <div className="space-y-4">
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-white font-medium">Storage Usage</h4>
              <button className="text-blue-400 hover:text-blue-300 text-sm">Refresh</button>
            </div>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-400">Projects</span>
                  <span className="text-white">1.2 GB / 10 GB</span>
                </div>
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '12%' }}></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-400">Templates</span>
                  <span className="text-white">0.3 GB / 5 GB</span>
                </div>
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{ width: '6%' }}></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-400">Backups</span>
                  <span className="text-white">2.5 GB / 20 GB</span>
                </div>
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '12.5%' }}></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-400">Other</span>
                  <span className="text-white">0.8 GB / 5 GB</span>
                </div>
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '16%' }}></div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => setShowBackupModal(true)}
              className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Backup Now</span>
            </button>
            
            <button
              onClick={() => setShowRestoreModal(true)}
              className="flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Upload className="w-4 h-4" />
              <span>Restore Backup</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDatabaseSettings = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Database Configuration</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-2">Database Type</label>
            <select
              value={settings.database.type}
              onChange={(e) => setSettings({
                ...settings,
                database: {
                  ...settings.database,
                  type: e.target.value as any
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="postgresql">PostgreSQL</option>
              <option value="mysql">MySQL</option>
              <option value="sqlite">SQLite</option>
            </select>
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">Host</label>
            <input
              type="text"
              value={settings.database.host}
              onChange={(e) => setSettings({
                ...settings,
                database: {
                  ...settings.database,
                  host: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-300 mb-2">Port</label>
              <input
                type="number"
                value={settings.database.port}
                onChange={(e) => setSettings({
                  ...settings,
                  database: {
                    ...settings.database,
                    port: parseInt(e.target.value)
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-gray-300 mb-2">Database Name</label>
              <input
                type="text"
                value={settings.database.name}
                onChange={(e) => setSettings({
                  ...settings,
                  database: {
                    ...settings.database,
                    name: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">Username</label>
            <input
              type="text"
              value={settings.database.username}
              onChange={(e) => setSettings({
                ...settings,
                database: {
                  ...settings.database,
                  username: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">Password</label>
            <input
              type="password"
              value={settings.database.password}
              onChange={(e) => setSettings({
                ...settings,
                database: {
                  ...settings.database,
                  password: e.target.value
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-300 mb-2">Max Connections</label>
              <input
                type="number"
                value={settings.database.maxConnections}
                onChange={(e) => setSettings({
                  ...settings,
                  database: {
                    ...settings.database,
                    maxConnections: parseInt(e.target.value)
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex items-center space-x-2 mt-8">
              <input
                type="checkbox"
                id="sslEnabled"
                checked={settings.database.sslEnabled}
                onChange={(e) => setSettings({
                  ...settings,
                  database: {
                    ...settings.database,
                    sslEnabled: e.target.checked
                  }
                })}
                className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
              />
              <label htmlFor="sslEnabled" className="text-white">Enable SSL</label>
            </div>
          </div>
          
          <div className="pt-4">
            <button
              onClick={handleTestDatabaseConnection}
              disabled={isTestingConnection}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors"
            >
              {isTestingConnection ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Database className="w-4 h-4" />
              )}
              <span>Test Connection</span>
            </button>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Database Maintenance</h3>
        <div className="space-y-4">
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-white font-medium">Database Status</h4>
              <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                Healthy
              </span>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Size</span>
                <span className="text-white">2.3 GB</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Tables</span>
                <span className="text-white">42</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Last Backup</span>
                <span className="text-white">2023-06-15 02:00:00</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Last Vacuum</span>
                <span className="text-white">2023-06-14 03:00:00</span>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <button
              className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Vacuum Database</span>
            </button>
            
            <button
              className="flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Reindex Database</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLoggingSettings = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Logging Configuration</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-gray-300 mb-2">Log Level</label>
            <select
              value={settings.logging.level}
              onChange={(e) => setSettings({
                ...settings,
                logging: {
                  ...settings.logging,
                  level: e.target.value as any
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="debug">Debug</option>
              <option value="info">Info</option>
              <option value="warn">Warning</option>
              <option value="error">Error</option>
            </select>
            <p className="text-xs text-gray-400 mt-1">
              Debug: Most verbose, includes all messages<br />
              Info: Includes informational messages, warnings, and errors<br />
              Warning: Includes only warnings and errors<br />
              Error: Includes only errors
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="logConsole"
              checked={settings.logging.console}
              onChange={(e) => setSettings({
                ...settings,
                logging: {
                  ...settings.logging,
                  console: e.target.checked
                }
              })}
              className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
            />
            <label htmlFor="logConsole" className="text-white">Log to console</label>
          </div>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="logFile"
              checked={settings.logging.file}
              onChange={(e) => setSettings({
                ...settings,
                logging: {
                  ...settings.logging,
                  file: e.target.checked
                }
              })}
              className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
            />
            <label htmlFor="logFile" className="text-white">Log to file</label>
          </div>
          
          {settings.logging.file && (
            <div>
              <label className="block text-gray-300 mb-2">Log File Path</label>
              <input
                type="text"
                value={settings.logging.filePath}
                onChange={(e) => setSettings({
                  ...settings,
                  logging: {
                    ...settings.logging,
                    filePath: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="logDatabase"
              checked={settings.logging.database}
              onChange={(e) => setSettings({
                ...settings,
                logging: {
                  ...settings.logging,
                  database: e.target.checked
                }
              })}
              className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
            />
            <label htmlFor="logDatabase" className="text-white">Log to database</label>
          </div>
          
          <div>
            <label className="block text-gray-300 mb-2">Log Retention (days)</label>
            <input
              type="number"
              value={settings.logging.retention}
              onChange={(e) => setSettings({
                ...settings,
                logging: {
                  ...settings.logging,
                  retention: parseInt(e.target.value)
                }
              })}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-400 mt-1">Logs older than this will be automatically deleted</p>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Log Management</h3>
        <div className="space-y-4">
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-white font-medium">Log Files</h4>
              <button className="text-blue-400 hover:text-blue-300 text-sm">Refresh</button>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <span className="text-white">application.log</span>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400 text-sm">2.3 MB</span>
                  <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <span className="text-white">error.log</span>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400 text-sm">0.5 MB</span>
                  <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <span className="text-white">access.log</span>
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400 text-sm">4.1 MB</span>
                  <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <button
              className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Download All Logs</span>
            </button>
            
            <button
              className="flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Trash2 className="w-4 h-4" />
              <span>Clear Logs</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Notification Channels</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center space-x-3">
              <Mail className="w-5 h-5 text-blue-400" />
              <div>
                <h4 className="text-white font-medium">Email Notifications</h4>
                <p className="text-sm text-gray-400">Send notifications via email</p>
              </div>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.notifications.emailEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      emailEnabled: e.target.checked
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center space-x-3">
              <Bell className="w-5 h-5 text-purple-400" />
              <div>
                <h4 className="text-white font-medium">In-App Notifications</h4>
                <p className="text-sm text-gray-400">Show notifications within the application</p>
              </div>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.notifications.inAppEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      inAppEnabled: e.target.checked
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center space-x-3">
              <Bell className="w-5 h-5 text-yellow-400" />
              <div>
                <h4 className="text-white font-medium">Push Notifications</h4>
                <p className="text-sm text-gray-400">Send browser push notifications</p>
              </div>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.notifications.pushEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      pushEnabled: e.target.checked
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
            <div className="flex items-center space-x-3">
              <svg className="w-5 h-5 text-blue-400" viewBox="0 0 24 24" fill="currentColor">
                <path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z" />
              </svg>
              <div>
                <h4 className="text-white font-medium">Slack Notifications</h4>
                <p className="text-sm text-gray-400">Send notifications to Slack</p>
              </div>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.notifications.slackEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      slackEnabled: e.target.checked
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          {settings.notifications.slackEnabled && (
            <div>
              <label className="block text-gray-300 mb-2">Slack Webhook URL</label>
              <input
                type="text"
                value={settings.notifications.slackWebhook}
                onChange={(e) => setSettings({
                  ...settings,
                  notifications: {
                    ...settings.notifications,
                    slackWebhook: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Notification Events</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">User Created</h4>
              <p className="text-sm text-gray-400">Notify when a new user is created</p>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.notifications.events.userCreated}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      events: {
                        ...settings.notifications.events,
                        userCreated: e.target.checked
                      }
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">User Login</h4>
              <p className="text-sm text-gray-400">Notify on user login events</p>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.notifications.events.userLogin}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      events: {
                        ...settings.notifications.events,
                        userLogin: e.target.checked
                      }
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Deployment Success</h4>
              <p className="text-sm text-gray-400">Notify when a deployment succeeds</p>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.notifications.events.deploymentSuccess}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      events: {
                        ...settings.notifications.events,
                        deploymentSuccess: e.target.checked
                      }
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Deployment Failure</h4>
              <p className="text-sm text-gray-400">Notify when a deployment fails</p>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.notifications.events.deploymentFailure}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      events: {
                        ...settings.notifications.events,
                        deploymentFailure: e.target.checked
                      }
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Security Alert</h4>
              <p className="text-sm text-gray-400">Notify on security events</p>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.notifications.events.securityAlert}
                  onChange={(e) => setSettings({
                    ...settings,
                    notifications: {
                      ...settings.notifications,
                      events: {
                        ...settings.notifications.events,
                        securityAlert: e.target.checked
                      }
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMaintenanceSettings = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Backup Configuration</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Automated Backups</h4>
              <p className="text-sm text-gray-400">Schedule regular system backups</p>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.maintenance.backupEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    maintenance: {
                      ...settings.maintenance,
                      backupEnabled: e.target.checked
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          {settings.maintenance.backupEnabled && (
            <>
              <div>
                <label className="block text-gray-300 mb-2">Backup Frequency</label>
                <select
                  value={settings.maintenance.backupFrequency}
                  onChange={(e) => setSettings({
                    ...settings,
                    maintenance: {
                      ...settings.maintenance,
                      backupFrequency: e.target.value as any
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Backup Time</label>
                <input
                  type="time"
                  value={settings.maintenance.backupTime}
                  onChange={(e) => setSettings({
                    ...settings,
                    maintenance: {
                      ...settings.maintenance,
                      backupTime: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Backup Retention (days)</label>
                <input
                  type="number"
                  value={settings.maintenance.backupRetention}
                  onChange={(e) => setSettings({
                    ...settings,
                    maintenance: {
                      ...settings.maintenance,
                      backupRetention: parseInt(e.target.value)
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-400 mt-1">Backups older than this will be automatically deleted</p>
              </div>
            </>
          )}
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Data Cleanup</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">Automated Cleanup</h4>
              <p className="text-sm text-gray-400">Schedule regular cleanup of old data</p>
            </div>
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={settings.maintenance.cleanupEnabled}
                  onChange={(e) => setSettings({
                    ...settings,
                    maintenance: {
                      ...settings.maintenance,
                      cleanupEnabled: e.target.checked
                    }
                  })}
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
          
          {settings.maintenance.cleanupEnabled && (
            <>
              <div>
                <label className="block text-gray-300 mb-2">Cleanup Frequency</label>
                <select
                  value={settings.maintenance.cleanupFrequency}
                  onChange={(e) => setSettings({
                    ...settings,
                    maintenance: {
                      ...settings.maintenance,
                      cleanupFrequency: e.target.value as any
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Cleanup Time</label>
                <input
                  type="time"
                  value={settings.maintenance.cleanupTime}
                  onChange={(e) => setSettings({
                    ...settings,
                    maintenance: {
                      ...settings.maintenance,
                      cleanupTime: e.target.value
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Clean Data Older Than (days)</label>
                <input
                  type="number"
                  value={settings.maintenance.cleanupOlderThan}
                  onChange={(e) => setSettings({
                    ...settings,
                    maintenance: {
                      ...settings.maintenance,
                      cleanupOlderThan: parseInt(e.target.value)
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-400 mt-1">Data older than this will be automatically deleted</p>
              </div>
              
              <div className="bg-yellow-600/20 border border-yellow-600/30 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-yellow-400 font-medium">Warning</p>
                    <p className="text-sm text-gray-300">Automated cleanup will permanently delete old data. Make sure you have backups before enabling this feature.</p>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Maintenance Status</h3>
        <div className="space-y-4">
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-white font-medium">Scheduled Tasks</h4>
              <button className="text-blue-400 hover:text-blue-300 text-sm">Refresh</button>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <div>
                  <span className="text-white">Daily Backup</span>
                  <div className="text-xs text-gray-400">Next run: 2023-06-16 02:00:00</div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                    Active
                  </span>
                  <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                    <Pause className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <div>
                  <span className="text-white">Weekly Cleanup</span>
                  <div className="text-xs text-gray-400">Next run: 2023-06-18 03:00:00</div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                    Active
                  </span>
                  <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                    <Pause className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <div>
                  <span className="text-white">Database Vacuum</span>
                  <div className="text-xs text-gray-400">Next run: 2023-06-20 01:00:00</div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 rounded text-xs bg-gray-500/20 text-gray-400 border border-gray-500/30">
                    Paused
                  </span>
                  <button className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded">
                    <Play className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => setShowBackupModal(true)}
              className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Run Backup Now</span>
            </button>
            
            <button
              className="flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Run Cleanup Now</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSystemInfo = () => (
    <div className="space-y-6">
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">System Information</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-400 mb-1">Version</label>
              <div className="text-white">LUREON v2.5.0</div>
            </div>
            <div>
              <label className="block text-gray-400 mb-1">Environment</label>
              <div className="text-white">Production</div>
            </div>
            <div>
              <label className="block text-gray-400 mb-1">Server OS</label>
              <div className="text-white">Ubuntu 22.04 LTS</div>
            </div>
            <div>
              <label className="block text-gray-400 mb-1">Node.js Version</label>
              <div className="text-white">18.16.0</div>
            </div>
            <div>
              <label className="block text-gray-400 mb-1">Database Version</label>
              <div className="text-white">PostgreSQL 15.3</div>
            </div>
            <div>
              <label className="block text-gray-400 mb-1">Last Updated</label>
              <div className="text-white">2023-06-10</div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">System Health</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">CPU Usage</h4>
                <Server className="w-5 h-5 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white mb-2">12%</div>
              <div className="w-full bg-gray-600 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '12%' }}></div>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">Memory Usage</h4>
                <HardDrive className="w-5 h-5 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-white mb-2">34%</div>
              <div className="w-full bg-gray-600 rounded-full h-2">
                <div className="bg-purple-500 h-2 rounded-full" style={{ width: '34%' }}></div>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">Disk Usage</h4>
                <HardDrive className="w-5 h-5 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-white mb-2">56%</div>
              <div className="w-full bg-gray-600 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '56%' }}></div>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">Network</h4>
                <Globe className="w-5 h-5 text-yellow-400" />
              </div>
              <div className="text-2xl font-bold text-white mb-2">2.3 MB/s</div>
              <div className="flex items-center justify-between text-xs text-gray-400">
                <span>In: 1.2 MB/s</span>
                <span>Out: 1.1 MB/s</span>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-white font-medium">Services Status</h4>
              <button className="text-blue-400 hover:text-blue-300 text-sm">Refresh</button>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <span className="text-white">Web Server</span>
                <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                  Running
                </span>
              </div>
              
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <span className="text-white">Database Server</span>
                <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                  Running
                </span>
              </div>
              
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <span className="text-white">Redis Cache</span>
                <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                  Running
                </span>
              </div>
              
              <div className="flex items-center justify-between bg-gray-800 p-2 rounded">
                <span className="text-white">Background Jobs</span>
                <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                  Running
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">License Information</h3>
        <div className="space-y-4">
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-white font-medium">Enterprise License</h4>
              <span className="px-2 py-1 rounded text-xs bg-green-500/20 text-green-400 border border-green-500/30">
                Active
              </span>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">License Key</span>
                <span className="text-white font-mono">LURN-ENT-2345-6789-ABCD-EF01</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Valid Until</span>
                <span className="text-white">March 31, 2025</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Licensed To</span>
                <span className="text-white">LUREON Technologies</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Seats</span>
                <span className="text-white">50 (42 used)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">System Settings</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => {
                setIsLoading(true);
                setTimeout(() => setIsLoading(false), 1000);
              }}
              className={`p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors ${isLoading ? 'animate-spin text-blue-400' : ''}`}
              disabled={isLoading}
            >
              <RefreshCw className="w-5 h-5" />
            </button>
            <button
              onClick={handleSaveSettings}
              disabled={isSaving}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors"
            >
              {isSaving ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>Save Settings</span>
            </button>
          </div>
        </div>
        
        <div className="flex overflow-x-auto">
          {[
            { id: 'general', name: 'General', icon: Settings },
            { id: 'email', name: 'Email', icon: Mail },
            { id: 'storage', name: 'Storage', icon: HardDrive },
            { id: 'database', name: 'Database', icon: Database },
            { id: 'logging', name: 'Logging', icon: FileText },
            { id: 'notifications', name: 'Notifications', icon: Bell },
            { id: 'maintenance', name: 'Maintenance', icon: Clock },
            { id: 'system', name: 'System Info', icon: Server }
          ].map(tab => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                className={`flex items-center space-x-2 px-4 py-2 whitespace-nowrap transition-colors ${
                  activeTab === tab.id 
                    ? 'text-white border-b-2 border-blue-500' 
                    : 'text-gray-400 hover:text-white'
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                <IconComponent className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center space-x-2">
              <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />
              <span className="text-white">Loading settings...</span>
            </div>
          </div>
        ) : (
          <>
            {activeTab === 'general' && renderGeneralSettings()}
            {activeTab === 'email' && renderEmailSettings()}
            {activeTab === 'storage' && renderStorageSettings()}
            {activeTab === 'database' && renderDatabaseSettings()}
            {activeTab === 'logging' && renderLoggingSettings()}
            {activeTab === 'notifications' && renderNotificationSettings()}
            {activeTab === 'maintenance' && renderMaintenanceSettings()}
            {activeTab === 'system' && renderSystemInfo()}
          </>
        )}
      </div>

      {/* Backup Modal */}
      {showBackupModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Create Backup</h3>
              <button
                onClick={() => setShowBackupModal(false)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Backup Name</label>
                <input
                  type="text"
                  defaultValue={`backup-${new Date().toISOString().split('T')[0]}`}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Backup Type</label>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="fullBackup"
                      name="backupType"
                      value="full"
                      defaultChecked
                      className="text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="fullBackup" className="text-white">Full Backup</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="dataOnlyBackup"
                      name="backupType"
                      value="data"
                      className="text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="dataOnlyBackup" className="text-white">Data Only</label>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="compressBackup"
                  defaultChecked
                  className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                />
                <label htmlFor="compressBackup" className="text-white">Compress backup</label>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="encryptBackup"
                  defaultChecked
                  className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                />
                <label htmlFor="encryptBackup" className="text-white">Encrypt backup</label>
              </div>
              
              <div className="bg-blue-600/20 border border-blue-600/30 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <Info className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-300">Creating a backup may temporarily affect system performance. It's recommended to run backups during low-usage periods.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowBackupModal(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleBackupNow}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Start Backup
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Restore Modal */}
      {showRestoreModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Restore Backup</h3>
              <button
                onClick={() => setShowRestoreModal(false)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Select Backup</label>
                <select
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="backup-2023-06-15">backup-2023-06-15 (2.3 GB)</option>
                  <option value="backup-2023-06-14">backup-2023-06-14 (2.2 GB)</option>
                  <option value="backup-2023-06-13">backup-2023-06-13 (2.2 GB)</option>
                  <option value="backup-2023-06-12">backup-2023-06-12 (2.1 GB)</option>
                  <option value="backup-2023-06-11">backup-2023-06-11 (2.1 GB)</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Or Upload Backup File</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="file"
                    className="hidden"
                    id="backupFile"
                  />
                  <label
                    htmlFor="backupFile"
                    className="flex-1 flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors cursor-pointer"
                  >
                    <Upload className="w-4 h-4" />
                    <span>Choose File</span>
                  </label>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="overwriteExisting"
                  className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                />
                <label htmlFor="overwriteExisting" className="text-white">Overwrite existing data</label>
              </div>
              
              <div className="bg-red-600/20 border border-red-600/30 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-red-400 font-medium">Warning</p>
                    <p className="text-sm text-gray-300">Restoring a backup will replace all current data. This action cannot be undone.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowRestoreModal(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleRestoreBackup}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Restore Backup
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemSettings;