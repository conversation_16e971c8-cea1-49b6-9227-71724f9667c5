export interface PLCTag {
  id: string;
  name: string;
  type: 'BOOL' | 'INT' | 'DINT' | 'REAL' | 'TIME' | 'STRING' | 'ARRAY';
  value: any;
  address?: string;
  description?: string;
  scope: 'INPUT' | 'OUTPUT' | 'LOCAL' | 'GLOBAL';
  lastUpdated?: Date;
  ioMapping?: IOMapping;
  safetyRated?: boolean;
  silLevel?: 'SIL1' | 'SIL2' | 'SIL3';
}

export interface IOMapping {
  slot: number;
  channel: number;
  terminalBlock?: string;
  wireNumber?: string;
  physicalAddress: string;
  deviceType: 'DI' | 'DO' | 'AI' | 'AO';
  status: 'connected' | 'disconnected' | 'fault';
}

export interface LadderRung {
  id: string;
  number: number;
  elements: LadderElement[];
  comment?: string;
  enabled: boolean;
  safetyRung?: boolean;
  breakpoints?: string[];
}

export interface LadderElement {
  id: string;
  type: 'contact' | 'coil' | 'timer' | 'counter' | 'function' | 'safety_relay' | 'muting_function';
  position: { x: number; y: number };
  tag?: string;
  properties?: Record<string, any>;
  connections: string[];
  traced?: boolean;
  safetyElement?: boolean;
}

export interface PLCProgram {
  id: string;
  name: string;
  type: 'ladder' | 'st' | 'fbd' | 'sfc' | 'safety';
  content: string | LadderRung[];
  tags: PLCTag[];
  modified: boolean;
  lastSaved?: Date;
  safetyProgram?: boolean;
  silRating?: 'SIL1' | 'SIL2' | 'SIL3';
  version?: string;
  snapshots?: ProgramSnapshot[];
}

export interface ProgramSnapshot {
  id: string;
  timestamp: Date;
  description: string;
  content: string | LadderRung[];
  tags: PLCTag[];
  author: string;
}

export interface PLCTarget {
  id: string;
  name: string;
  brand: 'siemens' | 'rockwell' | 'schneider' | 'beckhoff';
  model: string;
  connected: boolean;
  status: 'run' | 'stop' | 'fault';
  ipAddress?: string;
  scanTime?: number;
  cpuLoad?: number;
  ioModules?: IOModule[];
  networkNodes?: NetworkNode[];
  safetyController?: boolean;
}

export interface IOModule {
  id: string;
  slot: number;
  type: 'DI' | 'DO' | 'AI' | 'AO' | 'MIXED';
  channels: IOChannel[];
  status: 'ok' | 'fault' | 'missing';
  partNumber: string;
}

export interface IOChannel {
  id: string;
  channel: number;
  type: 'DI' | 'DO' | 'AI' | 'AO';
  tag?: string;
  value?: any;
  status: 'ok' | 'fault' | 'forced';
  physicalAddress: string;
  terminalBlock?: string;
  wireColor?: string;
}

export interface NetworkNode {
  id: string;
  name: string;
  type: 'PLC' | 'HMI' | 'Drive' | 'IO_Block' | 'Safety_Controller';
  ipAddress: string;
  status: 'online' | 'offline' | 'fault';
  protocol: 'EtherNet/IP' | 'Modbus TCP' | 'Profinet' | 'DeviceNet';
  position: { x: number; y: number };
  connections: string[];
}

export interface HMIScreen {
  id: string;
  name: string;
  elements: HMIElement[];
  size: { width: number; height: number };
}

export interface HMIElement {
  id: string;
  type: 'button' | 'indicator' | 'text' | 'gauge' | 'trend';
  position: { x: number; y: number };
  size: { width: number; height: number };
  tag?: string;
  properties: Record<string, any>;
}

export interface PLCProject {
  id: string;
  name: string;
  description?: string;
  programs: PLCProgram[];
  globalTags: PLCTag[];
  targets: PLCTarget[];
  hmiScreens?: HMIScreen[];
  networkTopology?: NetworkNode[];
  templates?: PLCTemplate[];
  created: Date;
  modified: Date;
  version: string;
  collaborators?: Collaborator[];
}

export interface PLCTemplate {
  id: string;
  name: string;
  category: 'Motor Control' | 'Safety' | 'Process Control' | 'Communication';
  description: string;
  content: LadderRung[] | string;
  tags: PLCTag[];
  vendor?: string;
}

export interface Collaborator {
  id: string;
  name: string;
  email: string;
  role: 'owner' | 'editor' | 'viewer';
  lastActive: Date;
  currentlyEditing?: string;
}