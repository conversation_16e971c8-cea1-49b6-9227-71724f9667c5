import React from 'react';
import { 
  Square, 
  Circle, 
  Timer, 
  Hash, 
  Calculator,
  GitBranch,
  Zap,
  Shield,
  RotateCcw,
  Play,
  Pause,
  Plus,
  Check,
  X
} from 'lucide-react';
import { usePLCStore } from '../../store/plcStore';

interface LadderToolbarProps {
  onAddElement: (elementType: string, properties?: any) => void;
  disabled?: boolean;
}

const LadderToolbar: React.FC<LadderToolbarProps> = ({ onAddElement, disabled = false }) => {
  const { toggleSimulation, simulationMode } = usePLCStore();
  
  const toolbarItems = [
    {
      id: 'contact-no',
      name: 'Contact (NO)',
      icon: <Square className="w-4 h-4" />,
      symbol: '][',
      description: 'Normally Open Contact',
      action: () => onAddElement('contact', { normally: 'open' })
    },
    {
      id: 'contact-nc',
      name: 'Contact (NC)',
      icon: <Square className="w-4 h-4" />,
      symbol: '/]',
      description: 'Normally Closed Contact',
      action: () => onAddElement('contact', { normally: 'closed' })
    },
    {
      id: 'coil',
      name: 'Coil',
      icon: <Circle className="w-4 h-4" />,
      symbol: '( )',
      description: 'Output Coil',
      action: () => onAddElement('coil')
    },
    {
      id: 'timer-ton',
      name: 'Timer (TON)',
      icon: <Timer className="w-4 h-4" />,
      symbol: 'TON',
      description: 'Timer On Delay',
      action: () => onAddElement('timer', { timerType: 'TON', preset: 'T#5S' })
    },
    {
      id: 'timer-tof',
      name: 'Timer (TOF)',
      icon: <Timer className="w-4 h-4" />,
      symbol: 'TOF',
      description: 'Timer Off Delay',
      action: () => onAddElement('timer', { timerType: 'TOF', preset: 'T#5S' })
    },
    {
      id: 'counter-ctu',
      name: 'Counter (CTU)',
      icon: <Hash className="w-4 h-4" />,
      symbol: 'CTU',
      description: 'Count Up',
      action: () => onAddElement('counter', { counterType: 'CTU', preset: 10 })
    },
    {
      id: 'counter-ctd',
      name: 'Counter (CTD)',
      icon: <Hash className="w-4 h-4" />,
      symbol: 'CTD',
      description: 'Count Down',
      action: () => onAddElement('counter', { counterType: 'CTD', preset: 10 })
    },
    {
      id: 'compare',
      name: 'Compare',
      icon: <Calculator className="w-4 h-4" />,
      symbol: 'CMP',
      description: 'Compare Function',
      action: () => onAddElement('function', { functionType: 'compare', operator: '==' })
    },
    {
      id: 'math',
      name: 'Math',
      icon: <Calculator className="w-4 h-4" />,
      symbol: 'ADD',
      description: 'Math Function',
      action: () => onAddElement('function', { functionType: 'math', operator: '+' })
    },
    {
      id: 'branch',
      name: 'Branch',
      icon: <GitBranch className="w-4 h-4" />,
      symbol: '||',
      description: 'Parallel Branch',
      action: () => onAddElement('branch')
    },
    {
      id: 'safety-relay',
      name: 'Safety Relay',
      icon: <Shield className="w-4 h-4" />,
      symbol: 'SR',
      description: 'Safety Relay Function',
      action: () => onAddElement('safety_relay')
    },
    {
      id: 'reset',
      name: 'Reset',
      icon: <RotateCcw className="w-4 h-4" />,
      symbol: 'RES',
      description: 'Reset Function',
      action: () => onAddElement('function', { functionType: 'reset' })
    }
  ];

  return (
    <div className="bg-gray-800 border-b border-gray-700 p-2">
      <div className="flex items-center space-x-1 overflow-x-auto">
        <div className="text-sm text-gray-400 mr-4 whitespace-nowrap">Ladder Elements:</div>
        
        {toolbarItems.map(item => (
          <button
            key={item.id}
            onClick={item.action}
            disabled={disabled}
            className="flex flex-col items-center p-2 min-w-16 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed border border-gray-600 rounded transition-colors group"
            title={item.description}
          >
            <div className="text-blue-400 mb-1">
              {item.icon}
            </div>
            <div className="text-xs text-white font-mono font-bold">
              {item.symbol}
            </div>
            <div className="text-xs text-gray-400 mt-1 text-center leading-tight">
              {item.name.split(' ')[0]}
            </div>
          </button>
        ))}
        
        <div className="w-px h-12 bg-gray-600 mx-2" />
        
        {/* Control buttons */}
        <button
          className="flex flex-col items-center p-2 min-w-16 bg-green-700 hover:bg-green-600 border border-green-600 rounded transition-colors"
          title="Run Logic"
          onClick={() => {
            const { toggleSimulation } = usePLCStore.getState();
            toggleSimulation();
          }}
        >
          <Play className="w-4 h-4 text-white mb-1" />
          <div className="text-xs text-white">Run</div>
        </button>
        
        <button
          className="flex flex-col items-center p-2 min-w-16 bg-red-700 hover:bg-red-600 border border-red-600 rounded transition-colors"
          title="Stop Logic"
          onClick={() => {
            const { toggleSimulation, simulationMode } = usePLCStore.getState();
            if (simulationMode) {
              toggleSimulation();
            }
          }}
        >
          <Pause className="w-4 h-4 text-white mb-1" />
          <div className="text-xs text-white">Stop</div>
        </button>
        
        {/* Add Rung button */}
        <button
          className="flex flex-col items-center p-2 min-w-16 bg-blue-700 hover:bg-blue-600 border border-blue-600 rounded transition-colors"
          title="Add Rung"
          onClick={() => onAddElement('add-rung')}
        >
          <Plus className="w-4 h-4 text-white mb-1" />
          <div className="text-xs text-white">Add Rung</div>
        </button>
        
        {/* Validate button */}
        <button
          className="flex flex-col items-center p-2 min-w-16 bg-purple-700 hover:bg-purple-600 border border-purple-600 rounded transition-colors"
          title="Validate Logic"
          onClick={() => onAddElement('validate')}
        >
          <Check className="w-4 h-4 text-white mb-1" />
          <div className="text-xs text-white">Validate</div>
        </button>
      </div>
    </div>
  );
};

export default LadderToolbar;