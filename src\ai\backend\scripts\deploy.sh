#!/bin/bash
set -e

echo "🚀 Starting LUREON AI Backend Deployment..."

# Load environment variables
if [ -f .env.production ]; then
    export $(cat .env.production | grep -v '^#' | xargs)
fi

# Build and deploy
echo "📦 Building containers..."
docker-compose -f docker-compose.prod.yml build --no-cache

echo "🔄 Starting services..."
docker-compose -f docker-compose.prod.yml up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
timeout 300 bash -c 'until docker-compose -f docker-compose.prod.yml exec ai-backend curl -f http://localhost:3001/health; do sleep 5; done'

echo "🔍 Running health checks..."
docker-compose -f docker-compose.prod.yml exec ai-backend curl -f http://localhost:3001/health

echo "📊 Displaying service status..."
docker-compose -f docker-compose.prod.yml ps

echo "✅ Deployment completed successfully!"
echo "🌐 AI Backend is available at: https://your-domain.com/api/ai"