import { create } from 'zustand';
import { <PERSON><PERSON><PERSON>roject, P<PERSON><PERSON>rogram, PLC<PERSON>ag, PLCTarget, LadderRung, IOMapping, HMIScreen } from '../types/plc';
import { v4 as uuidv4 } from 'uuid';

interface PLCStore {
  // State
  currentProject: PLCProject | null;
  activeProgram: string | null;
  selectedTags: PLCTag[];
  simulationMode: boolean;
  
  // Actions
  createProject: (name: string, description?: string) => void;
  loadProject: (project: PLCProject) => void;
  saveProject: () => void;
  
  createProgram: (name: string, type: PLCProgram['type']) => void;
  updateProgram: (id: string, updates: Partial<PLCProgram>) => void;
  deleteProgram: (id: string) => void;
  setActiveProgram: (id: string) => void;
  
  createTag: (tag: Omit<PLCTag, 'id'>) => void;
  updateTag: (id: string, updates: Partial<PLCTag>) => void;
  deleteTag: (id: string) => void;
  updateTagIOMapping: (tagId: string, mapping?: IOMapping) => void;
  
  addTarget: (target: Omit<PLCTarget, 'id'>) => void;
  updateTarget: (id: string, updates: Partial<PLCTarget>) => void;
  removeTarget: (id: string) => void;
  
  toggleSimulation: () => void;
  updateTagValue: (tagId: string, value: any) => void;
}

const createDefaultProject = (): PLCProject => ({
  id: uuidv4(),
  name: 'PumpControl_Station_A',
  description: 'Main pump control system with safety interlocks',
  version: '1.0.0',
  programs: [
    {
      id: 'main-program',
      name: 'Main_Program',
      type: 'ladder',
      content: [
        {
          id: 'rung-0',
          number: 0,
          elements: [
            {
              id: 'elem-1',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Start_Button',
              properties: { normally: 'open' },
              connections: ['elem-2']
            },
            {
              id: 'elem-2',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Emergency_Stop',
              properties: { normally: 'closed' },
              connections: ['elem-3']
            },
            {
              id: 'elem-3',
              type: 'coil',
              position: { x: 6, y: 0 },
              tag: 'Pump_Run',
              properties: {},
              connections: []
            }
          ],
          comment: 'Start/Stop logic with emergency stop',
          enabled: true
        }
      ] as LadderRung[],
      tags: [],
      modified: false
    },
    {
      id: 'safety-logic',
      name: 'Safety_Logic',
      type: 'safety',
      content: '',
      tags: [],
      modified: true,
      safetyProgram: true,
      silRating: 'SIL2'
    }
  ],
  globalTags: [
    {
      id: 'tag-1',
      name: 'Start_Button',
      type: 'BOOL',
      value: false,
      scope: 'INPUT',
      description: 'Main start button input',
      ioMapping: {
        slot: 1,
        channel: 0,
        physicalAddress: 'I:1/0',
        deviceType: 'DI',
        status: 'connected'
      }
    },
    {
      id: 'tag-2',
      name: 'Emergency_Stop',
      type: 'BOOL',
      value: true,
      scope: 'INPUT',
      description: 'Emergency stop button (NC)',
      safetyRated: true,
      silLevel: 'SIL3',
      ioMapping: {
        slot: 1,
        channel: 1,
        physicalAddress: 'I:1/1',
        deviceType: 'DI',
        status: 'connected'
      }
    },
    {
      id: 'tag-3',
      name: 'Pump_Run',
      type: 'BOOL',
      value: false,
      scope: 'OUTPUT',
      description: 'Pump run command output',
      ioMapping: {
        slot: 2,
        channel: 0,
        physicalAddress: 'O:2/0',
        deviceType: 'DO',
        status: 'connected'
      }
    },
    {
      id: 'tag-4',
      name: 'Motor_Output',
      type: 'BOOL',
      value: false,
      scope: 'OUTPUT',
      description: 'Motor contactor output',
      ioMapping: {
        slot: 2,
        channel: 1,
        physicalAddress: 'O:2/1',
        deviceType: 'DO',
        status: 'connected'
      }
    },
    {
      id: 'tag-5',
      name: 'Pressure_Value',
      type: 'REAL',
      value: 50.0,
      scope: 'INPUT',
      description: 'Pressure sensor value'
    },
    {
      id: 'tag-6',
      name: 'Temperature_Value',
      type: 'REAL',
      value: 25.0,
      scope: 'INPUT',
      description: 'Temperature sensor value'
    },
    {
      id: 'tag-7',
      name: 'Flow_Rate',
      type: 'REAL',
      value: 75.0,
      scope: 'INPUT',
      description: 'Flow rate sensor value'
    },
    {
      id: 'tag-8',
      name: 'Tank_Level',
      type: 'REAL',
      value: 65.0,
      scope: 'INPUT',
      description: 'Tank level sensor value'
    }
  ],
  targets: [
    {
      id: 'target-1',
      name: 'Siemens S7-1500',
      brand: 'siemens',
      model: 'CPU 1511-1 PN',
      connected: true,
      status: 'run',
      ipAddress: '*************',
      scanTime: 2.3,
      cpuLoad: 12,
      ioModules: [
        {
          id: 'module-1',
          slot: 1,
          type: 'DI',
          partNumber: 'SM 1221',
          status: 'ok',
          channels: [
            {
              id: 'ch-1-0',
              channel: 0,
              type: 'DI',
              tag: 'Start_Button',
              value: false,
              status: 'ok',
              physicalAddress: 'I:1/0',
              terminalBlock: '1'
            },
            {
              id: 'ch-1-1',
              channel: 1,
              type: 'DI',
              tag: 'Emergency_Stop',
              value: true,
              status: 'ok',
              physicalAddress: 'I:1/1',
              terminalBlock: '2'
            },
            {
              id: 'ch-1-2',
              channel: 2,
              type: 'DI',
              status: 'ok',
              physicalAddress: 'I:1/2'
            },
            {
              id: 'ch-1-3',
              channel: 3,
              type: 'DI',
              status: 'ok',
              physicalAddress: 'I:1/3'
            }
          ]
        },
        {
          id: 'module-2',
          slot: 2,
          type: 'DO',
          partNumber: 'SM 1222',
          status: 'ok',
          channels: [
            {
              id: 'ch-2-0',
              channel: 0,
              type: 'DO',
              tag: 'Pump_Run',
              value: false,
              status: 'ok',
              physicalAddress: 'O:2/0',
              terminalBlock: '1'
            },
            {
              id: 'ch-2-1',
              channel: 1,
              type: 'DO',
              tag: 'Motor_Output',
              value: false,
              status: 'ok',
              physicalAddress: 'O:2/1',
              terminalBlock: '2'
            },
            {
              id: 'ch-2-2',
              channel: 2,
              type: 'DO',
              status: 'ok',
              physicalAddress: 'O:2/2'
            },
            {
              id: 'ch-2-3',
              channel: 3,
              type: 'DO',
              status: 'ok',
              physicalAddress: 'O:2/3'
            }
          ]
        }
      ]
    }
  ],
  hmiScreens: [
    {
      id: 'main-screen',
      name: 'Main Control Panel',
      size: { width: 1024, height: 768 },
      elements: [
        {
          id: 'header-text',
          type: 'text',
          position: { x: 20, y: 20 },
          size: { width: 400, height: 40 },
          properties: {
            text: 'PUMP STATION CONTROL',
            fontSize: 24,
            color: 'white',
            fontWeight: 'bold'
          }
        },
        {
          id: 'start-btn',
          type: 'button',
          position: { x: 50, y: 120 },
          size: { width: 120, height: 60 },
          tag: 'Start_Button',
          properties: {
            text: 'START',
            color: 'green',
            pressAction: 'momentary'
          }
        },
        {
          id: 'stop-btn',
          type: 'button',
          position: { x: 200, y: 120 },
          size: { width: 120, height: 60 },
          tag: 'Emergency_Stop',
          properties: {
            text: 'E-STOP',
            color: 'red',
            pressAction: 'toggle'
          }
        }
      ]
    }
  ],
  created: new Date(),
  modified: new Date()
});

// Available PLC models for selection
export const availablePLCModels = [
  { brand: 'siemens', models: ['CPU 1511-1 PN', 'CPU 1513-1 PN', 'CPU 1515-2 PN', 'CPU 1517-3 PN/DP'] },
  { brand: 'rockwell', models: ['ControlLogix 1756-L71', 'ControlLogix 1756-L81E', 'CompactLogix 5380', 'CompactLogix 5069-L306ER'] },
  { brand: 'schneider', models: ['M340 BMXP342020', 'M580 BMEP582040', 'Modicon M251', 'Modicon M262'] },
  { brand: 'beckhoff', models: ['CX5120', 'CX5130', 'CX5140', 'CX8190'] },
  { brand: 'omron', models: ['NX1P2', 'NJ301', 'NJ501', 'CJ2M-CPU31'] }
];

export const usePLCStore = create<PLCStore>((set, get) => ({
  currentProject: createDefaultProject(),
  activeProgram: 'main-program',
  selectedTags: [],
  simulationMode: false,

  createProject: (name, description) => {
    const project: PLCProject = {
      id: uuidv4(),
      name,
      description,
      version: '1.0.0',
      programs: [],
      globalTags: [],
      targets: [],
      created: new Date(),
      modified: new Date()
    };
    set({ currentProject: project, activeProgram: null });
  },

  loadProject: (project) => {
    set({ currentProject: project, activeProgram: project.programs[0]?.id || null });
  },

  saveProject: () => {
    const { currentProject } = get();
    if (currentProject) {
      set({
        currentProject: {
          ...currentProject,
          modified: new Date()
        }
      });
      
      // In a real app, this would save to a server or local storage
      console.log('Project saved:', currentProject.name);
      alert('Project saved successfully');
    }
  },

  createProgram: (name, type) => {
    const { currentProject } = get();
    if (!currentProject) return;

    const program: PLCProgram = {
      id: uuidv4(),
      name,
      type,
      content: type === 'ladder' ? [] : '',
      tags: [],
      modified: false
    };

    set({
      currentProject: {
        ...currentProject,
        programs: [...currentProject.programs, program],
        modified: new Date()
      },
      activeProgram: program.id
    });
  },

  updateProgram: (id, updates) => {
    const { currentProject } = get();
    if (!currentProject) return;

    set({
      currentProject: {
        ...currentProject,
        programs: currentProject.programs.map(p =>
          p.id === id ? { ...p, ...updates, modified: true } : p
        ),
        modified: new Date()
      }
    });
  },

  deleteProgram: (id) => {
    const { currentProject, activeProgram } = get();
    if (!currentProject) return;

    const newPrograms = currentProject.programs.filter(p => p.id !== id);
    set({
      currentProject: {
        ...currentProject,
        programs: newPrograms,
        modified: new Date()
      },
      activeProgram: activeProgram === id ? (newPrograms[0]?.id || null) : activeProgram
    });
  },

  setActiveProgram: (id) => {
    set({ activeProgram: id });
  },

  createTag: (tag) => {
    const { currentProject } = get();
    if (!currentProject) return;

    const newTag: PLCTag = {
      ...tag,
      id: uuidv4(),
      lastUpdated: new Date()
    };

    set({
      currentProject: {
        ...currentProject,
        globalTags: [...currentProject.globalTags, newTag],
        modified: new Date()
      }
    });
  },

  updateTag: (id, updates) => {
    const { currentProject } = get();
    if (!currentProject) return;

    set({
      currentProject: {
        ...currentProject,
        globalTags: currentProject.globalTags.map(t =>
          t.id === id ? { ...t, ...updates, lastUpdated: new Date() } : t
        ),
        modified: new Date()
      }
    });
  },

  deleteTag: (id) => {
    const { currentProject } = get();
    if (!currentProject) return;

    set({
      currentProject: {
        ...currentProject,
        globalTags: currentProject.globalTags.filter(t => t.id !== id),
        modified: new Date()
      }
    });
  },

  updateTagIOMapping: (tagId, mapping) => {
    const { currentProject } = get();
    if (!currentProject) return;

    set({
      currentProject: {
        ...currentProject,
        globalTags: currentProject.globalTags.map(t =>
          t.id === tagId ? { ...t, ioMapping: mapping, lastUpdated: new Date() } : t
        ),
        modified: new Date()
      }
    });
  },

  addTarget: (target) => {
    const { currentProject } = get();
    if (!currentProject) return;

    const newTarget: PLCTarget = {
      ...target,
      id: uuidv4()
    };

    set({
      currentProject: {
        ...currentProject,
        targets: [...currentProject.targets, newTarget],
        modified: new Date()
      }
    });
  },

  updateTarget: (id, updates) => {
    const { currentProject } = get();
    if (!currentProject) return;

    set({
      currentProject: {
        ...currentProject,
        targets: currentProject.targets.map(t =>
          t.id === id ? { ...t, ...updates } : t
        ),
        modified: new Date()
      }
    });
  },

  removeTarget: (id) => {
    const { currentProject } = get();
    if (!currentProject) return;

    set({
      currentProject: {
        ...currentProject,
        targets: currentProject.targets.filter(t => t.id !== id),
        modified: new Date()
      }
    });
  },

  toggleSimulation: () => {
    set(state => ({ simulationMode: !state.simulationMode }));
  },

  updateTagValue: (tagId, value) => {
    const { currentProject } = get();
    if (!currentProject) return;

    set({
      currentProject: {
        ...currentProject,
        globalTags: currentProject.globalTags.map(t =>
          t.id === tagId ? { ...t, value, lastUpdated: new Date() } : t
        )
      }
    });
  }
}));