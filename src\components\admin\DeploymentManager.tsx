import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  Download, 
  Upload, 
  CheckCircle2, 
  XCircle, 
  Clock, 
  RefreshCw, 
  AlertTriangle, 
  FileText, 
  Server, 
  User, 
  Calendar, 
  ArrowRight, 
  X, 
  RotateCcw,
  Play,
  Pause,
  Eye
} from 'lucide-react';

interface DeploymentData {
  id: string;
  projectName: string;
  version: string;
  target: {
    name: string;
    type: string;
    ipAddress: string;
  };
  status: 'pending' | 'in_progress' | 'success' | 'failed' | 'cancelled' | 'scheduled';
  deployedBy: string;
  deployedAt: Date;
  completedAt?: Date;
  logs: {
    timestamp: Date;
    level: 'info' | 'warning' | 'error';
    message: string;
  }[];
  rollbackAvailable: boolean;
  previousVersion?: string;
  approvals: {
    user: string;
    status: 'approved' | 'rejected' | 'pending';
    timestamp?: Date;
  }[];
}

const DeploymentManager: React.FC = () => {
  const [deployments, setDeployments] = useState<DeploymentData[]>([]);
  const [filteredDeployments, setFilteredDeployments] = useState<DeploymentData[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedDeployment, setSelectedDeployment] = useState<DeploymentData | null>(null);
  const [showNewDeploymentModal, setShowNewDeploymentModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showLogs, setShowLogs] = useState(false);

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const mockDeployments: DeploymentData[] = [
        {
          id: '1',
          projectName: 'Production Line A',
          version: '2.5.0',
          target: {
            name: 'PLC-S7-1500-A',
            type: 'Siemens S7-1500',
            ipAddress: '*************'
          },
          status: 'success',
          deployedBy: 'John Engineer',
          deployedAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
          completedAt: new Date(Date.now() - 1000 * 60 * 25), // 25 minutes ago
          logs: [
            { timestamp: new Date(Date.now() - 1000 * 60 * 30), level: 'info', message: 'Deployment started' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 28), level: 'info', message: 'Connecting to PLC' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 27), level: 'info', message: 'Uploading program' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 25), level: 'info', message: 'Deployment completed successfully' }
          ],
          rollbackAvailable: true,
          previousVersion: '2.4.2',
          approvals: [
            { user: 'Sarah Admin', status: 'approved', timestamp: new Date(Date.now() - 1000 * 60 * 60) }
          ]
        },
        {
          id: '2',
          projectName: 'Packaging System',
          version: '1.8.3',
          target: {
            name: 'PLC-AB-CompactLogix',
            type: 'Allen-Bradley CompactLogix',
            ipAddress: '*************'
          },
          status: 'in_progress',
          deployedBy: 'Mike Safety',
          deployedAt: new Date(Date.now() - 1000 * 60 * 10), // 10 minutes ago
          logs: [
            { timestamp: new Date(Date.now() - 1000 * 60 * 10), level: 'info', message: 'Deployment started' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 8), level: 'info', message: 'Connecting to PLC' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 7), level: 'info', message: 'Uploading program' }
          ],
          rollbackAvailable: true,
          previousVersion: '1.8.2',
          approvals: [
            { user: 'David Manager', status: 'approved', timestamp: new Date(Date.now() - 1000 * 60 * 30) }
          ]
        },
        {
          id: '3',
          projectName: 'HVAC Control',
          version: '3.1.0',
          target: {
            name: 'PLC-Beckhoff-CX5140',
            type: 'Beckhoff CX5140',
            ipAddress: '*************'
          },
          status: 'failed',
          deployedBy: 'Lisa Operator',
          deployedAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          completedAt: new Date(Date.now() - 1000 * 60 * 60 * 2 + 1000 * 60 * 5), // 1 hour 55 minutes ago
          logs: [
            { timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), level: 'info', message: 'Deployment started' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2 + 1000 * 60 * 2), level: 'info', message: 'Connecting to PLC' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2 + 1000 * 60 * 3), level: 'warning', message: 'Connection unstable' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2 + 1000 * 60 * 5), level: 'error', message: 'Deployment failed: Connection lost' }
          ],
          rollbackAvailable: false,
          approvals: [
            { user: 'Sarah Admin', status: 'approved', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3) }
          ]
        },
        {
          id: '4',
          projectName: 'Conveyor System',
          version: '1.2.0',
          target: {
            name: 'PLC-Siemens-S7-300',
            type: 'Siemens S7-300',
            ipAddress: '*************'
          },
          status: 'scheduled',
          deployedBy: 'John Engineer',
          deployedAt: new Date(Date.now() + 1000 * 60 * 60 * 12), // 12 hours in the future
          logs: [],
          rollbackAvailable: false,
          approvals: [
            { user: 'David Manager', status: 'approved', timestamp: new Date(Date.now() - 1000 * 60 * 30) },
            { user: 'Sarah Admin', status: 'pending' }
          ]
        },
        {
          id: '5',
          projectName: 'Safety System',
          version: '2.0.1',
          target: {
            name: 'PLC-Pilz-PSS4000',
            type: 'Pilz PSS4000',
            ipAddress: '*************'
          },
          status: 'pending',
          deployedBy: 'Mike Safety',
          deployedAt: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
          logs: [
            { timestamp: new Date(Date.now() - 1000 * 60 * 5), level: 'info', message: 'Deployment queued' }
          ],
          rollbackAvailable: false,
          approvals: [
            { user: 'Sarah Admin', status: 'approved', timestamp: new Date(Date.now() - 1000 * 60 * 60) },
            { user: 'David Manager', status: 'pending' }
          ]
        },
        {
          id: '6',
          projectName: 'Production Line B',
          version: '1.7.3',
          target: {
            name: 'PLC-S7-1500-B',
            type: 'Siemens S7-1500',
            ipAddress: '*************'
          },
          status: 'cancelled',
          deployedBy: 'John Engineer',
          deployedAt: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
          completedAt: new Date(Date.now() - 1000 * 60 * 60 * 5 + 1000 * 60 * 2), // 4 hours 58 minutes ago
          logs: [
            { timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), level: 'info', message: 'Deployment started' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5 + 1000 * 60 * 1), level: 'info', message: 'Connecting to PLC' },
            { timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5 + 1000 * 60 * 2), level: 'warning', message: 'Deployment cancelled by user' }
          ],
          rollbackAvailable: false,
          approvals: [
            { user: 'Sarah Admin', status: 'approved', timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6) }
          ]
        }
      ];
      
      setDeployments(mockDeployments);
      setFilteredDeployments(mockDeployments);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter deployments based on search and filters
  useEffect(() => {
    let result = deployments;
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(deployment => 
        deployment.projectName.toLowerCase().includes(query) || 
        deployment.target.name.toLowerCase().includes(query) ||
        deployment.deployedBy.toLowerCase().includes(query)
      );
    }
    
    if (filterStatus !== 'all') {
      result = result.filter(deployment => deployment.status === filterStatus);
    }
    
    setFilteredDeployments(result);
  }, [deployments, searchQuery, filterStatus]);

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const handleCancelDeployment = (deploymentId: string) => {
    if (confirm('Are you sure you want to cancel this deployment?')) {
      setDeployments(deployments.map(deployment => 
        deployment.id === deploymentId 
          ? { 
              ...deployment, 
              status: 'cancelled',
              completedAt: new Date(),
              logs: [
                ...deployment.logs,
                { timestamp: new Date(), level: 'warning', message: 'Deployment cancelled by user' }
              ]
            } 
          : deployment
      ));
      
      if (selectedDeployment?.id === deploymentId) {
        setSelectedDeployment({
          ...selectedDeployment,
          status: 'cancelled',
          completedAt: new Date(),
          logs: [
            ...selectedDeployment.logs,
            { timestamp: new Date(), level: 'warning', message: 'Deployment cancelled by user' }
          ]
        });
      }
    }
  };

  const handleRollbackDeployment = (deploymentId: string) => {
    if (confirm('Are you sure you want to rollback this deployment? This will revert to the previous version.')) {
      // In a real implementation, this would trigger a rollback process
      alert('Rollback initiated');
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'in_progress':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'failed':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'cancelled':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      case 'scheduled':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle2 className="w-4 h-4 text-green-400" />;
      case 'in_progress':
        return <RefreshCw className="w-4 h-4 text-blue-400 animate-spin" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-400" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'cancelled':
        return <X className="w-4 h-4 text-gray-400" />;
      case 'scheduled':
        return <Calendar className="w-4 h-4 text-purple-400" />;
      default:
        return null;
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'info':
        return 'text-blue-400';
      case 'warning':
        return 'text-yellow-400';
      case 'error':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const getLogLevelIcon = (level: string) => {
    switch (level) {
      case 'info':
        return <Info className="w-4 h-4" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4" />;
      case 'error':
        return <XCircle className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const formatTimestamp = (date: Date): string => {
    return date.toLocaleString();
  };

  const calculateDuration = (start: Date, end?: Date): string => {
    if (!end) return 'In progress';
    
    const diffMs = end.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Less than a minute';
    if (diffMins < 60) return `${diffMins} minute${diffMins !== 1 ? 's' : ''}`;
    
    const diffHours = Math.floor(diffMins / 60);
    const remainingMins = diffMins % 60;
    
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ${remainingMins} minute${remainingMins !== 1 ? 's' : ''}`;
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with search and filters */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Deployment Manager</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleRefresh}
              className={`p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors ${isLoading ? 'animate-spin text-blue-400' : ''}`}
              disabled={isLoading}
            >
              <RefreshCw className="w-5 h-5" />
            </button>
            <button
              onClick={() => setShowNewDeploymentModal(true)}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>New Deployment</span>
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div className="col-span-3 sm:col-span-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search deployments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="success">Success</option>
              <option value="in_progress">In Progress</option>
              <option value="failed">Failed</option>
              <option value="pending">Pending</option>
              <option value="cancelled">Cancelled</option>
              <option value="scheduled">Scheduled</option>
            </select>
          </div>
        </div>
      </div>

      {/* Deployment Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Total Deployments</h3>
            <Package className="w-5 h-5 text-blue-400" />
          </div>
          <div className="text-2xl font-bold text-white">{deployments.length}</div>
          <div className="text-xs text-green-400 mt-1">+12% from last month</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Success Rate</h3>
            <CheckCircle2 className="w-5 h-5 text-green-400" />
          </div>
          <div className="text-2xl font-bold text-white">
            {Math.round((deployments.filter(d => d.status === 'success').length / deployments.length) * 100)}%
          </div>
          <div className="text-xs text-green-400 mt-1">+3% from last month</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Avg. Deploy Time</h3>
            <Clock className="w-5 h-5 text-yellow-400" />
          </div>
          <div className="text-2xl font-bold text-white">4.5 min</div>
          <div className="text-xs text-green-400 mt-1">-0.8 min from last month</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-400">Pending Approvals</h3>
            <AlertTriangle className="w-5 h-5 text-yellow-400" />
          </div>
          <div className="text-2xl font-bold text-white">2</div>
          <div className="text-xs text-gray-400 mt-1">Requires attention</div>
        </div>
      </div>

      {/* Deployments List and Details */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Deployments List */}
        <div className="lg:col-span-2 bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-700">
            <h3 className="text-white font-medium">Recent Deployments</h3>
          </div>
          
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center space-x-2">
                <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />
                <span className="text-white">Loading deployments...</span>
              </div>
            </div>
          ) : filteredDeployments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64">
              <Package className="w-16 h-16 text-gray-600 mb-4" />
              <h3 className="text-lg font-medium text-white">No deployments found</h3>
              <p className="text-gray-400 mt-2">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="overflow-y-auto max-h-[calc(100vh-300px)]">
              {filteredDeployments.map(deployment => (
                <div 
                  key={deployment.id} 
                  className={`p-4 border-b border-gray-700 hover:bg-gray-700/50 cursor-pointer transition-colors ${
                    selectedDeployment?.id === deployment.id ? 'bg-gray-700/50' : ''
                  }`}
                  onClick={() => setSelectedDeployment(deployment)}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${
                      deployment.status === 'success' ? 'bg-green-500/20' :
                      deployment.status === 'in_progress' ? 'bg-blue-500/20' :
                      deployment.status === 'failed' ? 'bg-red-500/20' :
                      deployment.status === 'pending' ? 'bg-yellow-500/20' :
                      deployment.status === 'scheduled' ? 'bg-purple-500/20' :
                      'bg-gray-500/20'
                    }`}>
                      {getStatusIcon(deployment.status)}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-white font-medium">{deployment.projectName}</span>
                          <span className="text-xs text-gray-400">v{deployment.version}</span>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs border ${getStatusBadgeColor(deployment.status)}`}>
                          {deployment.status.replace('_', ' ')}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-400 mt-1">
                        <Server className="w-3 h-3" />
                        <span>{deployment.target.name}</span>
                        <span>•</span>
                        <span>{deployment.target.type}</span>
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <User className="w-3 h-3" />
                          <span>{deployment.deployedBy}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <Clock className="w-3 h-3" />
                          <span>
                            {deployment.status === 'scheduled' 
                              ? `Scheduled for ${formatTimestamp(deployment.deployedAt)}` 
                              : `${formatTimestamp(deployment.deployedAt)}`}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Deployment Details */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          {selectedDeployment ? (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-gray-700 flex items-center justify-between">
                <h3 className="text-white font-medium">Deployment Details</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowLogs(!showLogs)}
                    className={`p-1 rounded transition-colors ${
                      showLogs ? 'text-blue-400 bg-blue-400/10' : 'text-gray-400 hover:text-white hover:bg-gray-700'
                    }`}
                    title={showLogs ? 'Hide Logs' : 'Show Logs'}
                  >
                    <FileText className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setSelectedDeployment(null)}
                    className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div className="p-4 flex-1 overflow-y-auto">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-white font-medium">{selectedDeployment.projectName}</h4>
                    <div className="text-sm text-gray-400">Version {selectedDeployment.version}</div>
                  </div>
                  <span className={`px-2 py-1 rounded text-xs border ${getStatusBadgeColor(selectedDeployment.status)}`}>
                    {selectedDeployment.status.replace('_', ' ')}
                  </span>
                </div>
                
                {showLogs ? (
                  <div className="space-y-4">
                    <h4 className="text-white font-medium">Deployment Logs</h4>
                    <div className="bg-gray-900 rounded-lg p-3 max-h-96 overflow-y-auto font-mono text-sm">
                      {selectedDeployment.logs.length === 0 ? (
                        <div className="text-center py-4 text-gray-500">
                          No logs available for this deployment
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {selectedDeployment.logs.map((log, index) => (
                            <div key={index} className="flex items-start space-x-2">
                              <span className="text-gray-500">[{log.timestamp.toLocaleTimeString()}]</span>
                              <span className={getLogLevelColor(log.level)}>
                                {getLogLevelIcon(log.level)}
                              </span>
                              <span className="text-gray-300">{log.message}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="bg-gray-700 rounded-lg p-4">
                      <h4 className="text-white font-medium mb-3">Target Information</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Name</span>
                          <span className="text-white">{selectedDeployment.target.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Type</span>
                          <span className="text-white">{selectedDeployment.target.type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">IP Address</span>
                          <span className="text-white">{selectedDeployment.target.ipAddress}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-700 rounded-lg p-4">
                      <h4 className="text-white font-medium mb-3">Deployment Information</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Deployed By</span>
                          <span className="text-white">{selectedDeployment.deployedBy}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">
                            {selectedDeployment.status === 'scheduled' ? 'Scheduled For' : 'Deployed At'}
                          </span>
                          <span className="text-white">{formatTimestamp(selectedDeployment.deployedAt)}</span>
                        </div>
                        {selectedDeployment.completedAt && (
                          <div className="flex justify-between">
                            <span className="text-gray-400">Completed At</span>
                            <span className="text-white">{formatTimestamp(selectedDeployment.completedAt)}</span>
                          </div>
                        )}
                        {selectedDeployment.completedAt && (
                          <div className="flex justify-between">
                            <span className="text-gray-400">Duration</span>
                            <span className="text-white">{calculateDuration(selectedDeployment.deployedAt, selectedDeployment.completedAt)}</span>
                          </div>
                        )}
                        {selectedDeployment.previousVersion && (
                          <div className="flex justify-between">
                            <span className="text-gray-400">Previous Version</span>
                            <span className="text-white">{selectedDeployment.previousVersion}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="bg-gray-700 rounded-lg p-4">
                      <h4 className="text-white font-medium mb-3">Approvals</h4>
                      <div className="space-y-2">
                        {selectedDeployment.approvals.map((approval, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <span className="text-white">{approval.user}</span>
                            <div className="flex items-center space-x-2">
                              {approval.status === 'approved' ? (
                                <span className="text-green-400 text-sm flex items-center space-x-1">
                                  <CheckCircle2 className="w-4 h-4" />
                                  <span>Approved</span>
                                </span>
                              ) : approval.status === 'rejected' ? (
                                <span className="text-red-400 text-sm flex items-center space-x-1">
                                  <XCircle className="w-4 h-4" />
                                  <span>Rejected</span>
                                </span>
                              ) : (
                                <span className="text-yellow-400 text-sm flex items-center space-x-1">
                                  <Clock className="w-4 h-4" />
                                  <span>Pending</span>
                                </span>
                              )}
                              {approval.timestamp && (
                                <span className="text-xs text-gray-400">{formatTimestamp(approval.timestamp)}</span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="p-4 border-t border-gray-700">
                <div className="flex space-x-3">
                  {selectedDeployment.status === 'in_progress' && (
                    <button
                      onClick={() => handleCancelDeployment(selectedDeployment.id)}
                      className="flex-1 flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <X className="w-4 h-4" />
                      <span>Cancel Deployment</span>
                    </button>
                  )}
                  
                  {selectedDeployment.status === 'success' && selectedDeployment.rollbackAvailable && (
                    <button
                      onClick={() => handleRollbackDeployment(selectedDeployment.id)}
                      className="flex-1 flex items-center justify-center space-x-2 bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <RotateCcw className="w-4 h-4" />
                      <span>Rollback</span>
                    </button>
                  )}
                  
                  {selectedDeployment.status === 'failed' && (
                    <button
                      onClick={() => {
                        // Retry deployment
                      }}
                      className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>Retry Deployment</span>
                    </button>
                  )}
                  
                  {selectedDeployment.status === 'pending' && (
                    <button
                      onClick={() => {
                        // Approve deployment
                      }}
                      className="flex-1 flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <CheckCircle2 className="w-4 h-4" />
                      <span>Approve</span>
                    </button>
                  )}
                  
                  {selectedDeployment.status === 'scheduled' && (
                    <button
                      onClick={() => {
                        // Cancel scheduled deployment
                      }}
                      className="flex-1 flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <X className="w-4 h-4" />
                      <span>Cancel Schedule</span>
                    </button>
                  )}
                  
                  <button
                    onClick={() => {
                      // View detailed logs
                      setShowLogs(true);
                    }}
                    className="flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <Eye className="w-4 h-4" />
                    <span>View Details</span>
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full flex flex-col items-center justify-center p-6">
              <Package className="w-16 h-16 text-gray-600 mb-4" />
              <h3 className="text-lg font-medium text-white">No Deployment Selected</h3>
              <p className="text-gray-400 mt-2 text-center">Select a deployment from the list to view details</p>
            </div>
          )}
        </div>
      </div>

      {/* New Deployment Modal */}
      {showNewDeploymentModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">New Deployment</h3>
              <button
                onClick={() => setShowNewDeploymentModal(false)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Project</label>
                <select
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a project</option>
                  <option value="1">Production Line A</option>
                  <option value="2">Packaging System</option>
                  <option value="3">HVAC Control</option>
                  <option value="4">Conveyor System</option>
                  <option value="5">Safety System</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Version</label>
                <input
                  type="text"
                  placeholder="e.g., 1.0.0"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Target PLC</label>
                <select
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a target</option>
                  <option value="1">PLC-S7-1500-A (*************)</option>
                  <option value="2">PLC-AB-CompactLogix (*************)</option>
                  <option value="3">PLC-Beckhoff-CX5140 (*************)</option>
                  <option value="4">PLC-Siemens-S7-300 (*************)</option>
                  <option value="5">PLC-Pilz-PSS4000 (*************)</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Deployment Type</label>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="deployNow"
                      name="deploymentType"
                      value="now"
                      defaultChecked
                      className="text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="deployNow" className="text-white">Deploy Now</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="deployScheduled"
                      name="deploymentType"
                      value="scheduled"
                      className="text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="deployScheduled" className="text-white">Schedule</label>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Deployment Options</label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="backupBeforeDeploy"
                      defaultChecked
                      className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="backupBeforeDeploy" className="text-white">Backup before deployment</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="validateAfterDeploy"
                      defaultChecked
                      className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="validateAfterDeploy" className="text-white">Validate after deployment</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="requireApproval"
                      className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="requireApproval" className="text-white">Require approval</label>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Comments (Optional)</label>
                <textarea
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none"
                  placeholder="Add any deployment notes or comments"
                ></textarea>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowNewDeploymentModal(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Create new deployment
                  setShowNewDeploymentModal(false);
                  alert('Deployment created successfully');
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Create Deployment
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Info icon component
const Info = ({ className }: { className?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="12" y1="16" x2="12" y2="12"></line>
    <line x1="12" y1="8" x2="12.01" y2="8"></line>
  </svg>
);

export default DeploymentManager;