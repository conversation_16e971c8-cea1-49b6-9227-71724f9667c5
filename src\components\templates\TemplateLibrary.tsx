import React, { useState } from 'react';
import { PLCTemplate } from '../../types/plc';
import { usePLCStore } from '../../store/plcStore';
import { 
  BookOpen, 
  Search, 
  Download, 
  Star, 
  Filter,
  Zap,
  Shield,
  Gauge,
  Wifi,
  Plus,
  Code,
  Grid3X3,
  CheckCircle2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from 'lucide-react';

const TemplateLibrary: React.FC = () => {
  const { createProgram, currentProject, updateProgram } = usePLCStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<PLCTemplate | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  // Comprehensive template library with multiple rungs
  const templates: PLCTemplate[] = [
    {
      id: 'motor-start-stop',
      name: 'Motor Start/Stop Control',
      category: 'Motor Control',
      description: 'Complete 3-wire motor control with start/stop buttons, overload protection, and status indicators',
      content: [
        {
          id: 'rung-0',
          number: 0,
          elements: [
            {
              id: 'start-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Start_PB',
              properties: { normally: 'open' },
              connections: ['aux-contact']
            },
            {
              id: 'aux-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Motor_Run',
              properties: { normally: 'open' },
              connections: ['stop-contact']
            },
            {
              id: 'stop-contact',
              type: 'contact',
              position: { x: 5, y: 0 },
              tag: 'Stop_PB',
              properties: { normally: 'closed' },
              connections: ['overload-contact']
            },
            {
              id: 'overload-contact',
              type: 'contact',
              position: { x: 7, y: 0 },
              tag: 'Motor_OL',
              properties: { normally: 'closed' },
              connections: ['motor-coil']
            },
            {
              id: 'motor-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Motor_Run',
              properties: {},
              connections: []
            }
          ],
          comment: 'Motor start/stop with seal-in circuit',
          enabled: true
        },
        {
          id: 'rung-1',
          number: 1,
          elements: [
            {
              id: 'motor-run-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Motor_Run',
              properties: { normally: 'open' },
              connections: ['motor-output-coil']
            },
            {
              id: 'motor-output-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Motor_Output',
              properties: {},
              connections: []
            }
          ],
          comment: 'Motor output control',
          enabled: true
        },
        {
          id: 'rung-2',
          number: 2,
          elements: [
            {
              id: 'motor-run-contact-2',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Motor_Run',
              properties: { normally: 'open' },
              connections: ['run-timer']
            },
            {
              id: 'run-timer',
              type: 'timer',
              position: { x: 3, y: 0 },
              tag: 'Run_Timer',
              properties: { timerType: 'TON', preset: 'T#5S' },
              connections: ['run-light-coil']
            },
            {
              id: 'run-light-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Run_Light',
              properties: {},
              connections: []
            }
          ],
          comment: 'Run light with 5 second delay',
          enabled: true
        },
        {
          id: 'rung-3',
          number: 3,
          elements: [
            {
              id: 'motor-run-contact-3',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Motor_Run',
              properties: { normally: 'open' },
              connections: ['motor-fault-contact']
            },
            {
              id: 'motor-fault-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Motor_Fault',
              properties: { normally: 'closed' },
              connections: ['run-status-coil']
            },
            {
              id: 'run-status-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Run_Status',
              properties: {},
              connections: []
            }
          ],
          comment: 'Run status indicator',
          enabled: true
        },
        {
          id: 'rung-4',
          number: 4,
          elements: [
            {
              id: 'estop-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Emergency_Stop',
              properties: { normally: 'closed' },
              connections: ['fault-coil']
            },
            {
              id: 'fault-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'System_Fault',
              properties: {},
              connections: []
            }
          ],
          comment: 'System fault detection',
          enabled: true
        },
        {
          id: 'rung-5',
          number: 5,
          elements: [
            {
              id: 'motor-run-contact-4',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Motor_Run',
              properties: { normally: 'open' },
              connections: ['run-counter']
            },
            {
              id: 'run-counter',
              type: 'counter',
              position: { x: 3, y: 0 },
              tag: 'Run_Counter',
              properties: { counterType: 'CTU', preset: 10 },
              connections: ['maintenance-coil']
            },
            {
              id: 'maintenance-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Maintenance_Due',
              properties: {},
              connections: []
            }
          ],
          comment: 'Maintenance counter',
          enabled: true
        },
        {
          id: 'rung-6',
          number: 6,
          elements: [
            {
              id: 'reset-pb-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Reset_PB',
              properties: { normally: 'open' },
              connections: ['reset-coil']
            },
            {
              id: 'reset-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'System_Reset',
              properties: {},
              connections: []
            }
          ],
          comment: 'System reset',
          enabled: true
        },
        {
          id: 'rung-7',
          number: 7,
          elements: [
            {
              id: 'auto-mode-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Auto_Mode',
              properties: { normally: 'open' },
              connections: ['auto-start-timer']
            },
            {
              id: 'auto-start-timer',
              type: 'timer',
              position: { x: 3, y: 0 },
              tag: 'Auto_Start_Delay',
              properties: { timerType: 'TON', preset: 'T#10S' },
              connections: ['auto-start-coil']
            },
            {
              id: 'auto-start-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Auto_Start',
              properties: {},
              connections: []
            }
          ],
          comment: 'Automatic start sequence',
          enabled: true
        },
        {
          id: 'rung-8',
          number: 8,
          elements: [
            {
              id: 'motor-run-contact-5',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Motor_Run',
              properties: { normally: 'open' },
              connections: ['run-time-counter']
            },
            {
              id: 'run-time-counter',
              type: 'timer',
              position: { x: 3, y: 0 },
              tag: 'Run_Time_Counter',
              properties: { timerType: 'TON', preset: 'T#3600S' },
              connections: ['hour-meter-coil']
            },
            {
              id: 'hour-meter-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Hour_Meter',
              properties: {},
              connections: []
            }
          ],
          comment: 'Run time hour meter',
          enabled: true
        },
        {
          id: 'rung-9',
          number: 9,
          elements: [
            {
              id: 'manual-mode-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Manual_Mode',
              properties: { normally: 'open' },
              connections: ['jog-pb-contact']
            },
            {
              id: 'jog-pb-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Jog_PB',
              properties: { normally: 'open' },
              connections: ['jog-coil']
            },
            {
              id: 'jog-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Jog_Output',
              properties: {},
              connections: []
            }
          ],
          comment: 'Manual jog control',
          enabled: true
        }
      ],
      tags: [
        {
          id: 'start-pb-tag',
          name: 'Start_PB',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Motor start pushbutton'
        },
        {
          id: 'stop-pb-tag',
          name: 'Stop_PB',
          type: 'BOOL',
          value: true,
          scope: 'INPUT',
          description: 'Motor stop pushbutton (NC)'
        },
        {
          id: 'motor-ol-tag',
          name: 'Motor_OL',
          type: 'BOOL',
          value: true,
          scope: 'INPUT',
          description: 'Motor overload contact (NC)'
        },
        {
          id: 'motor-run-tag',
          name: 'Motor_Run',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Motor run output'
        },
        {
          id: 'motor-output-tag',
          name: 'Motor_Output',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Motor contactor output'
        },
        {
          id: 'run-light-tag',
          name: 'Run_Light',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Run indicator light'
        },
        {
          id: 'run-timer-tag',
          name: 'Run_Timer',
          type: 'TIMER',
          value: 0,
          scope: 'LOCAL',
          description: 'Run delay timer'
        },
        {
          id: 'emergency-stop-tag',
          name: 'Emergency_Stop',
          type: 'BOOL',
          value: true,
          scope: 'INPUT',
          description: 'Emergency stop button (NC)'
        },
        {
          id: 'system-fault-tag',
          name: 'System_Fault',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'System fault indicator'
        },
        {
          id: 'motor-fault-tag',
          name: 'Motor_Fault',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Motor fault input'
        },
        {
          id: 'run-status-tag',
          name: 'Run_Status',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Run status indicator'
        },
        {
          id: 'run-counter-tag',
          name: 'Run_Counter',
          type: 'COUNTER',
          value: 0,
          scope: 'LOCAL',
          description: 'Run counter for maintenance'
        },
        {
          id: 'maintenance-due-tag',
          name: 'Maintenance_Due',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Maintenance due indicator'
        },
        {
          id: 'reset-pb-tag',
          name: 'Reset_PB',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Reset pushbutton'
        },
        {
          id: 'system-reset-tag',
          name: 'System_Reset',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'System reset command'
        }
      ],
      vendor: 'Generic'
    },
    {
      id: 'safety-light-curtain',
      name: 'Safety Light Curtain',
      category: 'Safety',
      description: 'Safety-rated light curtain with muting function and reset capability',
      content: [
        {
          id: 'rung-0',
          number: 0,
          elements: [
            {
              id: 'light-curtain-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Light_Curtain_OK',
              properties: { normally: 'open' },
              connections: ['safety-ok-coil']
            },
            {
              id: 'safety-ok-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Safety_OK',
              properties: {},
              connections: []
            }
          ],
          comment: 'Basic light curtain safety',
          enabled: true
        },
        {
          id: 'rung-1',
          number: 1,
          elements: [
            {
              id: 'muting-sensor1-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Muting_Sensor_1',
              properties: { normally: 'open' },
              connections: ['muting-sensor2-contact']
            },
            {
              id: 'muting-sensor2-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Muting_Sensor_2',
              properties: { normally: 'open' },
              connections: ['muting-enable-contact']
            },
            {
              id: 'muting-enable-contact',
              type: 'contact',
              position: { x: 5, y: 0 },
              tag: 'Muting_Enable',
              properties: { normally: 'open' },
              connections: ['muting-timer']
            },
            {
              id: 'muting-timer',
              type: 'timer',
              position: { x: 7, y: 0 },
              tag: 'Muting_Timer',
              properties: { timerType: 'TON', preset: 'T#30S' },
              connections: ['muting-active-coil']
            },
            {
              id: 'muting-active-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Muting_Active',
              properties: {},
              connections: []
            }
          ],
          comment: 'Muting logic with timer',
          enabled: true
        },
        {
          id: 'rung-2',
          number: 2,
          elements: [
            {
              id: 'light-curtain-ok-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Light_Curtain_OK',
              properties: { normally: 'open' },
              connections: ['muting-active-contact']
            },
            {
              id: 'muting-active-contact',
              type: 'contact',
              position: { x: 3, y: 1 },
              tag: 'Muting_Active',
              properties: { normally: 'open' },
              connections: ['fault-present-contact']
            },
            {
              id: 'fault-present-contact',
              type: 'contact',
              position: { x: 5, y: 0 },
              tag: 'Fault_Present',
              properties: { normally: 'closed' },
              connections: ['safety-output-coil']
            },
            {
              id: 'safety-output-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Safety_Output',
              properties: {},
              connections: []
            }
          ],
          comment: 'Safety output with muting override',
          enabled: true
        },
        {
          id: 'rung-3',
          number: 3,
          elements: [
            {
              id: 'light-curtain-ok-contact-2',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Light_Curtain_OK',
              properties: { normally: 'closed' },
              connections: ['muting-active-contact-2']
            },
            {
              id: 'muting-active-contact-2',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Muting_Active',
              properties: { normally: 'closed' },
              connections: ['fault-memory-coil']
            },
            {
              id: 'fault-memory-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Fault_Memory',
              properties: {},
              connections: []
            }
          ],
          comment: 'Fault detection',
          enabled: true
        },
        {
          id: 'rung-4',
          number: 4,
          elements: [
            {
              id: 'reset-button-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Reset_Button',
              properties: { normally: 'open' },
              connections: ['light-curtain-ok-contact-3']
            },
            {
              id: 'light-curtain-ok-contact-3',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Light_Curtain_OK',
              properties: { normally: 'open' },
              connections: ['fault-memory-reset-coil']
            },
            {
              id: 'fault-memory-reset-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Fault_Memory',
              properties: { coilType: 'reset' },
              connections: []
            }
          ],
          comment: 'Fault reset',
          enabled: true
        },
        {
          id: 'rung-5',
          number: 5,
          elements: [
            {
              id: 'fault-memory-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Fault_Memory',
              properties: { normally: 'open' },
              connections: ['fault-present-coil']
            },
            {
              id: 'fault-present-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Fault_Present',
              properties: {},
              connections: []
            }
          ],
          comment: 'Fault present indicator',
          enabled: true
        },
        {
          id: 'rung-6',
          number: 6,
          elements: [
            {
              id: 'safety-output-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Safety_Output',
              properties: { normally: 'open' },
              connections: ['enable-production-contact']
            },
            {
              id: 'enable-production-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Enable_Production',
              properties: { normally: 'open' },
              connections: ['machine-enable-coil']
            },
            {
              id: 'machine-enable-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Machine_Enable',
              properties: {},
              connections: []
            }
          ],
          comment: 'Machine enable logic',
          enabled: true
        },
        {
          id: 'rung-7',
          number: 7,
          elements: [
            {
              id: 'safety-output-contact-2',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Safety_Output',
              properties: { normally: 'closed' },
              connections: ['safety-fault-alarm-coil']
            },
            {
              id: 'safety-fault-alarm-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Safety_Fault_Alarm',
              properties: {},
              connections: []
            }
          ],
          comment: 'Safety fault alarm',
          enabled: true
        },
        {
          id: 'rung-8',
          number: 8,
          elements: [
            {
              id: 'muting-active-contact-3',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Muting_Active',
              properties: { normally: 'open' },
              connections: ['muting-lamp-coil']
            },
            {
              id: 'muting-lamp-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Muting_Lamp',
              properties: {},
              connections: []
            }
          ],
          comment: 'Muting lamp indicator',
          enabled: true
        },
        {
          id: 'rung-9',
          number: 9,
          elements: [
            {
              id: 'muting-timeout-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Muting_Timeout',
              properties: { normally: 'open' },
              connections: ['muting-timeout-alarm-coil']
            },
            {
              id: 'muting-timeout-alarm-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Muting_Timeout_Alarm',
              properties: {},
              connections: []
            }
          ],
          comment: 'Muting timeout alarm',
          enabled: true
        }
      ],
      tags: [
        {
          id: 'light-curtain-tag',
          name: 'Light_Curtain_OK',
          type: 'BOOL',
          value: true,
          scope: 'INPUT',
          description: 'Light curtain status input',
          safetyRated: true,
          silLevel: 'SIL3'
        },
        {
          id: 'muting-sensor1-tag',
          name: 'Muting_Sensor_1',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'First muting sensor',
          safetyRated: true
        },
        {
          id: 'muting-sensor2-tag',
          name: 'Muting_Sensor_2',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Second muting sensor',
          safetyRated: true
        },
        {
          id: 'muting-enable-tag',
          name: 'Muting_Enable',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Muting enable switch',
          safetyRated: true
        },
        {
          id: 'reset-button-tag',
          name: 'Reset_Button',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Safety reset button',
          safetyRated: true
        },
        {
          id: 'enable-production-tag',
          name: 'Enable_Production',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Production enable switch'
        },
        {
          id: 'safety-ok-tag',
          name: 'Safety_OK',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Safety system OK status',
          safetyRated: true,
          silLevel: 'SIL3'
        },
        {
          id: 'muting-active-tag',
          name: 'Muting_Active',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Muting active status',
          safetyRated: true
        },
        {
          id: 'fault-present-tag',
          name: 'Fault_Present',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Safety fault present',
          safetyRated: true
        },
        {
          id: 'fault-memory-tag',
          name: 'Fault_Memory',
          type: 'BOOL',
          value: false,
          scope: 'LOCAL',
          description: 'Safety fault memory',
          safetyRated: true
        },
        {
          id: 'safety-output-tag',
          name: 'Safety_Output',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Safety system output',
          safetyRated: true,
          silLevel: 'SIL3'
        },
        {
          id: 'machine-enable-tag',
          name: 'Machine_Enable',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Machine enable output',
          safetyRated: true
        },
        {
          id: 'safety-fault-alarm-tag',
          name: 'Safety_Fault_Alarm',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Safety fault alarm'
        },
        {
          id: 'muting-lamp-tag',
          name: 'Muting_Lamp',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Muting lamp indicator'
        },
        {
          id: 'muting-timeout-tag',
          name: 'Muting_Timeout',
          type: 'BOOL',
          value: false,
          scope: 'LOCAL',
          description: 'Muting timeout flag'
        }
      ],
      vendor: 'Pilz'
    },
    {
      id: 'pid-temperature',
      name: 'PID Temperature Control',
      category: 'Process Control',
      description: 'PID controller for temperature regulation with auto-tuning',
      content: `FUNCTION_BLOCK PID_Temperature_FB
VAR_INPUT
    Process_Variable : REAL;  // Current temperature
    Setpoint : REAL;          // Target temperature
    Manual_Mode : BOOL;
    Manual_Output : REAL;
    Auto_Tune : BOOL;
    Kp : REAL := 1.0;
    Ki : REAL := 0.1;
    Kd : REAL := 0.01;
END_VAR

VAR_OUTPUT
    Control_Output : REAL;
    PID_Error : REAL;
    Tuning_Complete : BOOL;
    At_Setpoint : BOOL;
END_VAR

VAR
    Error_Previous : REAL;
    Integral_Sum : REAL;
    Derivative : REAL;
    Output_Limited : REAL;
    Deadband : REAL := 0.5;
END_VAR

// Calculate error
PID_Error := Setpoint - Process_Variable;

// Check if at setpoint
At_Setpoint := ABS(PID_Error) <= Deadband;

// PID calculation
IF NOT Manual_Mode THEN
    // Proportional term
    Control_Output := Kp * PID_Error;
    
    // Integral term (with windup protection)
    IF Control_Output >= 0.0 AND Control_Output <= 100.0 THEN
        Integral_Sum := Integral_Sum + (Ki * PID_Error);
    END_IF;
    Control_Output := Control_Output + Integral_Sum;
    
    // Derivative term
    Derivative := Kd * (PID_Error - Error_Previous);
    Control_Output := Control_Output + Derivative;
    
    // Limit output
    IF Control_Output > 100.0 THEN
        Control_Output := 100.0;
    ELSIF Control_Output < 0.0 THEN
        Control_Output := 0.0;
    END_IF;
ELSE
    Control_Output := Manual_Output;
    Integral_Sum := Manual_Output; // Bumpless transfer
END_IF;

Error_Previous := PID_Error;

END_FUNCTION_BLOCK`,
      tags: [
        {
          id: 'temp-pv-tag',
          name: 'Temperature_PV',
          type: 'REAL',
          value: 20.0,
          scope: 'INPUT',
          description: 'Temperature process variable'
        },
        {
          id: 'temp-sp-tag',
          name: 'Temperature_SP',
          type: 'REAL',
          value: 25.0,
          scope: 'INPUT',
          description: 'Temperature setpoint'
        },
        {
          id: 'heater-output-tag',
          name: 'Heater_Output',
          type: 'REAL',
          value: 0.0,
          scope: 'OUTPUT',
          description: 'Heater control output (0-100%)'
        },
        {
          id: 'manual-mode-tag',
          name: 'Manual_Mode',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Manual mode enable'
        },
        {
          id: 'manual-output-tag',
          name: 'Manual_Output',
          type: 'REAL',
          value: 0.0,
          scope: 'INPUT',
          description: 'Manual output value'
        },
        {
          id: 'auto-tune-tag',
          name: 'Auto_Tune',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Auto-tuning enable'
        },
        {
          id: 'kp-tag',
          name: 'Kp',
          type: 'REAL',
          value: 1.0,
          scope: 'INPUT',
          description: 'Proportional gain'
        },
        {
          id: 'ki-tag',
          name: 'Ki',
          type: 'REAL',
          value: 0.1,
          scope: 'INPUT',
          description: 'Integral gain'
        },
        {
          id: 'kd-tag',
          name: 'Kd',
          type: 'REAL',
          value: 0.01,
          scope: 'INPUT',
          description: 'Derivative gain'
        },
        {
          id: 'pid-error-tag',
          name: 'PID_Error',
          type: 'REAL',
          value: 0.0,
          scope: 'OUTPUT',
          description: 'PID error value'
        },
        {
          id: 'tuning-complete-tag',
          name: 'Tuning_Complete',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Auto-tuning complete flag'
        },
        {
          id: 'at-setpoint-tag',
          name: 'At_Setpoint',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'At setpoint indicator'
        }
      ],
      vendor: 'Siemens'
    },
    {
      id: 'conveyor-control',
      name: 'Conveyor Belt Control',
      category: 'Motor Control',
      description: 'Conveyor belt control with speed control and safety interlocks',
      content: [
        {
          id: 'rung-0',
          number: 0,
          elements: [
            {
              id: 'conv-start',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Conveyor_Start',
              properties: { normally: 'open' },
              connections: ['conv-run']
            },
            {
              id: 'conv-run',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Conveyor_Running',
              properties: { normally: 'open' },
              connections: ['conv-estop']
            },
            {
              id: 'conv-estop',
              type: 'contact',
              position: { x: 5, y: 0 },
              tag: 'Conveyor_EStop',
              properties: { normally: 'closed' },
              connections: ['conv-motor']
            },
            {
              id: 'conv-motor',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Conveyor_Running',
              properties: {},
              connections: []
            }
          ],
          comment: 'Conveyor start/stop logic',
          enabled: true
        },
        {
          id: 'rung-1',
          number: 1,
          elements: [
            {
              id: 'conv-running-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Conveyor_Running',
              properties: { normally: 'open' },
              connections: ['conv-fault-contact']
            },
            {
              id: 'conv-fault-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Conveyor_Fault',
              properties: { normally: 'closed' },
              connections: ['conv-motor-output']
            },
            {
              id: 'conv-motor-output',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Conveyor_Motor',
              properties: {},
              connections: []
            }
          ],
          comment: 'Conveyor motor control',
          enabled: true
        },
        {
          id: 'rung-2',
          number: 2,
          elements: [
            {
              id: 'conv-running-contact-2',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Conveyor_Running',
              properties: { normally: 'open' },
              connections: ['speed-control-function']
            },
            {
              id: 'speed-control-function',
              type: 'function',
              position: { x: 3, y: 0 },
              tag: 'Speed_Control',
              properties: { functionType: 'math', operator: '*' },
              connections: ['conv-speed-output']
            },
            {
              id: 'conv-speed-output',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Conveyor_Speed',
              properties: {},
              connections: []
            }
          ],
          comment: 'Conveyor speed control',
          enabled: true
        },
        {
          id: 'rung-3',
          number: 3,
          elements: [
            {
              id: 'conv-start-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Conveyor_Start',
              properties: { normally: 'open' },
              connections: ['start-delay-timer']
            },
            {
              id: 'start-delay-timer',
              type: 'timer',
              position: { x: 3, y: 0 },
              tag: 'Start_Delay',
              properties: { timerType: 'TON', preset: 'T#2S' },
              connections: ['start-light-coil']
            },
            {
              id: 'start-light-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Start_Light',
              properties: {},
              connections: []
            }
          ],
          comment: 'Start light with delay',
          enabled: true
        },
        {
          id: 'rung-4',
          number: 4,
          elements: [
            {
              id: 'conv-running-contact-3',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Conveyor_Running',
              properties: { normally: 'open' },
              connections: ['run-timer']
            },
            {
              id: 'run-timer',
              type: 'timer',
              position: { x: 3, y: 0 },
              tag: 'Run_Timer',
              properties: { timerType: 'TON', preset: 'T#3600S' },
              connections: ['maintenance-counter']
            },
            {
              id: 'maintenance-counter',
              type: 'counter',
              position: { x: 5, y: 0 },
              tag: 'Maintenance_Counter',
              properties: { counterType: 'CTU', preset: 24 },
              connections: ['maintenance-due-coil']
            },
            {
              id: 'maintenance-due-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Maintenance_Due',
              properties: {},
              connections: []
            }
          ],
          comment: 'Maintenance tracking',
          enabled: true
        },
        {
          id: 'rung-5',
          number: 5,
          elements: [
            {
              id: 'reset-pb-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Reset_PB',
              properties: { normally: 'open' },
              connections: ['maintenance-counter-reset']
            },
            {
              id: 'maintenance-counter-reset',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Maintenance_Counter',
              properties: { coilType: 'reset' },
              connections: []
            }
          ],
          comment: 'Reset maintenance counter',
          enabled: true
        },
        {
          id: 'rung-6',
          number: 6,
          elements: [
            {
              id: 'conv-running-contact-4',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Conveyor_Running',
              properties: { normally: 'open' },
              connections: ['jam-sensor-contact']
            },
            {
              id: 'jam-sensor-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Jam_Sensor',
              properties: { normally: 'closed' },
              connections: ['jam-timer']
            },
            {
              id: 'jam-timer',
              type: 'timer',
              position: { x: 5, y: 0 },
              tag: 'Jam_Timer',
              properties: { timerType: 'TON', preset: 'T#5S' },
              connections: ['jam-alarm-coil']
            },
            {
              id: 'jam-alarm-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Jam_Alarm',
              properties: {},
              connections: []
            }
          ],
          comment: 'Jam detection',
          enabled: true
        },
        {
          id: 'rung-7',
          number: 7,
          elements: [
            {
              id: 'jam-alarm-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Jam_Alarm',
              properties: { normally: 'open' },
              connections: ['auto-stop-coil']
            },
            {
              id: 'auto-stop-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Auto_Stop',
              properties: {},
              connections: []
            }
          ],
          comment: 'Auto stop on jam',
          enabled: true
        },
        {
          id: 'rung-8',
          number: 8,
          elements: [
            {
              id: 'auto-mode-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Auto_Mode',
              properties: { normally: 'open' },
              connections: ['product-sensor-contact']
            },
            {
              id: 'product-sensor-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Product_Sensor',
              properties: { normally: 'open' },
              connections: ['auto-start-coil']
            },
            {
              id: 'auto-start-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Auto_Start',
              properties: {},
              connections: []
            }
          ],
          comment: 'Auto start on product detection',
          enabled: true
        },
        {
          id: 'rung-9',
          number: 9,
          elements: [
            {
              id: 'auto-start-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Auto_Start',
              properties: { normally: 'open' },
              connections: ['auto-stop-contact']
            },
            {
              id: 'auto-stop-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Auto_Stop',
              properties: { normally: 'closed' },
              connections: ['conveyor-start-coil']
            },
            {
              id: 'conveyor-start-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Conveyor_Start',
              properties: {},
              connections: []
            }
          ],
          comment: 'Auto start/stop control',
          enabled: true
        }
      ],
      tags: [
        {
          id: 'conv-start-tag',
          name: 'Conveyor_Start',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Conveyor start button'
        },
        {
          id: 'conv-running-tag',
          name: 'Conveyor_Running',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Conveyor running status'
        },
        {
          id: 'conv-estop-tag',
          name: 'Conveyor_EStop',
          type: 'BOOL',
          value: true,
          scope: 'INPUT',
          description: 'Conveyor emergency stop (NC)'
        },
        {
          id: 'conv-motor-tag',
          name: 'Conveyor_Motor',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Conveyor motor output'
        },
        {
          id: 'conv-speed-tag',
          name: 'Conveyor_Speed',
          type: 'REAL',
          value: 0.0,
          scope: 'OUTPUT',
          description: 'Conveyor speed (0-100%)'
        },
        {
          id: 'speed-control-tag',
          name: 'Speed_Control',
          type: 'REAL',
          value: 50.0,
          scope: 'INPUT',
          description: 'Speed control setpoint'
        },
        {
          id: 'start-delay-tag',
          name: 'Start_Delay',
          type: 'TIMER',
          value: 0,
          scope: 'LOCAL',
          description: 'Start delay timer'
        },
        {
          id: 'start-light-tag',
          name: 'Start_Light',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Start indicator light'
        },
        {
          id: 'run-timer-tag',
          name: 'Run_Timer',
          type: 'TIMER',
          value: 0,
          scope: 'LOCAL',
          description: 'Run time timer'
        },
        {
          id: 'maintenance-counter-tag',
          name: 'Maintenance_Counter',
          type: 'COUNTER',
          value: 0,
          scope: 'LOCAL',
          description: 'Maintenance hour counter'
        },
        {
          id: 'maintenance-due-tag',
          name: 'Maintenance_Due',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Maintenance due indicator'
        },
        {
          id: 'reset-pb-tag',
          name: 'Reset_PB',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Reset pushbutton'
        },
        {
          id: 'jam-sensor-tag',
          name: 'Jam_Sensor',
          type: 'BOOL',
          value: true,
          scope: 'INPUT',
          description: 'Jam detection sensor (NC)'
        },
        {
          id: 'jam-timer-tag',
          name: 'Jam_Timer',
          type: 'TIMER',
          value: 0,
          scope: 'LOCAL',
          description: 'Jam detection timer'
        },
        {
          id: 'jam-alarm-tag',
          name: 'Jam_Alarm',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Jam alarm indicator'
        }
      ],
      vendor: 'Generic'
    },
    {
      id: 'tank-level-control',
      name: 'Tank Level Control',
      category: 'Process Control',
      description: 'Automatic tank level control with pump and valve control',
      content: [
        {
          id: 'rung-0',
          number: 0,
          elements: [
            {
              id: 'level-low-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Level_Low',
              properties: { normally: 'open' },
              connections: ['level-high-contact']
            },
            {
              id: 'level-high-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Level_High',
              properties: { normally: 'closed' },
              connections: ['emergency-stop-contact']
            },
            {
              id: 'emergency-stop-contact',
              type: 'contact',
              position: { x: 5, y: 0 },
              tag: 'Emergency_Stop',
              properties: { normally: 'closed' },
              connections: ['fill-pump-coil']
            },
            {
              id: 'fill-pump-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Fill_Pump',
              properties: {},
              connections: []
            }
          ],
          comment: 'Fill pump control based on level',
          enabled: true
        },
        {
          id: 'rung-1',
          number: 1,
          elements: [
            {
              id: 'level-high-contact-2',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Level_High',
              properties: { normally: 'open' },
              connections: ['drain-valve-coil']
            },
            {
              id: 'drain-valve-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Drain_Valve',
              properties: {},
              connections: []
            }
          ],
          comment: 'Drain valve control',
          enabled: true
        },
        {
          id: 'rung-2',
          number: 2,
          elements: [
            {
              id: 'level-sensor-function',
              type: 'function',
              position: { x: 1, y: 0 },
              tag: 'Level_Sensor',
              properties: { functionType: 'compare', operator: '<' },
              connections: ['level-low-coil']
            },
            {
              id: 'level-low-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Level_Low',
              properties: {},
              connections: []
            }
          ],
          comment: 'Low level detection',
          enabled: true
        },
        {
          id: 'rung-3',
          number: 3,
          elements: [
            {
              id: 'level-sensor-function-2',
              type: 'function',
              position: { x: 1, y: 0 },
              tag: 'Level_Sensor',
              properties: { functionType: 'compare', operator: '>' },
              connections: ['level-high-coil']
            },
            {
              id: 'level-high-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Level_High',
              properties: {},
              connections: []
            }
          ],
          comment: 'High level detection',
          enabled: true
        },
        {
          id: 'rung-4',
          number: 4,
          elements: [
            {
              id: 'level-sensor-function-3',
              type: 'function',
              position: { x: 1, y: 0 },
              tag: 'Level_Sensor',
              properties: { functionType: 'compare', operator: '>' },
              connections: ['high-alarm-timer']
            },
            {
              id: 'high-alarm-timer',
              type: 'timer',
              position: { x: 3, y: 0 },
              tag: 'High_Alarm_Delay',
              properties: { timerType: 'TON', preset: 'T#5S' },
              connections: ['high-level-alarm-coil']
            },
            {
              id: 'high-level-alarm-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'High_Level_Alarm',
              properties: {},
              connections: []
            }
          ],
          comment: 'High level alarm with delay',
          enabled: true
        },
        {
          id: 'rung-5',
          number: 5,
          elements: [
            {
              id: 'level-sensor-function-4',
              type: 'function',
              position: { x: 1, y: 0 },
              tag: 'Level_Sensor',
              properties: { functionType: 'compare', operator: '<' },
              connections: ['low-alarm-timer']
            },
            {
              id: 'low-alarm-timer',
              type: 'timer',
              position: { x: 3, y: 0 },
              tag: 'Low_Alarm_Delay',
              properties: { timerType: 'TON', preset: 'T#5S' },
              connections: ['low-level-alarm-coil']
            },
            {
              id: 'low-level-alarm-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Low_Level_Alarm',
              properties: {},
              connections: []
            }
          ],
          comment: 'Low level alarm with delay',
          enabled: true
        },
        {
          id: 'rung-6',
          number: 6,
          elements: [
            {
              id: 'manual-mode-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Manual_Mode',
              properties: { normally: 'open' },
              connections: ['manual-pump-contact']
            },
            {
              id: 'manual-pump-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Manual_Pump',
              properties: { normally: 'open' },
              connections: ['emergency-stop-contact-2']
            },
            {
              id: 'emergency-stop-contact-2',
              type: 'contact',
              position: { x: 5, y: 0 },
              tag: 'Emergency_Stop',
              properties: { normally: 'closed' },
              connections: ['manual-pump-output']
            },
            {
              id: 'manual-pump-output',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Fill_Pump',
              properties: {},
              connections: []
            }
          ],
          comment: 'Manual pump control',
          enabled: true
        },
        {
          id: 'rung-7',
          number: 7,
          elements: [
            {
              id: 'manual-mode-contact-2',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Manual_Mode',
              properties: { normally: 'open' },
              connections: ['manual-valve-contact']
            },
            {
              id: 'manual-valve-contact',
              type: 'contact',
              position: { x: 3, y: 0 },
              tag: 'Manual_Valve',
              properties: { normally: 'open' },
              connections: ['emergency-stop-contact-3']
            },
            {
              id: 'emergency-stop-contact-3',
              type: 'contact',
              position: { x: 5, y: 0 },
              tag: 'Emergency_Stop',
              properties: { normally: 'closed' },
              connections: ['manual-valve-output']
            },
            {
              id: 'manual-valve-output',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Drain_Valve',
              properties: {},
              connections: []
            }
          ],
          comment: 'Manual valve control',
          enabled: true
        },
        {
          id: 'rung-8',
          number: 8,
          elements: [
            {
              id: 'emergency-stop-contact-4',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Emergency_Stop',
              properties: { normally: 'open' },
              connections: ['emergency-drain-coil']
            },
            {
              id: 'emergency-drain-coil',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Drain_Valve',
              properties: {},
              connections: []
            }
          ],
          comment: 'Emergency drain',
          enabled: true
        },
        {
          id: 'rung-9',
          number: 9,
          elements: [
            {
              id: 'fill-pump-contact',
              type: 'contact',
              position: { x: 1, y: 0 },
              tag: 'Fill_Pump',
              properties: { normally: 'open' },
              connections: ['pump-running-light']
            },
            {
              id: 'pump-running-light',
              type: 'coil',
              position: { x: 10, y: 0 },
              tag: 'Pump_Running_Light',
              properties: {},
              connections: []
            }
          ],
          comment: 'Pump running indicator',
          enabled: true
        }
      ],
      tags: [
        {
          id: 'level-sensor-tag',
          name: 'Level_Sensor',
          type: 'REAL',
          value: 50.0,
          scope: 'INPUT',
          description: 'Tank level sensor (0-100%)'
        },
        {
          id: 'level-low-tag',
          name: 'Level_Low',
          type: 'BOOL',
          value: false,
          scope: 'LOCAL',
          description: 'Low level flag'
        },
        {
          id: 'level-high-tag',
          name: 'Level_High',
          type: 'BOOL',
          value: false,
          scope: 'LOCAL',
          description: 'High level flag'
        },
        {
          id: 'fill-pump-tag',
          name: 'Fill_Pump',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Fill pump control'
        },
        {
          id: 'drain-valve-tag',
          name: 'Drain_Valve',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Drain valve control'
        },
        {
          id: 'high-level-alarm-tag',
          name: 'High_Level_Alarm',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'High level alarm'
        },
        {
          id: 'low-level-alarm-tag',
          name: 'Low_Level_Alarm',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Low level alarm'
        },
        {
          id: 'emergency-stop-tag',
          name: 'Emergency_Stop',
          type: 'BOOL',
          value: true,
          scope: 'INPUT',
          description: 'Emergency stop button (NC)'
        },
        {
          id: 'manual-mode-tag',
          name: 'Manual_Mode',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Manual mode selector'
        },
        {
          id: 'manual-pump-tag',
          name: 'Manual_Pump',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Manual pump control'
        },
        {
          id: 'manual-valve-tag',
          name: 'Manual_Valve',
          type: 'BOOL',
          value: false,
          scope: 'INPUT',
          description: 'Manual valve control'
        },
        {
          id: 'pump-running-light-tag',
          name: 'Pump_Running_Light',
          type: 'BOOL',
          value: false,
          scope: 'OUTPUT',
          description: 'Pump running indicator light'
        }
      ],
      vendor: 'Generic'
    }
  ];

  const categories = ['all', 'Motor Control', 'Safety', 'Process Control', 'Communication'];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Motor Control': return <Zap className="w-4 h-4" />;
      case 'Safety': return <Shield className="w-4 h-4" />;
      case 'Process Control': return <Gauge className="w-4 h-4" />;
      case 'Communication': return <Wifi className="w-4 h-4" />;
      default: return <BookOpen className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Motor Control': return 'text-yellow-400 bg-yellow-400/20 border-yellow-400/30';
      case 'Safety': return 'text-red-400 bg-red-400/20 border-red-400/30';
      case 'Process Control': return 'text-green-400 bg-green-400/20 border-green-400/30';
      case 'Communication': return 'text-blue-400 bg-blue-400/20 border-blue-400/30';
      default: return 'text-gray-400 bg-gray-400/20 border-gray-400/30';
    }
  };

  const handleUseTemplate = (template: PLCTemplate) => {
    const programName = prompt(`Enter name for new program based on "${template.name}":`);
    if (!programName) return;
    
    const programType = typeof template.content === 'string' ? 'st' : 'ladder';
    createProgram(programName, programType);
    
    // Get the newly created program
    const newProgram = currentProject?.programs.find(p => p.name === programName);
    if (newProgram) {
      // Update with template content
      updateProgram(newProgram.id, {
        content: template.content
      });
      
      // Add template tags to global tags if they don't exist
      if (currentProject && template.tags) {
        const { createTag } = usePLCStore.getState();
        template.tags.forEach(tag => {
          if (!currentProject.globalTags.some(t => t.name === tag.name)) {
            createTag({
              name: tag.name,
              type: tag.type,
              value: tag.value,
              scope: tag.scope,
              description: tag.description,
              safetyRated: tag.safetyRated,
              silLevel: tag.silLevel
            });
          }
        });
      }
    }
  };

  const renderTemplatePreview = () => {
    if (!selectedTemplate) return null;

    const isLadder = Array.isArray(selectedTemplate.content);
    
    return (
      <div className="bg-gray-800/50 rounded-lg p-4 mt-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-white font-medium">Code Preview</h4>
          <div className="flex items-center space-x-2">
            {isLadder ? <Grid3X3 className="w-4 h-4 text-accent" /> : <Code className="w-4 h-4 text-success" />}
            <span className="text-xs text-gray-400">{isLadder ? 'Ladder Logic' : 'Structured Text'}</span>
          </div>
        </div>
        
        <div className="bg-gray-900 rounded p-3 text-xs text-gray-300 font-mono max-h-40 overflow-y-auto">
          {isLadder ? (
            <div>
              {(selectedTemplate.content as any[]).map((rung, index) => (
                <div key={index} className="mb-2">
                  <div className="text-blue-400">Rung {rung.number}:</div>
                  <div className="ml-2 text-gray-300">{rung.comment}</div>
                  <div className="ml-2 text-green-400">{rung.elements.length} elements</div>
                </div>
              ))}
            </div>
          ) : (
            <pre className="whitespace-pre-wrap">{(selectedTemplate.content as string).slice(0, 500)}...</pre>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="h-full bg-gray-900 flex">
      {/* Template List */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-gray-800 border-b border-gray-700 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <BookOpen className="w-5 h-5 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">Template Library</h3>
            </div>
            <div className="text-sm text-gray-400">
              {filteredTemplates.length} templates
            </div>
          </div>
          
          {/* Search and Filter */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
              />
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        {/* Template Grid */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.map(template => (
              <div
                key={template.id}
                className={`bg-gray-800 rounded-lg border border-gray-700 p-4 cursor-pointer transition-all hover:border-blue-500 hover:shadow-lg ${
                  selectedTemplate?.id === template.id ? 'border-blue-500 bg-blue-500/10' : ''
                }`}
                onClick={() => setSelectedTemplate(template)}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {getCategoryIcon(template.category)}
                    <span className={`text-xs px-2 py-1 rounded border ${getCategoryColor(template.category)}`}>
                      {template.category}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-xs text-gray-400">4.8</span>
                  </div>
                </div>
                
                <h4 className="text-white font-semibold mb-2">{template.name}</h4>
                <p className="text-gray-400 text-sm mb-3 line-clamp-2">
                  {template.description}
                </p>
                
                <div className="flex items-center justify-between mb-3">
                  <div className="text-xs text-gray-500">
                    {template.vendor}
                  </div>
                  <div className="flex items-center space-x-2">
                    {Array.isArray(template.content) ? (
                      <div className="flex items-center space-x-1 text-xs text-accent">
                        <Grid3X3 className="w-3 h-3" />
                        <span>Ladder</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1 text-xs text-success">
                        <Code className="w-3 h-3" />
                        <span>ST</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-xs text-gray-400">
                    <span>{template.tags.length} tags</span>
                    {template.tags.some(t => t.safetyRated) && (
                      <div className="flex items-center space-x-1 text-red-400">
                        <Shield className="w-3 h-3" />
                        <span>Safety</span>
                      </div>
                    )}
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleUseTemplate(template);
                    }}
                    className="flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors"
                  >
                    <Plus className="w-3 h-3" />
                    <span>Use</span>
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          {filteredTemplates.length === 0 && (
            <div className="text-center py-12 text-gray-400">
              <BookOpen className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No templates found</p>
              <p className="text-sm">Try adjusting your search or filter</p>
            </div>
          )}
        </div>
      </div>
      
      {/* Template Preview */}
      {selectedTemplate && (
        <div className="w-96 bg-gray-800 border-l border-gray-700 flex flex-col">
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-white font-semibold">{selectedTemplate.name}</h3>
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-400">4.8</span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-xs px-2 py-1 rounded border ${getCategoryColor(selectedTemplate.category)}`}>
                {selectedTemplate.category}
              </span>
              {selectedTemplate.tags.some(t => t.safetyRated) && (
                <span className="text-xs px-2 py-1 rounded border border-red-400/30 text-red-400 bg-red-400/20">
                  Safety Rated
                </span>
              )}
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <div>
              <h4 className="text-white font-medium mb-2">Description</h4>
              <p className="text-gray-300 text-sm">{selectedTemplate.description}</p>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-2">Vendor</h4>
              <p className="text-gray-300 text-sm">{selectedTemplate.vendor}</p>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-2">Tags ({selectedTemplate.tags.length})</h4>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {selectedTemplate.tags.map(tag => (
                  <div key={tag.id} className="bg-gray-700/50 rounded p-2">
                    <div className="flex items-center justify-between">
                      <span className="text-white text-sm font-medium">{tag.name}</span>
                      <div className="flex items-center space-x-1">
                        <span className="text-xs text-gray-400">{tag.type}</span>
                        {tag.safetyRated && (
                          <Shield className="w-3 h-3 text-red-400" />
                        )}
                      </div>
                    </div>
                    <div className="text-xs text-gray-400 mt-1">{tag.description}</div>
                    {tag.silLevel && (
                      <div className="text-xs text-red-400 mt-1">SIL Level: {tag.silLevel}</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">Preview</h4>
                <button
                  onClick={() => setShowPreview(!showPreview)}
                  className="text-blue-400 hover:text-blue-300 text-sm transition-colors"
                >
                  {showPreview ? 'Hide' : 'Show'} Code
                </button>
              </div>
              
              {showPreview && renderTemplatePreview()}
            </div>

            <div>
              <h4 className="text-white font-medium mb-2">Features</h4>
              <div className="space-y-1 text-sm text-gray-300">
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="w-4 h-4 text-green-400" />
                  <span>IEC 61131-3 Compliant</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="w-4 h-4 text-green-400" />
                  <span>Production Ready</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="w-4 h-4 text-green-400" />
                  <span>Well Documented</span>
                </div>
                {selectedTemplate.tags.some(t => t.safetyRated) && (
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-red-400" />
                    <span>Safety Certified</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <div className="p-4 border-t border-gray-700">
            <button
              onClick={() => handleUseTemplate(selectedTemplate)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors flex items-center justify-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Use Template</span>
            </button>
            
            <div className="mt-2 text-xs text-gray-400 text-center">
              Creates new program with template code and tags
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateLibrary;