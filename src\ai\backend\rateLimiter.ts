import Redis from 'ioredis';

export class RateLimiter {
  private redis: Redis;

  constructor() {
    this.redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');
  }

  async checkRateLimit(userId: string, plan: string) {
    const key = `rate_limit:${userId}`;
    const limits = this.getPlanLimits(plan);

    const current = await this.redis.incr(key);
    if (current === 1) {
      await this.redis.expire(key, limits.window);
    }

    return {
      allowed: current <= limits.max,
      remaining: Math.max(0, limits.max - current),
      resetTime: Date.now() + (limits.window * 1000)
    };
  }

  private getPlanLimits(plan: string) {
    const limits = {
      basic: { max: 100, window: 3600 },
      pro: { max: 500, window: 3600 },
      enterprise: { max: 1000, window: 3600 }
    };
    return limits[plan as keyof typeof limits] || limits.basic;
  }
}

export const rateLimiter = new RateLimiter();