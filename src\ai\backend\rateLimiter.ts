interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyGenerator: (userId: string, userPlan: string) => string;
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

class RateLimiter {
  private limits: Map<string, RateLimitEntry> = new Map();
  private configs: Map<string, RateLimitConfig> = new Map();

  constructor() {
    this.setupDefaultConfigs();
  }

  private setupDefaultConfigs() {
    // Free tier: 10 requests per 15 minutes
    this.configs.set('free', {
      windowMs: 15 * 60 * 1000,
      maxRequests: 10,
      keyGenerator: (userId, plan) => `${plan}:${userId}`
    });

    // Pro tier: 100 requests per 15 minutes
    this.configs.set('pro', {
      windowMs: 15 * 60 * 1000,
      maxRequests: 100,
      keyGenerator: (userId, plan) => `${plan}:${userId}`
    });

    // Enterprise tier: 1000 requests per 15 minutes
    this.configs.set('enterprise', {
      windowMs: 15 * 60 * 1000,
      maxRequests: 1000,
      keyGenerator: (userId, plan) => `${plan}:${userId}`
    });
  }

  async checkRateLimit(userId: string, userPlan: string = 'free'): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    const config = this.configs.get(userPlan) || this.configs.get('free')!;
    const key = config.keyGenerator(userId, userPlan);
    const now = Date.now();

    let entry = this.limits.get(key);

    // Reset if window has expired
    if (!entry || now > entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + config.windowMs
      };
    }

    // Check if limit exceeded
    if (entry.count >= config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime
      };
    }

    // Increment count and update
    entry.count++;
    this.limits.set(key, entry);

    return {
      allowed: true,
      remaining: config.maxRequests - entry.count,
      resetTime: entry.resetTime
    };
  }

  async getRemainingRequests(userId: string, userPlan: string = 'free'): Promise<number> {
    const result = await this.checkRateLimit(userId, userPlan);
    return result.remaining;
  }

  async resetUserLimit(userId: string, userPlan: string = 'free'): Promise<void> {
    const config = this.configs.get(userPlan) || this.configs.get('free')!;
    const key = config.keyGenerator(userId, userPlan);
    this.limits.delete(key);
  }

  // Cleanup expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.limits.entries()) {
      if (now > entry.resetTime) {
        this.limits.delete(key);
      }
    }
  }
}

export const rateLimiter = new RateLimiter();

// Cleanup expired entries every 5 minutes
setInterval(() => {
  rateLimiter.cleanup();
}, 5 * 60 * 1000);