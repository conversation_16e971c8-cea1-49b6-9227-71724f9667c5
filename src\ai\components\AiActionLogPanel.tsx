import React, { useState, useEffect } from 'react';
import { useAI } from '../hooks/useAI';
import { 
  FileText, 
  Download, 
  Filter, 
  Search, 
  Clock, 
  User,
  Trash2,
  Eye,
  RefreshCw,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Sparkles,
  Zap,
  Wrench,
  Bug,
  HelpCircle
} from 'lucide-react';
import AiConfidenceBadge from './AiConfidenceBadge';
import AiImpactBadge from './AiImpactBadge';
import AiPromptPreview from './AiPromptPreview';

interface AiActionLog {
  id: string;
  timestamp: Date;
  action: 'explain' | 'generate' | 'refactor' | 'suggest' | 'debug';
  prompt: string;
  response: string;
  confidence: number;
  timeTaken: number;
  accepted: boolean;
  userId: string;
  context: any;
  feedback?: {
    rating: 'thumbs_up' | 'thumbs_down' | null;
    comment?: string;
  };
}

const AiActionLogPanel: React.FC = () => {
  const { history, clearHistory, metrics } = useAI();
  const [logs, setLogs] = useState<AiActionLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<AiActionLog[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [filterFeedback, setFilterFeedback] = useState<string>('all');
  const [selectedLog, setSelectedLog] = useState<AiActionLog | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [feedbackComment, setFeedbackComment] = useState('');

  useEffect(() => {
    // Load logs from localStorage or API
    loadLogs();
  }, [history]);

  useEffect(() => {
    applyFilters();
  }, [logs, searchQuery, filterAction, filterFeedback]);

  const loadLogs = () => {
    // Convert history to logs format
    const historyLogs = history.map(item => ({
      id: item.id,
      timestamp: item.timestamp,
      action: item.id.includes('explain') ? 'explain' : 
              item.id.includes('generate') ? 'generate' :
              item.id.includes('refactor') ? 'refactor' :
              item.id.includes('suggest') ? 'suggest' : 'debug',
      prompt: item.prompt || 'No prompt available',
      response: item.content,
      confidence: item.confidence,
      timeTaken: item.timeTaken || 1.5,
      accepted: Boolean(item.accepted),
      userId: 'current-user',
      context: { language: 'ladder', type: 'plc_code' },
      feedback: {
        rating: null
      }
    }));

    // Add some mock data for demonstration
    const mockLogs: AiActionLog[] = [
      {
        id: 'log-1',
        timestamp: new Date(Date.now() - 1000 * 60 * 30),
        action: 'generate',
        prompt: 'Create motor control logic with start/stop buttons',
        response: 'Generated ladder logic with safety interlocks...',
        confidence: 0.85,
        timeTaken: 2.3,
        accepted: true,
        userId: 'user-1',
        context: { language: 'ladder', type: 'motor_control' },
        feedback: {
          rating: 'thumbs_up',
          comment: 'Excellent code, exactly what I needed!'
        }
      },
      {
        id: 'log-2',
        timestamp: new Date(Date.now() - 1000 * 60 * 60),
        action: 'explain',
        prompt: 'Explain this ladder logic rung',
        response: 'This rung implements a seal-in circuit...',
        confidence: 0.92,
        timeTaken: 1.8,
        accepted: true,
        userId: 'user-1',
        context: { language: 'ladder', elements: 3 },
        feedback: {
          rating: 'thumbs_up'
        }
      },
      {
        id: 'log-3',
        timestamp: new Date(Date.now() - 1000 * 60 * 90),
        action: 'refactor',
        prompt: 'Suggest improvements for this ST code',
        response: 'Consider using function blocks for reusability...',
        confidence: 0.78,
        timeTaken: 3.1,
        accepted: false,
        userId: 'user-1',
        context: { language: 'st', lines: 25 },
        feedback: {
          rating: 'thumbs_down',
          comment: 'Suggestions were not applicable to my use case'
        }
      }
    ];
    
    setLogs([...historyLogs, ...mockLogs]);
  };

  const applyFilters = () => {
    let filtered = logs;

    if (searchQuery) {
      filtered = filtered.filter(log =>
        log.prompt.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.response.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (filterAction !== 'all') {
      filtered = filtered.filter(log => log.action === filterAction);
    }

    if (filterFeedback !== 'all') {
      filtered = filtered.filter(log => 
        filterFeedback === 'positive' ? log.feedback?.rating === 'thumbs_up' :
        filterFeedback === 'negative' ? log.feedback?.rating === 'thumbs_down' :
        filterFeedback === 'none' ? !log.feedback?.rating : true
      );
    }

    setFilteredLogs(filtered);
  };

  const exportLogs = () => {
    const exportData = {
      exportDate: new Date().toISOString(),
      totalLogs: logs.length,
      logs: logs.map(log => ({
        ...log,
        timestamp: log.timestamp.toISOString()
      }))
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ai-action-log-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const clearLogs = () => {
    if (confirm('Are you sure you want to clear all AI action logs?')) {
      setLogs([]);
      setSelectedLog(null);
      clearHistory();
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'generate': return <Zap className="w-4 h-4" />;
      case 'explain': return <HelpCircle className="w-4 h-4" />;
      case 'refactor': return <Wrench className="w-4 h-4" />;
      case 'suggest': return <Sparkles className="w-4 h-4" />;
      case 'debug': return <Bug className="w-4 h-4" />;
      default: return <Sparkles className="w-4 h-4" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'generate': return 'text-blue-400 bg-blue-400/20 border-blue-400/30';
      case 'explain': return 'text-yellow-400 bg-yellow-400/20 border-yellow-400/30';
      case 'refactor': return 'text-green-400 bg-green-400/20 border-green-400/30';
      case 'suggest': return 'text-purple-400 bg-purple-400/20 border-purple-400/30';
      case 'debug': return 'text-red-400 bg-red-400/20 border-red-400/30';
      default: return 'text-gray-400 bg-gray-400/20 border-gray-400/30';
    }
  };

  const handleFeedback = (logId: string, rating: 'thumbs_up' | 'thumbs_down') => {
    setLogs(prev => prev.map(log => 
      log.id === logId 
        ? { 
            ...log, 
            feedback: { 
              ...log.feedback, 
              rating 
            } 
          } 
        : log
    ));
  };

  const submitFeedbackComment = () => {
    if (!selectedLog || !feedbackComment.trim()) return;
    
    setLogs(prev => prev.map(log => 
      log.id === selectedLog.id 
        ? { 
            ...log, 
            feedback: { 
              ...log.feedback, 
              comment: feedbackComment 
            } 
          } 
        : log
    ));
    
    setFeedbackComment('');
  };

  const renderLogEntry = (log: AiActionLog) => (
    <div
      key={log.id}
      className={`bg-gray-800/50 rounded-lg border border-gray-700 p-4 cursor-pointer transition-all hover:border-blue-500 ${
        selectedLog?.id === log.id ? 'border-blue-500 bg-blue-500/10' : ''
      }`}
      onClick={() => setSelectedLog(log)}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-3">
          <span className={getActionColor(log.action)}>
            {getActionIcon(log.action)}
          </span>
          <div>
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 rounded text-xs capitalize border ${getActionColor(log.action)}`}>
                {log.action}
              </span>
              <AiConfidenceBadge confidence={log.confidence} size="sm" showText={false} />
            </div>
            <div className="flex items-center space-x-2 text-xs text-gray-400 mt-1">
              <Clock className="w-3 h-3" />
              <span>{log.timestamp.toLocaleString()}</span>
              <span>•</span>
              <span>{log.timeTaken.toFixed(1)}s</span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {log.feedback?.rating === 'thumbs_up' && (
            <ThumbsUp className="w-4 h-4 text-green-400" />
          )}
          {log.feedback?.rating === 'thumbs_down' && (
            <ThumbsDown className="w-4 h-4 text-red-400" />
          )}
          {log.accepted ? (
            <span className="text-green-400 text-xs">✓ Applied</span>
          ) : (
            <span className="text-gray-400 text-xs">○ Not Applied</span>
          )}
        </div>
      </div>
      
      <div className="text-sm text-gray-300 mb-2 line-clamp-2">
        <strong>Prompt:</strong> {log.prompt}
      </div>
      
      <div className="text-sm text-gray-400 line-clamp-1">
        <strong>Response:</strong> {log.response.substring(0, 100)}...
      </div>
      
      {log.feedback?.comment && (
        <div className="mt-2 text-xs text-gray-400 bg-gray-700/30 p-2 rounded">
          <MessageSquare className="w-3 h-3 inline mr-1" />
          {log.feedback.comment}
        </div>
      )}
    </div>
  );

  const renderLogDetails = () => {
    if (!selectedLog) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400">
          <div className="text-center">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Select a log entry to view details</p>
          </div>
        </div>
      );
    }

    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-white">AI Action Details</h3>
          <div className="flex items-center space-x-2">
            <span className={`px-3 py-1 rounded capitalize border ${getActionColor(selectedLog.action)}`}>
              {selectedLog.action}
            </span>
            <AiConfidenceBadge confidence={selectedLog.confidence} size="sm" />
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-semibold mb-3">Request Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-400 mb-1">Timestamp</label>
              <span className="text-white">{selectedLog.timestamp.toLocaleString()}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Processing Time</label>
              <span className="text-white">{selectedLog.timeTaken.toFixed(1)} seconds</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">User</label>
              <span className="text-white">{selectedLog.userId}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Status</label>
              <span className={selectedLog.accepted ? 'text-green-400' : 'text-gray-400'}>
                {selectedLog.accepted ? '✓ Applied' : '○ Not Applied'}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-white font-semibold">Prompt</h4>
            <button
              onClick={() => setShowPrompt(!showPrompt)}
              className="text-blue-400 hover:text-blue-300 text-sm transition-colors"
            >
              {showPrompt ? 'Hide Details' : 'Show Details'}
            </button>
          </div>
          
          {showPrompt ? (
            <AiPromptPreview 
              prompt={selectedLog.prompt}
              templateUsed={`${selectedLog.action.charAt(0).toUpperCase() + selectedLog.action.slice(1)} Template`}
              className="border-0 mt-0"
            />
          ) : (
            <div className="bg-gray-900 rounded p-3 text-gray-300 text-sm">
              {selectedLog.prompt}
            </div>
          )}
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-semibold mb-3">AI Response</h4>
          <div className="bg-gray-900 rounded p-3 text-gray-300 text-sm whitespace-pre-wrap max-h-64 overflow-y-auto">
            {selectedLog.response}
          </div>
        </div>

        {/* Feedback Section */}
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-semibold mb-3">Feedback</h4>
          
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => handleFeedback(selectedLog.id, 'thumbs_up')}
              className={`flex items-center space-x-2 px-3 py-2 rounded transition-colors ${
                selectedLog.feedback?.rating === 'thumbs_up'
                  ? 'bg-green-600/20 text-green-400 border border-green-600/30'
                  : 'bg-gray-700 text-gray-400 hover:bg-green-600/10 hover:text-green-400'
              }`}
            >
              <ThumbsUp className="w-4 h-4" />
              <span>Helpful</span>
            </button>
            
            <button
              onClick={() => handleFeedback(selectedLog.id, 'thumbs_down')}
              className={`flex items-center space-x-2 px-3 py-2 rounded transition-colors ${
                selectedLog.feedback?.rating === 'thumbs_down'
                  ? 'bg-red-600/20 text-red-400 border border-red-600/30'
                  : 'bg-gray-700 text-gray-400 hover:bg-red-600/10 hover:text-red-400'
              }`}
            >
              <ThumbsDown className="w-4 h-4" />
              <span>Not Helpful</span>
            </button>
          </div>
          
          <div className="space-y-3">
            {selectedLog.feedback?.comment ? (
              <div className="bg-gray-700/50 rounded p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <MessageSquare className="w-4 h-4 text-blue-400" />
                  <span className="text-white text-sm">Your Comment</span>
                </div>
                <p className="text-gray-300 text-sm">{selectedLog.feedback.comment}</p>
              </div>
            ) : (
              <div>
                <textarea
                  value={feedbackComment}
                  onChange={(e) => setFeedbackComment(e.target.value)}
                  placeholder="Add your feedback comments here..."
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm resize-none h-20"
                />
                <div className="flex justify-end mt-2">
                  <button
                    onClick={submitFeedbackComment}
                    disabled={!feedbackComment.trim()}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-3 py-1 rounded text-sm transition-colors"
                  >
                    Submit Feedback
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full bg-gray-900 flex">
      {/* Log List */}
      <div className="w-1/2 border-r border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-white">AI Action Log</h2>
            <div className="flex items-center space-x-2">
              <button
                onClick={exportLogs}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button>
              <button
                onClick={clearLogs}
                className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                <span>Clear</span>
              </button>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search actions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <select
                value={filterAction}
                onChange={(e) => setFilterAction(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              >
                <option value="all">All Actions</option>
                <option value="generate">Generate</option>
                <option value="explain">Explain</option>
                <option value="refactor">Refactor</option>
                <option value="suggest">Suggest</option>
                <option value="debug">Debug</option>
              </select>
              
              <select
                value={filterFeedback}
                onChange={(e) => setFilterFeedback(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              >
                <option value="all">All Feedback</option>
                <option value="positive">Positive</option>
                <option value="negative">Negative</option>
                <option value="none">No Feedback</option>
              </select>
            </div>
          </div>
        </div>

        {/* Impact Metrics */}
        <div className="p-4 border-b border-gray-700">
          <AiImpactBadge metrics={metrics} showDetails={true} />
        </div>
        
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {filteredLogs.map(renderLogEntry)}
          
          {filteredLogs.length === 0 && (
            <div className="text-center py-12 text-gray-400">
              <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No action logs found</p>
              <p className="text-sm">Try adjusting your search or filter</p>
            </div>
          )}
        </div>
      </div>
      
      {/* Log Details */}
      <div className="w-1/2">
        {renderLogDetails()}
      </div>
    </div>
  );
};

export default AiActionLogPanel;