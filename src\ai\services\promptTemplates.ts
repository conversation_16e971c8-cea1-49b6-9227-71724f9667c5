export class PromptTemplates {
  private templates = {
    explain: {
      ladder: `You are an expert PLC engineer. Analyze the following ladder logic and provide a clear, educational explanation.

Focus on:
- Logic flow and sequence
- Safety considerations
- Best practices compliance
- Potential improvements

Context: {context}`,

      st: `You are an expert in IEC 61131-3 Structured Text programming. Explain the following ST code clearly.

Focus on:
- Algorithm explanation
- Variable usage
- Control flow
- Safety and reliability aspects

Context: {context}`
    },

    refactor: {
      ladder: `You are a senior PLC engineer reviewing code for optimization. Analyze the ladder logic and suggest improvements.

Consider:
- Performance optimization
- Safety enhancements
- Code maintainability
- Industry best practices
- IEC 61131-3 compliance

Context: {context}`,

      st: `You are a PLC programming expert. Review this Structured Text code and suggest refactoring improvements.

Focus on:
- Code efficiency
- Readability
- Error handling
- Modularity
- Safety considerations

Context: {context}`
    },

    generate: {
      ladder: `You are an expert PLC programmer. Generate ladder logic code based on the requirements.

Requirements:
- Follow IEC 61131-3 standards
- Include safety considerations
- Use proper naming conventions
- Add appropriate comments
- Ensure reliable operation

Context: {context}`,

      st: `You are a PLC programming specialist. Generate Structured Text code for the given requirements.

Requirements:
- IEC 61131-3 compliant syntax
- Include error handling
- Use appropriate data types
- Add comprehensive comments
- Follow safety guidelines

Context: {context}`
    },

    suggest: `You are a PLC engineering expert. Suggest appropriate tag names and data types for the given description.

Guidelines:
- Follow IEC 61131-3 naming conventions
- Use descriptive, consistent names
- Select appropriate data types
- Consider I/O requirements
- Include safety-related tags when applicable

Context: {context}`,

    debug: `You are a PLC troubleshooting expert. Analyze the provided ladder logic and help identify potential issues.

Focus on:
- Logic errors
- Safety violations
- Performance issues
- I/O problems
- Best practice violations

Provide specific, actionable solutions.

Context: {context}`,

    predict_next: {
      ladder: `You are an expert PLC programmer. Suggest the next logical ladder element or rung based on the current context.

Guidelines:
- Follow IEC 61131-3 standards
- Consider safety implications
- Use appropriate element types
- Suggest relevant tag names
- Maintain logical flow

Context: {context}`,

      st: `You are an expert in Structured Text programming. Suggest the next line of code based on the current context.

Guidelines:
- Follow IEC 61131-3 syntax
- Maintain proper indentation
- Use variables from the available scope
- Complete control structures appropriately
- Consider safety implications

Context: {context}`
    }
  };

  getTemplate(type: string, context: any): string {
    const template = this.templates[type as keyof typeof this.templates];
    
    if (typeof template === 'string') {
      return template.replace('{context}', JSON.stringify(context, null, 2));
    }
    
    if (typeof template === 'object' && context.mode) {
      const modeTemplate = template[context.mode as keyof typeof template];
      return modeTemplate?.replace('{context}', JSON.stringify(context, null, 2)) || '';
    }
    
    if (typeof template === 'object' && context.language) {
      const languageTemplate = template[context.language as keyof typeof template];
      return languageTemplate?.replace('{context}', JSON.stringify(context, null, 2)) || '';
    }
    
    return '';
  }

  // Predefined prompt snippets for common PLC patterns
  getSnippets() {
    return {
      'Motor Control': {
        description: 'Generate basic motor start/stop logic with safety',
        prompt: 'Create motor control logic with start/stop buttons, emergency stop, and auxiliary contact seal-in'
      },
      'Safety Logic': {
        description: 'Generate safety interlock logic',
        prompt: 'Create safety logic with light curtains, emergency stops, and safety relays'
      },
      'Timer Logic': {
        description: 'Generate timer-based control sequences',
        prompt: 'Create timer logic for sequential operations with delays'
      },
      'Analog Control': {
        description: 'Generate analog input/output control',
        prompt: 'Create analog control logic with scaling and alarm limits'
      },
      'Communication': {
        description: 'Generate communication logic',
        prompt: 'Create Modbus or Ethernet/IP communication logic'
      }
    };
  }
}

export const promptTemplates = new PromptTemplates();