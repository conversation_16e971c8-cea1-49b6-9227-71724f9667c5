import React, { useState, useEffect } from 'react';
import { User, UserRole, Permission } from '../../types/enterprise';
import { enterpriseServices } from '../../services/enterpriseServices';
import { 
  Users, 
  Shield, 
  Key, 
  Settings, 
  Plus, 
  Edit, 
  Trash2,
  Search,
  Filter,
  UserCheck,
  UserX,
  Clock,
  AlertTriangle,
  CheckCircle2,
  Mail,
  Phone,
  Calendar,
  Award
} from 'lucide-react';

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showCreateUser, setShowCreateUser] = useState(false);
  const [showEditUser, setShowEditUser] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRole, setFilterRole] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [newUser, setNewUser] = useState({
    username: '',
    email: '',
    role: 'viewer',
    permissions: [] as string[]
  });

  useEffect(() => {
    loadUsers();
    loadRoles();
  }, []);

  const loadUsers = async () => {
    // Enhanced mock users with more realistic data
    const mockUsers: User[] = [
      {
        id: 'user-1',
        username: 'john.engineer',
        email: '<EMAIL>',
        role: {
          id: 'role-engineer',
          name: 'engineer',
          level: 3,
          description: 'Control Systems Engineer',
          defaultPermissions: []
        },
        permissions: [
          { id: 'perm-1', resource: 'project', action: 'read', scope: 'global' },
          { id: 'perm-2', resource: 'project', action: 'write', scope: 'project' },
          { id: 'perm-3', resource: 'program', action: 'write', scope: 'project' },
          { id: 'perm-4', resource: 'deployment', action: 'deploy', scope: 'project' }
        ],
        lastActive: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        preferences: {
          theme: 'dark',
          language: 'en',
          keyboardShortcuts: {},
          autoSave: true,
          autoSaveInterval: 300,
          defaultPLCBrand: 'siemens',
          gridSnap: true,
          showMinimap: true
        },
        certifications: [
          {
            id: 'cert-1',
            name: 'Functional Safety Engineer',
            issuer: 'TÜV Rheinland',
            level: 'SIL3',
            expiryDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365), // 1 year
            verified: true
          },
          {
            id: 'cert-2',
            name: 'Siemens S7 Certified',
            issuer: 'Siemens',
            level: 'Expert',
            expiryDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 180), // 6 months
            verified: true
          }
        ],
        currentSession: {
          id: 'session-123',
          startTime: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          lastActivity: new Date(Date.now() - 1000 * 60 * 30),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          location: 'New York, USA'
        }
      },
      {
        id: 'user-2',
        username: 'sarah.admin',
        email: '<EMAIL>',
        role: {
          id: 'role-admin',
          name: 'admin',
          level: 5,
          description: 'System Administrator',
          defaultPermissions: []
        },
        permissions: [
          { id: 'perm-4', resource: 'system', action: 'read', scope: 'global' },
          { id: 'perm-5', resource: 'system', action: 'write', scope: 'global' },
          { id: 'perm-6', resource: 'project', action: 'delete', scope: 'global' },
          { id: 'perm-7', resource: 'user', action: 'write', scope: 'global' }
        ],
        lastActive: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
        preferences: {
          theme: 'light',
          language: 'en',
          keyboardShortcuts: {},
          autoSave: true,
          autoSaveInterval: 180,
          defaultPLCBrand: 'rockwell',
          gridSnap: false,
          showMinimap: false
        },
        currentSession: {
          id: 'session-124',
          startTime: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
          lastActivity: new Date(Date.now() - 1000 * 60 * 5),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (macOS; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          location: 'San Francisco, USA'
        }
      },
      {
        id: 'user-3',
        username: 'mike.safety',
        email: '<EMAIL>',
        role: {
          id: 'role-safety',
          name: 'safety_engineer',
          level: 4,
          description: 'Safety Systems Engineer',
          defaultPermissions: []
        },
        permissions: [
          { id: 'perm-8', resource: 'safety', action: 'read', scope: 'global' },
          { id: 'perm-9', resource: 'safety', action: 'write', scope: 'global' },
          { id: 'perm-10', resource: 'project', action: 'read', scope: 'global' }
        ],
        lastActive: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        preferences: {
          theme: 'dark',
          language: 'en',
          keyboardShortcuts: {},
          autoSave: true,
          autoSaveInterval: 120,
          defaultPLCBrand: 'pilz',
          gridSnap: true,
          showMinimap: true
        },
        certifications: [
          {
            id: 'cert-3',
            name: 'Certified Safety Engineer',
            issuer: 'Pilz',
            level: 'SIL3',
            expiryDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 90), // 3 months
            verified: true
          }
        ]
      }
    ];
    setUsers(mockUsers);
  };

  const loadRoles = async () => {
    const mockRoles: UserRole[] = [
      {
        id: 'role-admin',
        name: 'admin',
        level: 5,
        description: 'Full system access with user management',
        defaultPermissions: []
      },
      {
        id: 'role-engineer',
        name: 'engineer',
        level: 3,
        description: 'Program development and deployment',
        defaultPermissions: []
      },
      {
        id: 'role-safety',
        name: 'safety_engineer',
        level: 4,
        description: 'Safety systems development and validation',
        defaultPermissions: []
      },
      {
        id: 'role-operator',
        name: 'operator',
        level: 2,
        description: 'Monitor and control operations',
        defaultPermissions: []
      },
      {
        id: 'role-viewer',
        name: 'viewer',
        level: 1,
        description: 'Read-only access to projects',
        defaultPermissions: []
      }
    ];
    setRoles(mockRoles);
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = filterRole === 'all' || user.role.name === filterRole;
    const matchesStatus = filterStatus === 'all' || 
                         (filterStatus === 'online' && isUserOnline(user)) ||
                         (filterStatus === 'offline' && !isUserOnline(user));
    return matchesSearch && matchesRole && matchesStatus;
  });

  const isUserOnline = (user: User): boolean => {
    if (!user.lastActive) return false;
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return user.lastActive > fiveMinutesAgo;
  };

  const getRoleColor = (roleName: string) => {
    switch (roleName) {
      case 'admin': return 'text-red-400 bg-red-400/20 border-red-400/30';
      case 'engineer': return 'text-blue-400 bg-blue-400/20 border-blue-400/30';
      case 'safety_engineer': return 'text-orange-400 bg-orange-400/20 border-orange-400/30';
      case 'operator': return 'text-green-400 bg-green-400/20 border-green-400/30';
      case 'viewer': return 'text-gray-400 bg-gray-400/20 border-gray-400/30';
      default: return 'text-gray-400 bg-gray-400/20 border-gray-400/30';
    }
  };

  const getActivityStatus = (lastActive: Date) => {
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - lastActive.getTime()) / (1000 * 60));
    
    if (diffMinutes < 5) return { status: 'online', text: 'Online', color: 'text-green-400' };
    if (diffMinutes < 30) return { status: 'recent', text: `${diffMinutes}m ago`, color: 'text-yellow-400' };
    if (diffMinutes < 1440) return { status: 'today', text: `${Math.floor(diffMinutes / 60)}h ago`, color: 'text-gray-400' };
    return { status: 'offline', text: `${Math.floor(diffMinutes / 1440)}d ago`, color: 'text-gray-500' };
  };

  const handleCreateUser = () => {
    if (!newUser.username || !newUser.email) return;
    
    const user: User = {
      id: `user-${Date.now()}`,
      username: newUser.username,
      email: newUser.email,
      role: roles.find(r => r.name === newUser.role) || roles[0],
      permissions: [],
      lastActive: new Date(),
      preferences: {
        theme: 'dark',
        language: 'en',
        keyboardShortcuts: {},
        autoSave: true,
        autoSaveInterval: 300,
        defaultPLCBrand: 'siemens',
        gridSnap: true,
        showMinimap: true
      }
    };
    
    setUsers(prev => [...prev, user]);
    setNewUser({ username: '', email: '', role: 'viewer', permissions: [] });
    setShowCreateUser(false);
  };

  const handleDeleteUser = (userId: string) => {
    if (confirm('Are you sure you want to delete this user?')) {
      setUsers(prev => prev.filter(u => u.id !== userId));
      if (selectedUser?.id === userId) {
        setSelectedUser(null);
      }
    }
  };

  const renderUserCard = (user: User) => {
    const activity = getActivityStatus(user.lastActive);
    
    return (
      <div
        key={user.id}
        className={`bg-gray-800 rounded-lg border border-gray-700 p-4 cursor-pointer transition-all hover:border-blue-500 ${
          selectedUser?.id === user.id ? 'border-blue-500 bg-blue-500/10' : ''
        }`}
        onClick={() => setSelectedUser(user)}
      >
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center relative">
              <span className="text-white font-semibold text-sm">
                {user.username.charAt(0).toUpperCase()}
              </span>
              <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-gray-800 ${
                isUserOnline(user) ? 'bg-green-400' : 'bg-gray-500'
              }`}></div>
            </div>
            <div>
              <h3 className="text-white font-semibold">{user.username}</h3>
              <p className="text-gray-400 text-sm">{user.email}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded text-xs border ${getRoleColor(user.role.name)}`}>
              {user.role.name.replace('_', ' ')}
            </span>
          </div>
        </div>
        
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span className={activity.color}>
              <Clock className="w-3 h-3 inline mr-1" />
              {activity.text}
            </span>
            <span className="text-gray-400">
              <Key className="w-3 h-3 inline mr-1" />
              {user.permissions.length} permissions
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {user.certifications && user.certifications.length > 0 && (
              <div className="flex items-center space-x-1">
                <Award className="w-3 h-3 text-yellow-400" />
                <span className="text-yellow-400 text-xs">{user.certifications.length}</span>
              </div>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteUser(user.id);
              }}
              className="text-gray-400 hover:text-red-400 transition-colors"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderUserDetails = () => {
    if (!selectedUser) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400">
          <div className="text-center">
            <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Select a user to view details</p>
          </div>
        </div>
      );
    }

    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-white">User Details</h3>
          <div className="flex items-center space-x-2">
            <button 
              onClick={() => setShowEditUser(true)}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button 
              onClick={() => handleDeleteUser(selectedUser.id)}
              className="p-2 text-gray-400 hover:text-red-400 hover:bg-red-400/20 rounded transition-colors"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Basic Information */}
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-semibold mb-3">Basic Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-400 mb-1">Username</label>
              <div className="text-white">{selectedUser.username}</div>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Email</label>
              <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <span className="text-white">{selectedUser.email}</span>
              </div>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Role</label>
              <span className={`px-2 py-1 rounded text-sm border ${getRoleColor(selectedUser.role.name)}`}>
                {selectedUser.role.name.replace('_', ' ')} - {selectedUser.role.description}
              </span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Last Active</label>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-gray-400" />
                <span className="text-white">{selectedUser.lastActive.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Session Information */}
        {selectedUser.currentSession && (
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h4 className="text-white font-semibold mb-3">Current Session</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-400 mb-1">Session Duration</label>
                <div className="text-white">
                  {Math.floor((Date.now() - selectedUser.currentSession.startTime.getTime()) / (1000 * 60))} minutes
                </div>
              </div>
              <div>
                <label className="block text-sm text-gray-400 mb-1">IP Address</label>
                <div className="text-white font-mono">{selectedUser.currentSession.ipAddress}</div>
              </div>
              <div>
                <label className="block text-sm text-gray-400 mb-1">Location</label>
                <div className="text-white">{selectedUser.currentSession.location}</div>
              </div>
              <div>
                <label className="block text-sm text-gray-400 mb-1">Browser</label>
                <div className="text-white text-sm">
                  {selectedUser.currentSession.userAgent.includes('Chrome') ? 'Chrome' : 
                   selectedUser.currentSession.userAgent.includes('Firefox') ? 'Firefox' : 'Other'}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Permissions */}
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-semibold mb-3">Permissions ({selectedUser.permissions.length})</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {selectedUser.permissions.map(permission => (
              <div key={permission.id} className="flex items-center justify-between bg-gray-700/50 rounded p-2">
                <div className="flex items-center space-x-3">
                  <Key className="w-4 h-4 text-blue-400" />
                  <span className="text-white capitalize">{permission.action}</span>
                  <span className="text-gray-400">{permission.resource}</span>
                </div>
                <span className={`px-2 py-1 rounded text-xs ${
                  permission.scope === 'global' ? 'bg-red-400/20 text-red-400' :
                  permission.scope === 'project' ? 'bg-blue-400/20 text-blue-400' :
                  'bg-gray-400/20 text-gray-400'
                }`}>
                  {permission.scope}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Certifications */}
        {selectedUser.certifications && selectedUser.certifications.length > 0 && (
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h4 className="text-white font-semibold mb-3">Certifications ({selectedUser.certifications.length})</h4>
            <div className="space-y-3">
              {selectedUser.certifications.map(cert => {
                const isExpiringSoon = new Date(cert.expiryDate) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
                const isExpired = new Date(cert.expiryDate) < new Date();
                
                return (
                  <div key={cert.id} className="bg-gray-700/50 rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <Award className={`w-5 h-5 ${cert.verified ? 'text-yellow-400' : 'text-gray-400'}`} />
                        <div>
                          <div className="text-white font-medium">{cert.name}</div>
                          <div className="text-sm text-gray-400">{cert.issuer} • {cert.level}</div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className={`text-sm font-medium ${
                          isExpired ? 'text-red-400' : 
                          isExpiringSoon ? 'text-yellow-400' : 'text-green-400'
                        }`}>
                          {isExpired ? 'Expired' : isExpiringSoon ? 'Expiring Soon' : 'Valid'}
                        </div>
                        <div className="text-xs text-gray-400 flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>{cert.expiryDate.toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    
                    {cert.verified && (
                      <div className="flex items-center space-x-1 text-xs text-green-400">
                        <CheckCircle2 className="w-3 h-3" />
                        <span>Verified</span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* User Preferences */}
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-semibold mb-3">Preferences</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Theme:</span>
              <span className="text-white ml-2 capitalize">{selectedUser.preferences.theme}</span>
            </div>
            <div>
              <span className="text-gray-400">Language:</span>
              <span className="text-white ml-2">{selectedUser.preferences.language.toUpperCase()}</span>
            </div>
            <div>
              <span className="text-gray-400">Auto Save:</span>
              <span className="text-white ml-2">{selectedUser.preferences.autoSave ? 'Enabled' : 'Disabled'}</span>
            </div>
            <div>
              <span className="text-gray-400">Default PLC:</span>
              <span className="text-white ml-2 capitalize">{selectedUser.preferences.defaultPLCBrand}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderCreateUserModal = () => {
    if (!showCreateUser) return null;

    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Create New User</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm text-gray-400 mb-2">Username</label>
                <input
                  type="text"
                  value={newUser.username}
                  onChange={(e) => setNewUser(prev => ({ ...prev, username: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                  placeholder="Enter username"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-400 mb-2">Email</label>
                <input
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                  placeholder="Enter email address"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-400 mb-2">Role</label>
                <select
                  value={newUser.role}
                  onChange={(e) => setNewUser(prev => ({ ...prev, role: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                >
                  {roles.map(role => (
                    <option key={role.id} value={role.name}>
                      {role.name.replace('_', ' ')} - {role.description}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="flex items-center justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowCreateUser(false)}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateUser}
                disabled={!newUser.username || !newUser.email}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded transition-colors"
              >
                Create User
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="h-full bg-gray-900 flex">
        {/* User List */}
        <div className="w-1/2 border-r border-gray-700 flex flex-col">
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">User Management</h2>
              <button
                onClick={() => setShowCreateUser(true)}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>Add User</span>
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search users..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <select
                  value={filterRole}
                  onChange={(e) => setFilterRole(e.target.value)}
                  className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                >
                  <option value="all">All Roles</option>
                  {roles.map(role => (
                    <option key={role.id} value={role.name}>
                      {role.name.replace('_', ' ')}
                    </option>
                  ))}
                </select>
                
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                >
                  <option value="all">All Status</option>
                  <option value="online">Online</option>
                  <option value="offline">Offline</option>
                </select>
              </div>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-3">
            {filteredUsers.map(renderUserCard)}
            
            {filteredUsers.length === 0 && (
              <div className="text-center py-12 text-gray-400">
                <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No users found</p>
              </div>
            )}
          </div>
        </div>
        
        {/* User Details */}
        <div className="w-1/2">
          {renderUserDetails()}
        </div>
      </div>

      {/* Create User Modal */}
      {renderCreateUserModal()}
    </>
  );
};

export default UserManagement;