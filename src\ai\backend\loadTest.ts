import autocannon from 'autocannon';
import app from './server';

const performLoadTest = async () => {
    console.log('Starting load test...');

    const result = await autocannon({
        url: 'http://localhost:3001',
        connections: 10,
        pipelining: 1,
        duration: 30,
        requests: [
            {
                method: 'POST',
                path: '/api/ai/request',
                headers: {
                    'Authorization': 'Bearer test-token',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: 'generate',
                    prompt: 'Create simple ladder logic',
                    context: { language: 'ladder' }
                })
            }
        ]
    });

    console.log('Load test results:', result);

    // Performance assertions
    // expect(result.latency.mean).toBeLessThan(2000); // < 2s average response time
    // expect(result.errors).toBe(0); // No errors
    // expect(result.non2xx).toBe(0); // All successful responses
};

export { performLoadTest };