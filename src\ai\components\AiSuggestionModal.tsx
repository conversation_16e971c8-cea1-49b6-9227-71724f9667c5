import React, { useState } from 'react';
import { X, Check, AlertTriangle, Eye, Edit, RefreshCw } from 'lucide-react';
import AiConfidenceBadge from './AiConfidenceBadge';
import AiPromptPreview from './AiPromptPreview';

interface AiSuggestionModalProps {
  isOpen: boolean;
  onClose: () => void;
  suggestion: {
    id: string;
    content: string;
    confidence: number;
    prompt: string;
    type: 'code' | 'explanation' | 'refactor';
    preview?: string;
  };
  onApply: () => void;
  onEdit?: (newPrompt: string) => void;
  requireReview?: boolean;
}

const AiSuggestionModal: React.FC<AiSuggestionModalProps> = ({
  isOpen,
  onClose,
  suggestion,
  onApply,
  onEdit,
  requireReview = false
}) => {
  const [showDiff, setShowDiff] = useState(false);
  const [userReviewed, setUserReviewed] = useState(false);

  if (!isOpen) return null;

  const handleApply = () => {
    if (requireReview && !userReviewed) {
      setUserReviewed(true);
      return;
    }
    onApply();
    onClose();
  };

  const getTypeIcon = () => {
    switch (suggestion.type) {
      case 'code': return '⚡';
      case 'explanation': return '💡';
      case 'refactor': return '🔧';
      default: return '🤖';
    }
  };

  const getTypeLabel = () => {
    switch (suggestion.type) {
      case 'code': return 'Code Generation';
      case 'explanation': return 'Code Explanation';
      case 'refactor': return 'Code Refactoring';
      default: return 'AI Suggestion';
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{getTypeIcon()}</span>
            <div>
              <h2 className="text-xl font-semibold text-white">{getTypeLabel()}</h2>
              <div className="flex items-center space-x-2 mt-1">
                <AiConfidenceBadge confidence={suggestion.confidence} size="sm" />
                {requireReview && !userReviewed && (
                  <span className="text-xs bg-amber-600/20 text-amber-400 px-2 py-1 rounded border border-amber-600/30">
                    Review Required
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex">
          {/* Main Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">AI Suggestion</h3>
                <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <pre className="text-gray-300 whitespace-pre-wrap text-sm">
                    {suggestion.content}
                  </pre>
                </div>
              </div>

              {suggestion.preview && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-semibold text-white">Preview</h3>
                    <button
                      onClick={() => setShowDiff(!showDiff)}
                      className="flex items-center space-x-2 text-blue-400 hover:text-blue-300 text-sm transition-colors"
                    >
                      <Eye className="w-4 h-4" />
                      <span>{showDiff ? 'Hide Diff' : 'Show Diff'}</span>
                    </button>
                  </div>
                  
                  <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                    {showDiff ? (
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm text-red-400 mb-2">- Original</div>
                          <pre className="text-gray-300 whitespace-pre-wrap text-sm bg-red-900/20 p-2 rounded">
                            {suggestion.preview}
                          </pre>
                        </div>
                        <div>
                          <div className="text-sm text-green-400 mb-2">+ Suggested</div>
                          <pre className="text-gray-300 whitespace-pre-wrap text-sm bg-green-900/20 p-2 rounded">
                            {suggestion.content}
                          </pre>
                        </div>
                      </div>
                    ) : (
                      <pre className="text-gray-300 whitespace-pre-wrap text-sm">
                        {suggestion.preview}
                      </pre>
                    )}
                  </div>
                </div>
              )}

              {/* Safety Warning for Low Confidence */}
              {suggestion.confidence < 0.6 && (
                <div className="bg-amber-600/20 border border-amber-600/30 rounded-lg p-4">
                  <div className="flex items-center space-x-2 text-amber-400 mb-2">
                    <AlertTriangle className="w-5 h-5" />
                    <span className="font-semibold">Low Confidence Warning</span>
                  </div>
                  <p className="text-amber-300 text-sm">
                    This AI suggestion has low confidence ({Math.round(suggestion.confidence * 100)}%). 
                    Please review carefully before applying, especially for safety-critical applications.
                  </p>
                </div>
              )}

              {/* Review Checklist for Safety */}
              {requireReview && suggestion.type === 'code' && (
                <div className="bg-blue-600/20 border border-blue-600/30 rounded-lg p-4">
                  <h4 className="text-blue-400 font-semibold mb-3">Safety Review Checklist</h4>
                  <div className="space-y-2 text-sm">
                    <label className="flex items-center space-x-2 text-blue-300">
                      <input type="checkbox" className="rounded" />
                      <span>Emergency stop logic is properly implemented</span>
                    </label>
                    <label className="flex items-center space-x-2 text-blue-300">
                      <input type="checkbox" className="rounded" />
                      <span>Safety interlocks are correctly configured</span>
                    </label>
                    <label className="flex items-center space-x-2 text-blue-300">
                      <input type="checkbox" className="rounded" />
                      <span>Code follows IEC 61131-3 standards</span>
                    </label>
                    <label className="flex items-center space-x-2 text-blue-300">
                      <input type="checkbox" className="rounded" />
                      <span>No safety-critical functions are compromised</span>
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="w-80 border-l border-gray-700 bg-gray-800/50">
            <AiPromptPreview
              prompt={suggestion.prompt}
              templateUsed="PLC Code Generation"
              onEdit={onEdit}
              onRerun={(prompt) => console.log('Re-running with prompt:', prompt)}
              className="border-0"
            />
            
            <div className="p-4 space-y-4">
              <div>
                <h4 className="text-white font-semibold mb-2">Suggestion Details</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Type:</span>
                    <span className="text-white capitalize">{suggestion.type}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Confidence:</span>
                    <span className="text-white">{Math.round(suggestion.confidence * 100)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Length:</span>
                    <span className="text-white">{suggestion.content.length} chars</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-white font-semibold mb-2">Quality Indicators</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Safety Compliance</span>
                    <span className="text-green-400">✓ Pass</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Code Standards</span>
                    <span className="text-green-400">✓ IEC 61131-3</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">Best Practices</span>
                    <span className="text-yellow-400">⚠ Review</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-700 bg-gray-800/50">
          <div className="flex items-center space-x-4">
            {requireReview && !userReviewed ? (
              <button
                onClick={() => setUserReviewed(true)}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
              >
                <Check className="w-4 h-4" />
                <span>Mark as Reviewed</span>
              </button>
            ) : (
              <button
                onClick={handleApply}
                className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors"
              >
                <Check className="w-4 h-4" />
                <span>Apply Suggestion</span>
              </button>
            )}
            
            <button
              onClick={onClose}
              className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors"
            >
              <X className="w-4 h-4" />
              <span>Cancel</span>
            </button>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            {onEdit && (
              <button
                onClick={() => onEdit && onEdit(suggestion.prompt)}
                className="flex items-center space-x-1 text-blue-400 hover:text-blue-300 transition-colors"
              >
                <Edit className="w-3 h-3" />
                <span>Edit Prompt</span>
              </button>
            )}
            <span>•</span>
            <span>ID: {suggestion.id.slice(-8)}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AiSuggestionModal;