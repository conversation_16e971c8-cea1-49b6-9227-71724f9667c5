import React, { useState, useEffect } from 'react';
import { 
  BookOpen, 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  Play, 
  Eye, 
  Download, 
  Upload, 
  FileText, 
  Video, 
  Link, 
  User, 
  Clock, 
  X, 
  RefreshCw,
  CheckCircle2,
  AlertTriangle,
  ExternalLink,
  Image,
  Save
} from 'lucide-react';

interface TutorialData {
  id: string;
  title: string;
  description: string;
  type: 'video' | 'article' | 'interactive';
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in minutes
  url?: string;
  thumbnailUrl?: string;
  author: string;
  createdAt: Date;
  lastModified?: Date;
  published: boolean;
  views: number;
  likes: number;
  tags: string[];
}

const TutorialManager: React.FC = () => {
  const [tutorials, setTutorials] = useState<TutorialData[]>([]);
  const [filteredTutorials, setFilteredTutorials] = useState<TutorialData[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [selectedTutorial, setSelectedTutorial] = useState<TutorialData | null>(null);
  const [showAddTutorialModal, setShowAddTutorialModal] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editedTutorial, setEditedTutorial] = useState<TutorialData | null>(null);
  const [newTutorial, setNewTutorial] = useState({
    title: '',
    description: '',
    type: 'video' as const,
    category: 'Getting Started',
    difficulty: 'beginner' as const,
    duration: 10,
    url: '',
    thumbnailUrl: '',
    tags: '',
    published: true
  });

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const mockTutorials: TutorialData[] = [
        {
          id: '1',
          title: 'Getting Started with LUREON IDE',
          description: 'Learn the basics of the LUREON IDE and create your first project.',
          type: 'video',
          category: 'Getting Started',
          difficulty: 'beginner',
          duration: 15,
          url: 'https://example.com/tutorials/getting-started',
          thumbnailUrl: 'https://via.placeholder.com/300x200?text=Getting+Started',
          author: 'Sarah Admin',
          createdAt: new Date(2023, 5, 15),
          lastModified: new Date(2023, 8, 22),
          published: true,
          views: 1245,
          likes: 87,
          tags: ['beginner', 'introduction', 'setup']
        },
        {
          id: '2',
          title: 'Ladder Logic Programming Fundamentals',
          description: 'Master the basics of ladder logic programming with practical examples.',
          type: 'video',
          category: 'Programming',
          difficulty: 'beginner',
          duration: 25,
          url: 'https://example.com/tutorials/ladder-logic-basics',
          thumbnailUrl: 'https://via.placeholder.com/300x200?text=Ladder+Logic',
          author: 'John Engineer',
          createdAt: new Date(2023, 3, 10),
          lastModified: new Date(2023, 7, 5),
          published: true,
          views: 3421,
          likes: 156,
          tags: ['ladder', 'programming', 'basics']
        },
        {
          id: '3',
          title: 'Advanced Safety Programming',
          description: 'Learn how to implement safety-critical applications with LUREON.',
          type: 'article',
          category: 'Safety',
          difficulty: 'advanced',
          duration: 30,
          url: 'https://example.com/tutorials/safety-programming',
          thumbnailUrl: 'https://via.placeholder.com/300x200?text=Safety+Programming',
          author: 'Mike Safety',
          createdAt: new Date(2023, 2, 18),
          lastModified: new Date(2023, 6, 30),
          published: true,
          views: 1876,
          likes: 92,
          tags: ['safety', 'advanced', 'certification']
        },
        {
          id: '4',
          title: 'Using the AI Assistant',
          description: 'Boost your productivity with AI-powered code generation and assistance.',
          type: 'video',
          category: 'AI Features',
          difficulty: 'intermediate',
          duration: 20,
          url: 'https://example.com/tutorials/ai-assistant',
          thumbnailUrl: 'https://via.placeholder.com/300x200?text=AI+Assistant',
          author: 'Sarah Admin',
          createdAt: new Date(2023, 4, 5),
          lastModified: new Date(2023, 9, 12),
          published: true,
          views: 2543,
          likes: 134,
          tags: ['ai', 'productivity', 'code-generation']
        },
        {
          id: '5',
          title: 'Structured Text Programming',
          description: 'Learn how to use Structured Text for complex control applications.',
          type: 'interactive',
          category: 'Programming',
          difficulty: 'intermediate',
          duration: 45,
          url: 'https://example.com/tutorials/structured-text',
          thumbnailUrl: 'https://via.placeholder.com/300x200?text=Structured+Text',
          author: 'John Engineer',
          createdAt: new Date(2023, 7, 20),
          lastModified: new Date(2023, 10, 15),
          published: true,
          views: 1987,
          likes: 76,
          tags: ['structured-text', 'programming', 'intermediate']
        },
        {
          id: '6',
          title: 'Real-time Collaboration',
          description: 'Learn how to collaborate with your team in real-time on PLC projects.',
          type: 'video',
          category: 'Collaboration',
          difficulty: 'beginner',
          duration: 18,
          url: 'https://example.com/tutorials/collaboration',
          thumbnailUrl: 'https://via.placeholder.com/300x200?text=Collaboration',
          author: 'David Manager',
          createdAt: new Date(2023, 6, 8),
          lastModified: new Date(2023, 8, 14),
          published: true,
          views: 1432,
          likes: 65,
          tags: ['collaboration', 'teamwork', 'real-time']
        },
        {
          id: '7',
          title: 'Deploying to PLC Hardware',
          description: 'Step-by-step guide to deploying your projects to physical PLC hardware.',
          type: 'article',
          category: 'Deployment',
          difficulty: 'intermediate',
          duration: 25,
          url: 'https://example.com/tutorials/deployment',
          thumbnailUrl: 'https://via.placeholder.com/300x200?text=Deployment',
          author: 'Lisa Operator',
          createdAt: new Date(2023, 9, 3),
          lastModified: new Date(2023, 11, 7),
          published: false,
          views: 0,
          likes: 0,
          tags: ['deployment', 'hardware', 'configuration']
        }
      ];
      
      setTutorials(mockTutorials);
      setFilteredTutorials(mockTutorials);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter tutorials based on search and filters
  useEffect(() => {
    let result = tutorials;
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(tutorial => 
        tutorial.title.toLowerCase().includes(query) || 
        tutorial.description.toLowerCase().includes(query) ||
        tutorial.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    if (filterCategory !== 'all') {
      result = result.filter(tutorial => tutorial.category === filterCategory);
    }
    
    if (filterType !== 'all') {
      result = result.filter(tutorial => tutorial.type === filterType);
    }
    
    setFilteredTutorials(result);
  }, [tutorials, searchQuery, filterCategory, filterType]);

  const handleAddTutorial = () => {
    if (!newTutorial.title || !newTutorial.description || !newTutorial.url) return;
    
    const tutorial: TutorialData = {
      id: (tutorials.length + 1).toString(),
      title: newTutorial.title,
      description: newTutorial.description,
      type: newTutorial.type,
      category: newTutorial.category,
      difficulty: newTutorial.difficulty,
      duration: newTutorial.duration,
      url: newTutorial.url,
      thumbnailUrl: newTutorial.thumbnailUrl || `https://via.placeholder.com/300x200?text=${encodeURIComponent(newTutorial.title)}`,
      author: 'Sarah Admin',
      createdAt: new Date(),
      published: newTutorial.published,
      views: 0,
      likes: 0,
      tags: newTutorial.tags.split(',').map(tag => tag.trim())
    };
    
    setTutorials([...tutorials, tutorial]);
    setShowAddTutorialModal(false);
    setNewTutorial({
      title: '',
      description: '',
      type: 'video',
      category: 'Getting Started',
      difficulty: 'beginner',
      duration: 10,
      url: '',
      thumbnailUrl: '',
      tags: '',
      published: true
    });
  };

  const handleSaveEdit = () => {
    if (!editedTutorial) return;
    
    setTutorials(tutorials.map(tutorial => 
      tutorial.id === editedTutorial.id ? editedTutorial : tutorial
    ));
    
    setSelectedTutorial(editedTutorial);
    setIsEditing(false);
    setEditedTutorial(null);
  };

  const handleDeleteTutorial = () => {
    if (!selectedTutorial) return;
    
    setTutorials(tutorials.filter(tutorial => tutorial.id !== selectedTutorial.id));
    setSelectedTutorial(null);
    setShowDeleteConfirmation(false);
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'video':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'article':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'interactive':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Video className="w-4 h-4" />;
      case 'article':
        return <FileText className="w-4 h-4" />;
      case 'interactive':
        return <Play className="w-4 h-4" />;
      default:
        return <BookOpen className="w-4 h-4" />;
    }
  };

  const getDifficultyBadgeColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'intermediate':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'advanced':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with search and filters */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Tutorials & Guides</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => {
                // Import tutorial functionality
              }}
              className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Upload className="w-4 h-4" />
              <span>Import</span>
            </button>
            <button
              onClick={() => setShowAddTutorialModal(true)}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add Tutorial</span>
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div className="col-span-3 sm:col-span-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search tutorials..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              <option value="Getting Started">Getting Started</option>
              <option value="Programming">Programming</option>
              <option value="Safety">Safety</option>
              <option value="AI Features">AI Features</option>
              <option value="Collaboration">Collaboration</option>
              <option value="Deployment">Deployment</option>
            </select>
          </div>
          
          <div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="video">Video</option>
              <option value="article">Article</option>
              <option value="interactive">Interactive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Tutorial Grid */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center space-x-2">
              <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />
              <span className="text-white">Loading tutorials...</span>
            </div>
          </div>
        ) : filteredTutorials.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 bg-gray-800 rounded-lg border border-gray-700">
            <BookOpen className="w-16 h-16 text-gray-600 mb-4" />
            <h3 className="text-lg font-medium text-white">No tutorials found</h3>
            <p className="text-gray-400 mt-2">Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTutorials.map(tutorial => (
              <div 
                key={tutorial.id} 
                className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden hover:border-blue-500 transition-colors cursor-pointer"
                onClick={() => setSelectedTutorial(tutorial)}
              >
                <div className="relative h-40 bg-gray-700">
                  {tutorial.thumbnailUrl ? (
                    <img 
                      src={tutorial.thumbnailUrl} 
                      alt={tutorial.title} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-700">
                      <BookOpen className="w-12 h-12 text-gray-500" />
                    </div>
                  )}
                  <div className="absolute top-2 right-2 flex space-x-2">
                    <span className={`px-2 py-1 rounded text-xs ${getTypeBadgeColor(tutorial.type)}`}>
                      {tutorial.type}
                    </span>
                    {!tutorial.published && (
                      <span className="px-2 py-1 rounded text-xs bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
                        Draft
                      </span>
                    )}
                  </div>
                  <div className="absolute bottom-2 right-2">
                    <span className="px-2 py-1 rounded text-xs bg-black/50 text-white">
                      {formatDuration(tutorial.duration)}
                    </span>
                  </div>
                </div>
                
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className={`px-2 py-1 rounded text-xs ${getDifficultyBadgeColor(tutorial.difficulty)}`}>
                      {tutorial.difficulty}
                    </span>
                    <span className="text-xs text-gray-400">{tutorial.category}</span>
                  </div>
                  <h3 className="text-white font-medium mb-2 line-clamp-2">{tutorial.title}</h3>
                  <p className="text-gray-400 text-sm mb-3 line-clamp-2">{tutorial.description}</p>
                  
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <span>By {tutorial.author}</span>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <Eye className="w-3 h-3" />
                        <span>{tutorial.views.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"></path>
                        </svg>
                        <span>{tutorial.likes.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Tutorial Details Sidebar - Show when a tutorial is selected */}
      {selectedTutorial && (
        <div className="fixed inset-y-0 right-0 w-96 bg-gray-800 border-l border-gray-700 p-6 overflow-y-auto shadow-xl z-10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">Tutorial Details</h3>
            <button
              onClick={() => {
                setSelectedTutorial(null);
                setIsEditing(false);
                setEditedTutorial(null);
              }}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          {isEditing && editedTutorial ? (
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Title</label>
                <input
                  type="text"
                  value={editedTutorial.title}
                  onChange={(e) => setEditedTutorial({...editedTutorial, title: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Description</label>
                <textarea
                  value={editedTutorial.description}
                  onChange={(e) => setEditedTutorial({...editedTutorial, description: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none"
                ></textarea>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Type</label>
                <select
                  value={editedTutorial.type}
                  onChange={(e) => setEditedTutorial({...editedTutorial, type: e.target.value as any})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="video">Video</option>
                  <option value="article">Article</option>
                  <option value="interactive">Interactive</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Category</label>
                <select
                  value={editedTutorial.category}
                  onChange={(e) => setEditedTutorial({...editedTutorial, category: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Getting Started">Getting Started</option>
                  <option value="Programming">Programming</option>
                  <option value="Safety">Safety</option>
                  <option value="AI Features">AI Features</option>
                  <option value="Collaboration">Collaboration</option>
                  <option value="Deployment">Deployment</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Difficulty</label>
                <select
                  value={editedTutorial.difficulty}
                  onChange={(e) => setEditedTutorial({...editedTutorial, difficulty: e.target.value as any})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Duration (minutes)</label>
                <input
                  type="number"
                  min="1"
                  value={editedTutorial.duration}
                  onChange={(e) => setEditedTutorial({...editedTutorial, duration: parseInt(e.target.value)})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">URL</label>
                <input
                  type="text"
                  value={editedTutorial.url || ''}
                  onChange={(e) => setEditedTutorial({...editedTutorial, url: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Thumbnail URL</label>
                <input
                  type="text"
                  value={editedTutorial.thumbnailUrl || ''}
                  onChange={(e) => setEditedTutorial({...editedTutorial, thumbnailUrl: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Tags (comma separated)</label>
                <input
                  type="text"
                  value={editedTutorial.tags.join(', ')}
                  onChange={(e) => setEditedTutorial({
                    ...editedTutorial, 
                    tags: e.target.value.split(',').map(tag => tag.trim())
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPublished"
                  checked={editedTutorial.published}
                  onChange={(e) => setEditedTutorial({...editedTutorial, published: e.target.checked})}
                  className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                />
                <label htmlFor="isPublished" className="text-gray-300">Published</label>
              </div>
              
              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setEditedTutorial(null);
                  }}
                  className="flex-1 px-4 py-2 text-gray-300 hover:text-white"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveEdit}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Save Changes
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="relative h-48 bg-gray-700 rounded-lg overflow-hidden">
                {selectedTutorial.thumbnailUrl ? (
                  <img 
                    src={selectedTutorial.thumbnailUrl} 
                    alt={selectedTutorial.title} 
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gray-700">
                    <BookOpen className="w-16 h-16 text-gray-500" />
                  </div>
                )}
                <div className="absolute top-2 right-2 flex space-x-2">
                  <span className={`px-2 py-1 rounded text-xs ${getTypeBadgeColor(selectedTutorial.type)}`}>
                    {selectedTutorial.type}
                  </span>
                  {!selectedTutorial.published && (
                    <span className="px-2 py-1 rounded text-xs bg-yellow-500/20 text-yellow-400 border border-yellow-500/30">
                      Draft
                    </span>
                  )}
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className={`px-2 py-1 rounded text-xs ${getDifficultyBadgeColor(selectedTutorial.difficulty)}`}>
                    {selectedTutorial.difficulty}
                  </span>
                  <span className="text-xs text-gray-400">{selectedTutorial.category}</span>
                </div>
                <h4 className="text-xl font-semibold text-white mb-2">{selectedTutorial.title}</h4>
                <p className="text-gray-300 text-sm">{selectedTutorial.description}</p>
              </div>
              
              <div className="bg-gray-700 rounded-lg p-4">
                <h5 className="text-white font-medium mb-3">Tutorial Information</h5>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Duration</span>
                    <span className="text-white">{formatDuration(selectedTutorial.duration)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Author</span>
                    <span className="text-white">{selectedTutorial.author}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Created</span>
                    <span className="text-white">{selectedTutorial.createdAt.toLocaleDateString()}</span>
                  </div>
                  {selectedTutorial.lastModified && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Last Modified</span>
                      <span className="text-white">{selectedTutorial.lastModified.toLocaleDateString()}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-400">Views</span>
                    <span className="text-white">{selectedTutorial.views.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Likes</span>
                    <span className="text-white">{selectedTutorial.likes.toLocaleString()}</span>
                  </div>
                </div>
              </div>
              
              {selectedTutorial.url && (
                <div className="bg-gray-700 rounded-lg p-4">
                  <h5 className="text-white font-medium mb-3">Resource Link</h5>
                  <div className="flex items-center justify-between">
                    <a 
                      href={selectedTutorial.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-400 hover:text-blue-300 flex items-center space-x-2"
                    >
                      <Link className="w-4 h-4" />
                      <span className="truncate">{selectedTutorial.url}</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </div>
                </div>
              )}
              
              <div className="bg-gray-700 rounded-lg p-4">
                <h5 className="text-white font-medium mb-3">Tags</h5>
                <div className="flex flex-wrap gap-2">
                  {selectedTutorial.tags.map((tag, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-600 text-gray-300 rounded text-xs">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setIsEditing(true);
                    setEditedTutorial({...selectedTutorial});
                  }}
                  className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <Edit className="w-4 h-4" />
                  <span>Edit</span>
                </button>
                <button
                  onClick={() => {
                    window.open(selectedTutorial.url, '_blank');
                  }}
                  className="flex-1 flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                  disabled={!selectedTutorial.url}
                >
                  <Eye className="w-4 h-4" />
                  <span>View</span>
                </button>
              </div>
              
              <button
                onClick={() => setShowDeleteConfirmation(true)}
                className="w-full flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete Tutorial</span>
              </button>
            </div>
          )}
        </div>
      )}

      {/* Add Tutorial Modal */}
      {showAddTutorialModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Add New Tutorial</h3>
              <button
                onClick={() => setShowAddTutorialModal(false)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Title</label>
                <input
                  type="text"
                  value={newTutorial.title}
                  onChange={(e) => setNewTutorial({...newTutorial, title: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter tutorial title"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Description</label>
                <textarea
                  value={newTutorial.description}
                  onChange={(e) => setNewTutorial({...newTutorial, description: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none"
                  placeholder="Enter tutorial description"
                ></textarea>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Type</label>
                <select
                  value={newTutorial.type}
                  onChange={(e) => setNewTutorial({...newTutorial, type: e.target.value as any})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="video">Video</option>
                  <option value="article">Article</option>
                  <option value="interactive">Interactive</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Category</label>
                <select
                  value={newTutorial.category}
                  onChange={(e) => setNewTutorial({...newTutorial, category: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Getting Started">Getting Started</option>
                  <option value="Programming">Programming</option>
                  <option value="Safety">Safety</option>
                  <option value="AI Features">AI Features</option>
                  <option value="Collaboration">Collaboration</option>
                  <option value="Deployment">Deployment</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Difficulty</label>
                <select
                  value={newTutorial.difficulty}
                  onChange={(e) => setNewTutorial({...newTutorial, difficulty: e.target.value as any})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Duration (minutes)</label>
                <input
                  type="number"
                  min="1"
                  value={newTutorial.duration}
                  onChange={(e) => setNewTutorial({...newTutorial, duration: parseInt(e.target.value)})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">URL</label>
                <input
                  type="text"
                  value={newTutorial.url}
                  onChange={(e) => setNewTutorial({...newTutorial, url: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter resource URL"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Thumbnail URL (Optional)</label>
                <input
                  type="text"
                  value={newTutorial.thumbnailUrl}
                  onChange={(e) => setNewTutorial({...newTutorial, thumbnailUrl: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter thumbnail URL"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Tags (comma separated)</label>
                <input
                  type="text"
                  value={newTutorial.tags}
                  onChange={(e) => setNewTutorial({...newTutorial, tags: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="beginner, ladder, safety"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPublished"
                  checked={newTutorial.published}
                  onChange={(e) => setNewTutorial({...newTutorial, published: e.target.checked})}
                  className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                />
                <label htmlFor="isPublished" className="text-gray-300">Publish immediately</label>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddTutorialModal(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleAddTutorial}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                disabled={!newTutorial.title || !newTutorial.description || !newTutorial.url}
              >
                Add Tutorial
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && selectedTutorial && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center space-x-3 text-red-400 mb-4">
              <AlertTriangle className="w-6 h-6" />
              <h3 className="text-xl font-semibold">Confirm Deletion</h3>
            </div>
            
            <p className="text-white mb-2">Are you sure you want to delete this tutorial?</p>
            <p className="text-gray-400 mb-6">This action cannot be undone. All users will lose access to this tutorial.</p>
            
            <div className="bg-gray-700/50 rounded-lg p-4 mb-6">
              <h4 className="text-white font-medium mb-2">{selectedTutorial.title}</h4>
              <p className="text-gray-400 text-sm">{selectedTutorial.description}</p>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirmation(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteTutorial}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Delete Tutorial
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TutorialManager;