# Production Environment Variables

# Server Configuration
NODE_ENV=production
PORT=3001
FRONTEND_URL=https://your-domain.com

# Database Configuration
DATABASE_URL=******************************************************/lureon_ai
DB_USER=lureon_user
DB_PASSWORD=secure_password

# Redis Configuration
REDIS_URL=redis://redis:6379

# AI Provider API Keys
OPENAI_API_KEY=your_production_openai_key
CLAUDE_API_KEY=your_production_claude_key

# Security
JWT_SECRET=your_super_secure_jwt_secret_at_least_32_characters_long
ENCRYPTION_KEY=your_32_character_encryption_key

# Monitoring & Logging
LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn
APP_VERSION=1.0.0

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000