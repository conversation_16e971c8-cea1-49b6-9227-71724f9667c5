export interface AIAuditLog {
  id: string;
  user_id: string;
  session_id: string;
  prompt_hash: string;
  response_hash: string;
  model_used: string;
  confidence_score: number;
  timestamp: Date;
  ip_address: string;
  request_type: string;
  safety_validated: boolean;
  compliance_issues: string[];
}

export interface ComplianceReport {
  period: { start: Date; end: Date };
  totalRequests: number;
  safetyViolations: number;
  averageConfidence: number;
  userActivity: Record<string, number>;
  modelUsage: Record<string, number>;
  complianceScore: number;
}

class AuditLogger {
  private logs: AIAuditLog[] = [];

  async logAIInteraction(interaction: Omit<AIAuditLog, 'id' | 'timestamp'>): Promise<void> {
    const log: AIAuditLog = {
      ...interaction,
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    this.logs.push(log);
    
    // In production, persist to secure database
    await this.persistAuditLog(log);
    
    // Check for compliance violations
    await this.checkComplianceViolations(log);
  }

  async generateComplianceReport(startDate: Date, endDate: Date): Promise<ComplianceReport> {
    const relevantLogs = this.logs.filter(log => 
      log.timestamp >= startDate && log.timestamp <= endDate
    );

    const safetyViolations = relevantLogs.filter(log => !log.safety_validated).length;
    const averageConfidence = relevantLogs.reduce((sum, log) => sum + log.confidence_score, 0) / relevantLogs.length;

    const userActivity = relevantLogs.reduce((acc, log) => {
      acc[log.user_id] = (acc[log.user_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const modelUsage = relevantLogs.reduce((acc, log) => {
      acc[log.model_used] = (acc[log.model_used] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const complianceScore = Math.max(0, 100 - (safetyViolations / relevantLogs.length * 100));

    return {
      period: { start: startDate, end: endDate },
      totalRequests: relevantLogs.length,
      safetyViolations,
      averageConfidence: averageConfidence || 0,
      userActivity,
      modelUsage,
      complianceScore
    };
  }

  async exportAuditTrail(userId?: string): Promise<string> {
    const logsToExport = userId 
      ? this.logs.filter(log => log.user_id === userId)
      : this.logs;

    const exportData = {
      exportDate: new Date().toISOString(),
      totalLogs: logsToExport.length,
      logs: logsToExport.map(log => ({
        ...log,
        timestamp: log.timestamp.toISOString()
      }))
    };

    return JSON.stringify(exportData, null, 2);
  }

  private async persistAuditLog(log: AIAuditLog): Promise<void> {
    // In production, this would write to secure, tamper-proof storage
    try {
      // Store in IndexedDB for local persistence
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['audit_logs'], 'readwrite');
      const store = transaction.objectStore('audit_logs');
      await store.add(log);
    } catch (error) {
      console.error('Failed to persist audit log:', error);
    }
  }

  private async checkComplianceViolations(log: AIAuditLog): Promise<void> {
    // Check for compliance violations and alert if necessary
    if (!log.safety_validated) {
      console.warn('Safety validation failed for AI request:', log.id);
      // In production, trigger compliance alert
    }

    if (log.confidence_score < 0.5) {
      console.warn('Low confidence AI response:', log.id);
      // In production, flag for review
    }
  }

  private async openIndexedDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PLCAuditDB', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        if (!db.objectStoreNames.contains('audit_logs')) {
          const store = db.createObjectStore('audit_logs', { keyPath: 'id' });
          store.createIndex('timestamp', 'timestamp');
          store.createIndex('user_id', 'user_id');
          store.createIndex('model_used', 'model_used');
        }
      };
    });
  }
}

export const auditLogger = new AuditLogger();