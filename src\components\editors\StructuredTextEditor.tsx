import React, { useRef, useEffect, useState } from 'react';
import Editor from '@monaco-editor/react';
import { usePLCStore } from '../../store/plcStore';
import { 
  Save, 
  Play, 
  Square, 
  Download, 
  Upload, 
  Check, 
  AlertTriangle, 
  Info, 
  Code, 
  FileText, 
  Sparkles,
  Plus,
  Copy,
  Trash2,
  RefreshCw,
  Search,
  Settings,
  HelpCircle
} from 'lucide-react';
import { useAICompletion, CompletionContext } from '../../ai/hooks/useAICompletion';
import AIGhostBlock from '../../ai/components/AIGhostBlock';

const StructuredTextEditor: React.FC = () => {
  const { currentProject, activeProgram, updateProgram, simulationMode, toggleSimulation } = usePLCStore();
  const editorRef = useRef<any>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState<{
    isValid: boolean;
    errors: { message: string; line: number; column: number; severity: 'error' | 'warning' | 'info' }[];
  }>({ isValid: true, errors: [] });
  const [showSnippets, setShowSnippets] = useState(false);
  const [selectedSnippet, setSelectedSnippet] = useState<string | null>(null);
  const [cursorPosition, setCursorPosition] = useState<{ lineNumber: number; column: number }>({ lineNumber: 1, column: 1 });
  const [showExplanation, setShowExplanation] = useState(false);

  // AI Completion integration
  const { 
    suggestion, 
    showSuggestion, 
    isLoading: isAiLoading, 
    requestSuggestion, 
    acceptSuggestion, 
    dismissSuggestion,
    cycleAlternatives,
    alternatives,
    selectedAlternative
  } = useAICompletion();

  const program = currentProject?.programs.find(p => p.id === activeProgram);
  const content = typeof program?.content === 'string' ? program.content : '';
  const isSafetyProgram = program?.safetyProgram || false;

  useEffect(() => {
    if (editorRef.current) {
      // Configure ST language support
      const monaco = editorRef.current.getMonaco();
      
      // Register ST language
      monaco.languages.register({ id: 'structured-text' });
      
      // Define ST syntax highlighting
      monaco.languages.setMonarchTokensProvider('structured-text', {
        tokenizer: {
          root: [
            // Keywords
            [/\b(FUNCTION_BLOCK|END_FUNCTION_BLOCK|FUNCTION|END_FUNCTION|PROGRAM|END_PROGRAM|VAR|END_VAR|VAR_INPUT|VAR_OUTPUT|VAR_IN_OUT|VAR_TEMP|VAR_GLOBAL|IF|THEN|ELSE|ELSIF|END_IF|CASE|OF|END_CASE|FOR|TO|BY|DO|END_FOR|WHILE|END_WHILE|REPEAT|UNTIL|END_REPEAT|RETURN|EXIT)\b/, 'keyword'],
            
            // Data types
            [/\b(BOOL|BYTE|WORD|DWORD|LWORD|SINT|INT|DINT|LINT|USINT|UINT|UDINT|ULINT|REAL|LREAL|TIME|DATE|TIME_OF_DAY|DATE_AND_TIME|STRING|WSTRING|ARRAY|STRUCT|POINTER|ANY)\b/, 'type'],
            
            // Safety keywords (for safety programs)
            [/\b(SAFEBOOL|SAFETIME|SAFEINT|SAFETY_IN|SAFETY_OUT|SAFETY_INOUT|SAFETY_TASK|SAFETY_SYSTEM|SAFETY_BLOCK)\b/, 'keyword.safety'],
            
            // Constants
            [/\b(TRUE|FALSE|NULL)\b/, 'constant.language'],
            
            // Numbers
            [/\b\d+\.\d+\b/, 'number.float'],
            [/\b\d+\b/, 'number'],
            [/\b16#[0-9A-Fa-f]+\b/, 'number.hex'],
            [/\b2#[01]+\b/, 'number.binary'],
            [/\bT#[0-9smhdMS_]+\b/, 'number.time'],
            
            // Strings
            [/'[^']*'/, 'string'],
            [/"[^"]*"/, 'string'],
            
            // Comments
            [/\/\/.*$/, 'comment'],
            [/\(\*[\s\S]*?\*\)/, 'comment'],
            
            // Operators
            [/(:=|<=|>=|<>|<|>|=|\+|\-|\*|\/|\bMOD\b|\bAND\b|\bOR\b|\bXOR\b|\bNOT\b)/, 'operator'],
            
            // Identifiers
            [/[a-zA-Z_][a-zA-Z0-9_]*/, 'identifier'],
          ]
        }
      });

      // Set theme
      monaco.editor.defineTheme('st-dark', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'keyword', foreground: 'C586C0' },
          { token: 'keyword.safety', foreground: 'FF5454' },
          { token: 'type', foreground: '4EC9B0' },
          { token: 'constant.language', foreground: '569CD6' },
          { token: 'number', foreground: 'B5CEA8' },
          { token: 'number.float', foreground: 'B5CEA8' },
          { token: 'number.hex', foreground: 'D7BA7D' },
          { token: 'number.binary', foreground: 'D7BA7D' },
          { token: 'number.time', foreground: 'CE9178' },
          { token: 'string', foreground: 'CE9178' },
          { token: 'comment', foreground: '6A9955' },
          { token: 'operator', foreground: 'D4D4D4' },
        ],
        colors: {
          'editor.background': '#0a0a0a',
          'editor.foreground': '#d4d4d4',
        }
      });

      monaco.editor.setTheme('st-dark');

      // Add code snippets
      monaco.languages.registerCompletionItemProvider('structured-text', {
        provideCompletionItems: (model, position) => {
          const suggestions = [
            {
              label: 'fb',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                'FUNCTION_BLOCK ${1:BlockName}',
                'VAR_INPUT',
                '\t${2:Input1} : ${3:BOOL};',
                'END_VAR',
                '',
                'VAR_OUTPUT',
                '\t${4:Output1} : ${5:BOOL};',
                'END_VAR',
                '',
                'VAR',
                '\t${6:LocalVar} : ${7:BOOL};',
                'END_VAR',
                '',
                '${0:// Function block implementation}',
                '',
                'END_FUNCTION_BLOCK'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Function Block template'
            },
            {
              label: 'if',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                'IF ${1:condition} THEN',
                '\t${2:// statements}',
                'END_IF;'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'IF statement'
            },
            {
              label: 'ifelse',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                'IF ${1:condition} THEN',
                '\t${2:// statements}',
                'ELSE',
                '\t${3:// else statements}',
                'END_IF;'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'IF-ELSE statement'
            },
            {
              label: 'for',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                'FOR ${1:i} := ${2:0} TO ${3:10} BY ${4:1} DO',
                '\t${5:// statements}',
                'END_FOR;'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'FOR loop'
            },
            {
              label: 'while',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                'WHILE ${1:condition} DO',
                '\t${2:// statements}',
                'END_WHILE;'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'WHILE loop'
            },
            {
              label: 'repeat',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                'REPEAT',
                '\t${1:// statements}',
                'UNTIL ${2:condition}',
                'END_REPEAT;'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'REPEAT-UNTIL loop'
            },
            {
              label: 'case',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                'CASE ${1:expression} OF',
                '\t${2:value1}: ${3:// statements};',
                '\t${4:value2}: ${5:// statements};',
                'ELSE',
                '\t${6:// default statements}',
                'END_CASE;'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'CASE statement'
            },
            {
              label: 'ton',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                '${1:TimerName}(IN := ${2:InputCondition}, PT := ${3:T#1s});',
                '${4:OutputVar} := ${1:TimerName}.Q;'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Timer On-Delay (TON)'
            },
            {
              label: 'tof',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                '${1:TimerName}(IN := ${2:InputCondition}, PT := ${3:T#1s});',
                '${4:OutputVar} := ${1:TimerName}.Q;'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Timer Off-Delay (TOF)'
            },
            {
              label: 'ctu',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                '${1:CounterName}(CU := ${2:CountUp}, R := ${3:Reset}, PV := ${4:10});',
                '${5:OutputVar} := ${1:CounterName}.Q;'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Counter Up (CTU)'
            },
            {
              label: 'safety_fb',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: [
                'FUNCTION_BLOCK ${1:SafetyBlockName}',
                'VAR_INPUT',
                '\t${2:SafetyInput1} : SAFEBOOL;',
                'END_VAR',
                '',
                'VAR_OUTPUT',
                '\t${3:SafetyOutput1} : SAFEBOOL;',
                'END_VAR',
                '',
                'VAR',
                '\t${4:LocalVar} : SAFEBOOL;',
                'END_VAR',
                '',
                '// Safety function block implementation',
                '${0:// Add safety logic here}',
                '',
                'END_FUNCTION_BLOCK'
              ].join('\n'),
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Safety Function Block template'
            }
          ];
          
          return { suggestions };
        }
      });

      // Set up cursor position change listener for AI suggestions
      const editor = editorRef.current.editor;
      editor.onDidChangeCursorPosition((e) => {
        setCursorPosition(e.position);
        
        // Get current line and previous lines for context
        const model = editor.getModel();
        const lineContent = model.getLineContent(e.position.lineNumber);
        const prevLineNumber = Math.max(1, e.position.lineNumber - 1);
        const prevLineContent = model.getLineContent(prevLineNumber);
        
        // Only trigger suggestions on specific conditions
        const shouldTrigger = (
          // After typing a keyword followed by space
          /\b(IF|WHILE|FOR|CASE|REPEAT|FUNCTION_BLOCK|VAR)\s+$/.test(lineContent.substring(0, e.position.column)) ||
          // After typing a semicolon on a line
          (lineContent.includes(';') && e.position.column === lineContent.length + 1) ||
          // After typing an opening parenthesis
          lineContent.substring(0, e.position.column).endsWith('(')
        );
        
        if (shouldTrigger) {
          // Get previous lines for context
          const startLine = Math.max(1, e.position.lineNumber - 5);
          const previousLines = [];
          for (let i = startLine; i <= e.position.lineNumber; i++) {
            previousLines.push(model.getLineContent(i));
          }
          
          // Get available tags
          const availableTags = currentProject?.globalTags.map(t => t.name) || [];
          
          // Build context for AI suggestion
          const context: CompletionContext = {
            mode: 'st',
            programType: isSafetyProgram ? 'Safety' : 'Standard',
            previousLines,
            scopeTags: availableTags,
            currentIndent: getIndentLevel(lineContent),
            cursorPosition: { line: e.position.lineNumber, column: e.position.column }
          };
          
          // Request suggestion with a small delay to avoid too many requests
          const timer = setTimeout(() => {
            requestSuggestion(context);
          }, 500);
          
          return () => clearTimeout(timer);
        }
      });
    }
  }, [currentProject, isSafetyProgram, requestSuggestion]);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = { editor, getMonaco: () => monaco };
  };

  const handleChange = (value: string | undefined) => {
    if (program && value !== undefined) {
      updateProgram(program.id, { content: value });
    }
  };

  const validateCode = () => {
    if (!editorRef.current || !content) return;
    
    setIsValidating(true);
    
    // Simulate validation process
    setTimeout(() => {
      const errors = [];
      
      // Simple validation rules
      const lines = content.split('\n');
      
      // Check for matching END_ statements
      const blocks = {
        'FUNCTION_BLOCK': 'END_FUNCTION_BLOCK',
        'FUNCTION': 'END_FUNCTION',
        'PROGRAM': 'END_PROGRAM',
        'IF': 'END_IF',
        'CASE': 'END_CASE',
        'FOR': 'END_FOR',
        'WHILE': 'END_WHILE',
        'REPEAT': 'END_REPEAT',
        'VAR': 'END_VAR',
        'VAR_INPUT': 'END_VAR',
        'VAR_OUTPUT': 'END_VAR',
        'VAR_IN_OUT': 'END_VAR',
        'VAR_TEMP': 'END_VAR',
        'VAR_GLOBAL': 'END_VAR'
      };
      
      const stack: { keyword: string; line: number; column: number }[] = [];
      
      lines.forEach((line, lineIndex) => {
        // Check for opening blocks
        for (const [openKeyword, closeKeyword] of Object.entries(blocks)) {
          const openMatch = line.match(new RegExp(`\\b${openKeyword}\\b`));
          if (openMatch) {
            stack.push({ 
              keyword: openKeyword, 
              line: lineIndex, 
              column: openMatch.index || 0 
            });
          }
          
          // Check for closing blocks
          const closeMatch = line.match(new RegExp(`\\b${closeKeyword}\\b`));
          if (closeMatch) {
            if (stack.length === 0) {
              errors.push({
                message: `Unexpected ${closeKeyword} without matching ${Object.keys(blocks).find(key => blocks[key as keyof typeof blocks] === closeKeyword)}`,
                line: lineIndex,
                column: closeMatch.index || 0,
                severity: 'error'
              });
            } else {
              const lastOpen = stack.pop();
              if (lastOpen && blocks[lastOpen.keyword as keyof typeof blocks] !== closeKeyword) {
                errors.push({
                  message: `Expected ${blocks[lastOpen.keyword as keyof typeof blocks]} but found ${closeKeyword}`,
                  line: lineIndex,
                  column: closeMatch.index || 0,
                  severity: 'error'
                });
              }
            }
          }
        }
        
        // Check for missing semicolons
        if (line.trim() && 
            !line.trim().endsWith(';') && 
            !line.trim().match(/^\s*(\/\/|FUNCTION_BLOCK|END_FUNCTION_BLOCK|FUNCTION|END_FUNCTION|PROGRAM|END_PROGRAM|VAR|END_VAR|VAR_INPUT|VAR_OUTPUT|VAR_IN_OUT|VAR_TEMP|VAR_GLOBAL|IF|THEN|ELSE|ELSIF|END_IF|CASE|OF|END_CASE|FOR|TO|BY|DO|END_FOR|WHILE|END_WHILE|REPEAT|UNTIL|END_REPEAT|RETURN|EXIT|\(\*|\*\))/)) {
          errors.push({
            message: 'Statement missing semicolon',
            line: lineIndex,
            column: line.length,
            severity: 'warning'
          });
        }
        
        // Check for safety-related issues in safety programs
        if (isSafetyProgram) {
          // Check for non-safety variables in safety program
          if (line.match(/\b(BOOL|INT|DINT|REAL)\b/) && !line.match(/\b(SAFE[A-Z]+)\b/)) {
            errors.push({
              message: 'Safety program should use safety data types (SAFEBOOL, SAFEINT, etc.)',
              line: lineIndex,
              column: 0,
              severity: 'warning'
            });
          }
        }
      });
      
      // Check for unclosed blocks
      stack.forEach(item => {
        errors.push({
          message: `Missing ${blocks[item.keyword as keyof typeof blocks]} for ${item.keyword} at line ${item.line + 1}`,
          line: item.line,
          column: item.column,
          severity: 'error'
        });
      });
      
      // Update validation results
      setValidationResults({
        isValid: errors.filter(e => e.severity === 'error').length === 0,
        errors
      });
      
      // Set editor markers
      if (editorRef.current) {
        const monaco = editorRef.current.getMonaco();
        const model = editorRef.current.editor.getModel();
        
        const markers = errors.map(error => ({
          severity: error.severity === 'error' ? monaco.MarkerSeverity.Error : 
                   error.severity === 'warning' ? monaco.MarkerSeverity.Warning : 
                   monaco.MarkerSeverity.Info,
          message: error.message,
          startLineNumber: error.line + 1,
          startColumn: error.column + 1,
          endLineNumber: error.line + 1,
          endColumn: error.column + 30
        }));
        
        monaco.editor.setModelMarkers(model, 'st-validation', markers);
      }
      
      setIsValidating(false);
    }, 500);
  };

  const handleSaveProgram = () => {
    if (program) {
      updateProgram(program.id, { 
        content,
        modified: false,
        lastSaved: new Date()
      });
      alert('Program saved successfully');
    }
  };

  const handleInsertSnippet = (snippetCode: string) => {
    if (!editorRef.current) return;
    
    const editor = editorRef.current.editor;
    const position = editor.getPosition();
    
    editor.executeEdits('insert-snippet', [
      {
        range: {
          startLineNumber: position.lineNumber,
          startColumn: position.column,
          endLineNumber: position.lineNumber,
          endColumn: position.column
        },
        text: snippetCode
      }
    ]);
    
    setShowSnippets(false);
  };

  const handleAcceptSuggestion = () => {
    if (!editorRef.current || !suggestion) return;
    
    const accepted = acceptSuggestion();
    if (!accepted) return;
    
    const editor = editorRef.current.editor;
    const position = editor.getPosition();
    
    editor.executeEdits('insert-ai-suggestion', [
      {
        range: {
          startLineNumber: position.lineNumber,
          startColumn: position.column,
          endLineNumber: position.lineNumber,
          endColumn: position.column
        },
        text: accepted.content
      }
    ]);
  };

  // Helper function to get indent level of a line
  const getIndentLevel = (line: string): number => {
    const match = line.match(/^(\s*)/);
    return match ? match[1].length : 0;
  };

  const codeSnippets = {
    'Function Block': {
      code: `FUNCTION_BLOCK NewFunctionBlock
VAR_INPUT
    Input1 : BOOL;
    Input2 : BOOL;
END_VAR

VAR_OUTPUT
    Output1 : BOOL;
END_VAR

VAR
    LocalVar : BOOL;
END_VAR

// Function block implementation
Output1 := Input1 AND Input2;

END_FUNCTION_BLOCK`,
      description: 'Basic function block template'
    },
    'IF Statement': {
      code: `IF Condition THEN
    // True statements
ELSE
    // False statements
END_IF;`,
      description: 'IF-ELSE control structure'
    },
    'FOR Loop': {
      code: `FOR i := 0 TO 10 BY 1 DO
    // Loop body
END_FOR;`,
      description: 'FOR loop with counter'
    },
    'WHILE Loop': {
      code: `WHILE Condition DO
    // Loop body
END_WHILE;`,
      description: 'WHILE loop'
    },
    'CASE Statement': {
      code: `CASE Expression OF
    1: // Case 1 statements;
    2: // Case 2 statements;
ELSE
    // Default statements
END_CASE;`,
      description: 'CASE selection structure'
    },
    'Timer On-Delay': {
      code: `Timer1(IN := InputCondition, PT := T#5s);
Output := Timer1.Q;`,
      description: 'TON timer implementation'
    },
    'Counter Up': {
      code: `Counter1(CU := CountUp, R := Reset, PV := 10);
CountReached := Counter1.Q;`,
      description: 'CTU counter implementation'
    },
    'Safety Function Block': {
      code: `FUNCTION_BLOCK SafetyBlock
VAR_INPUT
    SafetyInput1 : SAFEBOOL;
    SafetyInput2 : SAFEBOOL;
END_VAR

VAR_OUTPUT
    SafetyOutput : SAFEBOOL;
END_VAR

VAR
    SafetyLocal : SAFEBOOL;
END_VAR

// Safety logic implementation
SafetyOutput := SafetyInput1 AND SafetyInput2;

END_FUNCTION_BLOCK`,
      description: 'Safety-rated function block template'
    },
    'Emergency Stop': {
      code: `// Emergency stop logic with monitoring
EmergencyStop(
    Activate := E_STOP_BUTTON,
    S_StartReset := S_RESET_BUTTON,
    Reset := RESET_BUTTON,
    S_AutoReset := FALSE
);

SYSTEM_ENABLE := EmergencyStop.Ready AND NOT EmergencyStop.Error;`,
      description: 'Emergency stop safety function'
    }
  };

  const defaultSTCode = `FUNCTION_BLOCK PumpControl_FB
// Pump control function block with safety interlocks

VAR_INPUT
    Start_Command : BOOL;
    Stop_Command : BOOL;
    Emergency_Stop : BOOL;
    Pressure_OK : BOOL;
END_VAR

VAR_OUTPUT
    Motor_Output : BOOL;
    Status_Running : BOOL;
    Fault_Active : BOOL;
END_VAR

VAR
    Pump_Run_Memory : BOOL;
    Runtime_Timer : TON;
    Fault_Timer : TON;
END_VAR

// Main logic
IF Emergency_Stop THEN
    Pump_Run_Memory := FALSE;
    Fault_Active := TRUE;
ELSIF Start_Command AND Pressure_OK AND NOT Fault_Active THEN
    Pump_Run_Memory := TRUE;
ELSIF Stop_Command THEN
    Pump_Run_Memory := FALSE;
END_IF;

// Output assignments
Motor_Output := Pump_Run_Memory AND NOT Emergency_Stop;
Status_Running := Pump_Run_Memory;

// Runtime monitoring
Runtime_Timer(IN := Pump_Run_Memory, PT := T#3600S);

// Fault reset logic
IF NOT Emergency_Stop AND Stop_Command THEN
    Fault_Active := FALSE;
END_IF;

END_FUNCTION_BLOCK`;

  const defaultSafetyCode = `FUNCTION_BLOCK SafetyControl_FB
// Safety control function block with emergency stop and light curtain

VAR_INPUT
    Emergency_Stop : SAFEBOOL;
    Light_Curtain_OK : SAFEBOOL;
    Reset_Button : SAFEBOOL;
    Muting_Sensor1 : SAFEBOOL;
    Muting_Sensor2 : SAFEBOOL;
END_VAR

VAR_OUTPUT
    Safety_OK : SAFEBOOL;
    Machine_Enable : SAFEBOOL;
    Fault_Present : SAFEBOOL;
    Muting_Active : SAFEBOOL;
END_VAR

VAR
    Safety_Memory : SAFEBOOL;
    Fault_Memory : SAFEBOOL;
    Muting_Timer : SAFETIMER;
    Reset_Edge : R_TRIG;
END_VAR

// Safety logic implementation
Reset_Edge(CLK := Reset_Button);

// Muting logic for light curtain
Muting_Timer(IN := Muting_Sensor1 AND Muting_Sensor2, PT := T#10s);
Muting_Active := Muting_Timer.Q;

// Main safety logic
IF NOT Emergency_Stop THEN
    Safety_Memory := FALSE;
    Fault_Memory := TRUE;
ELSIF NOT (Light_Curtain_OK OR Muting_Active) THEN
    Safety_Memory := FALSE;
    Fault_Memory := TRUE;
ELSIF Reset_Edge.Q AND NOT Fault_Memory THEN
    Safety_Memory := TRUE;
END_IF;

// Output assignments
Safety_OK := Safety_Memory;
Machine_Enable := Safety_OK;
Fault_Present := Fault_Memory;

// Fault reset logic
IF Reset_Edge.Q AND Emergency_Stop AND (Light_Curtain_OK OR Muting_Active) THEN
    Fault_Memory := FALSE;
END_IF;

END_FUNCTION_BLOCK`;

  if (!program) {
    return (
      <div className="h-full flex items-center justify-center text-gray-400">
        <div className="text-center">
          <div className="text-lg mb-2">No Program Selected</div>
          <div className="text-sm">Select a program from the project navigator</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-950 flex flex-col">
      {/* Toolbar */}
      <div className="bg-gray-800 border-b border-gray-700 p-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {isSafetyProgram ? (
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-error" />
              <span className="text-white font-semibold">Safety Structured Text</span>
              <div className="px-2 py-1 rounded text-xs bg-error/20 text-error border border-error/30">
                {program.silRating || 'SIL2'}
              </div>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <Code className="w-5 h-5 text-success" />
              <span className="text-white font-semibold">Structured Text</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={validateCode}
            className={`p-2 rounded transition-colors ${
              isValidating ? 'text-blue-400 animate-pulse' : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
            title="Validate Code"
            disabled={isValidating}
          >
            <Check className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => setShowSnippets(!showSnippets)}
            className={`p-2 rounded transition-colors ${
              showSnippets ? 'bg-blue-600/20 text-blue-400' : 'text-gray-400 hover:text-white hover:bg-gray-700'
            }`}
            title="Code Snippets"
          >
            <Plus className="w-4 h-4" />
          </button>
          
          <button
            onClick={handleSaveProgram}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title="Save Program"
          >
            <Save className="w-4 h-4" />
          </button>
          
          <div className="w-px h-6 bg-gray-600 mx-1"></div>
          
          <button
            onClick={toggleSimulation}
            className={`p-2 rounded transition-colors ${
              simulationMode ? 'text-primary bg-primary/20' : 'text-gray-400 hover:text-primary hover:bg-gray-700'
            }`}
            title={simulationMode ? 'Stop Simulation' : 'Start Simulation'}
          >
            {simulationMode ? <Square className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </button>
          
          <button
            className="p-2 text-gray-400 hover:text-success hover:bg-gray-700 rounded transition-colors"
            title="Download to PLC"
          >
            <Download className="w-4 h-4" />
          </button>
          
          <button
            className="p-2 text-gray-400 hover:text-secondary hover:bg-gray-700 rounded transition-colors"
            title="Upload from PLC"
          >
            <Upload className="w-4 h-4" />
          </button>
          
          <div className="w-px h-6 bg-gray-600 mx-1"></div>
          
          <button
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title="Settings"
          >
            <Settings className="w-4 h-4" />
          </button>
          
          <button
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title="Help"
          >
            <HelpCircle className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      {/* Snippets Panel */}
      {showSnippets && (
        <div className="bg-gray-800 border-b border-gray-700 p-3">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-white font-medium">Code Snippets</h3>
            <button
              onClick={() => setShowSnippets(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          
          <div className="grid grid-cols-3 gap-3">
            {Object.entries(codeSnippets).map(([name, snippet]) => (
              <div
                key={name}
                className={`p-3 rounded border cursor-pointer transition-all ${
                  selectedSnippet === name 
                    ? 'bg-blue-600/20 border-blue-500' 
                    : 'bg-gray-700 border-gray-600 hover:border-blue-500'
                }`}
                onClick={() => setSelectedSnippet(name)}
                onDoubleClick={() => handleInsertSnippet(snippet.code)}
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="text-white font-medium">{name}</span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleInsertSnippet(snippet.code);
                    }}
                    className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded"
                  >
                    <Plus className="w-3 h-3" />
                  </button>
                </div>
                <p className="text-xs text-gray-400">{snippet.description}</p>
              </div>
            ))}
          </div>
          
          {selectedSnippet && (
            <div className="mt-3 pt-3 border-t border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white text-sm font-medium">{selectedSnippet}</h4>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleInsertSnippet(codeSnippets[selectedSnippet as keyof typeof codeSnippets].code)}
                    className="flex items-center space-x-1 text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded"
                  >
                    <Plus className="w-3 h-3" />
                    <span>Insert</span>
                  </button>
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(codeSnippets[selectedSnippet as keyof typeof codeSnippets].code);
                      alert('Snippet copied to clipboard');
                    }}
                    className="flex items-center space-x-1 text-xs bg-gray-600 hover:bg-gray-700 text-white px-2 py-1 rounded"
                  >
                    <Copy className="w-3 h-3" />
                    <span>Copy</span>
                  </button>
                </div>
              </div>
              <pre className="bg-gray-900 p-3 rounded text-xs text-gray-300 overflow-x-auto">
                {codeSnippets[selectedSnippet as keyof typeof codeSnippets].code}
              </pre>
            </div>
          )}
        </div>
      )}
      
      {/* Validation Results */}
      {validationResults.errors.length > 0 && (
        <div className="bg-gray-800 border-b border-gray-700 p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              {validationResults.isValid ? (
                <Check className="w-4 h-4 text-green-400" />
              ) : (
                <AlertTriangle className="w-4 h-4 text-red-400" />
              )}
              <h3 className="text-white font-medium">
                Validation Results: {validationResults.isValid ? 'Valid' : 'Invalid'}
              </h3>
            </div>
            <div className="text-sm text-gray-400">
              {validationResults.errors.filter(e => e.severity === 'error').length} errors, 
              {validationResults.errors.filter(e => e.severity === 'warning').length} warnings
            </div>
          </div>
          
          <div className="max-h-32 overflow-y-auto">
            {validationResults.errors.map((error, index) => (
              <div 
                key={index}
                className={`flex items-start space-x-2 p-2 rounded mb-1 ${
                  error.severity === 'error' ? 'bg-red-900/30' : 
                  error.severity === 'warning' ? 'bg-amber-900/30' : 'bg-blue-900/30'
                }`}
                onClick={() => {
                  if (editorRef.current) {
                    editorRef.current.editor.revealPositionInCenter({
                      lineNumber: error.line + 1,
                      column: error.column + 1
                    });
                    editorRef.current.editor.setPosition({
                      lineNumber: error.line + 1,
                      column: error.column + 1
                    });
                    editorRef.current.editor.focus();
                  }
                }}
              >
                {error.severity === 'error' ? (
                  <AlertTriangle className="w-4 h-4 text-red-400 flex-shrink-0 mt-0.5" />
                ) : error.severity === 'warning' ? (
                  <AlertTriangle className="w-4 h-4 text-amber-400 flex-shrink-0 mt-0.5" />
                ) : (
                  <Info className="w-4 h-4 text-blue-400 flex-shrink-0 mt-0.5" />
                )}
                <div className="flex-1">
                  <div className="text-white text-sm">{error.message}</div>
                  <div className="text-xs text-gray-400">Line {error.line + 1}, Column {error.column + 1}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Editor */}
      <div className="flex-1 relative">
        <Editor
          height="100%"
          language="structured-text"
          value={content || (isSafetyProgram ? defaultSafetyCode : defaultSTCode)}
          onChange={handleChange}
          onMount={handleEditorDidMount}
          options={{
            theme: 'st-dark',
            fontSize: 14,
            fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            insertSpaces: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            renderWhitespace: 'selection',
            bracketPairColorization: { enabled: true },
            autoIndent: 'full',
            formatOnPaste: true,
            formatOnType: true,
            suggestOnTriggerCharacters: true,
            acceptSuggestionOnEnter: 'on',
            folding: true,
            foldingStrategy: 'indentation',
            showFoldingControls: 'always'
          }}
        />
        
        {/* AI Suggestion Ghost Block */}
        {showSuggestion && suggestion && (
          <AIGhostBlock
            suggestion={suggestion}
            position={{ 
              top: (cursorPosition.lineNumber * 20) + 50, // Approximate line height
              left: (cursorPosition.column * 8) + 50 // Approximate character width
            }}
            onAccept={handleAcceptSuggestion}
            onDismiss={dismissSuggestion}
            onCycleAlternative={cycleAlternatives}
            mode="st"
            alternativeIndex={selectedAlternative}
            alternativesCount={alternatives.length}
            showExplanation={showExplanation}
            onToggleExplanation={() => setShowExplanation(!showExplanation)}
          />
        )}
      </div>
      
      {/* Status Bar */}
      <div className="bg-gray-800 border-t border-gray-700 p-2 flex items-center justify-between text-xs text-gray-400">
        <div className="flex items-center space-x-4">
          <span>
            {program.type.toUpperCase()} • {program.name}
          </span>
          <span>
            {program.lastSaved ? `Last saved: ${program.lastSaved.toLocaleTimeString()}` : 'Not saved'}
          </span>
        </div>
        <div className="flex items-center space-x-4">
          <span>
            {isSafetyProgram ? 'Safety Program' : 'Standard Program'}
          </span>
          <span>
            {validationResults.isValid ? 'Valid' : 'Invalid'}
          </span>
          <span>
            {simulationMode ? 'Simulation Active' : 'Simulation Inactive'}
          </span>
        </div>
      </div>
    </div>
  );
};

// X icon component
const X = ({ className }: { className?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M18 6 6 18"></path>
    <path d="m6 6 12 12"></path>
  </svg>
);

export default StructuredTextEditor;