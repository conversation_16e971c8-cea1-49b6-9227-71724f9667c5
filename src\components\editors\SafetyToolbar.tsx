import React from 'react';
import { 
  Shield, 
  AlertTriangle, 
  Lock, 
  Unlock,
  Eye,
  EyeOff,
  Zap,
  Timer,
  GitBranch,
  Play,
  Pause,
  CheckCircle2
} from 'lucide-react';

interface SafetyToolbarProps {
  onAddElement: (elementType: string) => void;
  disabled?: boolean;
}

const SafetyToolbar: React.FC<SafetyToolbarProps> = ({ onAddElement, disabled = false }) => {
  const toolbarItems = [
    {
      id: 'safety-relay',
      name: 'Safety Relay',
      icon: <Shield className="w-4 h-4" />,
      symbol: 'SR',
      description: 'Safety Relay Function Block (SIL3)',
      silLevel: 'SIL3',
      action: () => onAddElement('safety-relay')
    },
    {
      id: 'emergency-stop',
      name: 'E-Stop',
      icon: <AlertTriangle className="w-4 h-4" />,
      symbol: 'ES',
      description: 'Emergency Stop Function',
      silLevel: 'SIL3',
      action: () => onAddElement('emergency-stop')
    },
    {
      id: 'light-curtain',
      name: 'Light Curtain',
      icon: <Eye className="w-4 h-4" />,
      symbol: 'LC',
      description: 'Safety Light Curtain with Muting',
      silLevel: 'SIL3',
      action: () => onAddElement('light-curtain')
    },
    {
      id: 'safety-gate',
      name: 'Safety Gate',
      icon: <Lock className="w-4 h-4" />,
      symbol: 'SG',
      description: 'Safety Gate Monitor',
      silLevel: 'SIL2',
      action: () => onAddElement('safety-gate')
    },
    {
      id: 'muting',
      name: 'Muting',
      icon: <EyeOff className="w-4 h-4" />,
      symbol: 'MUT',
      description: 'Safety Muting Function',
      silLevel: 'SIL2',
      action: () => onAddElement('muting')
    },
    {
      id: 'safety-timer',
      name: 'Safety Timer',
      icon: <Timer className="w-4 h-4" />,
      symbol: 'ST',
      description: 'Safety Timer Function',
      silLevel: 'SIL2',
      action: () => onAddElement('safety-timer')
    },
    {
      id: 'two-hand',
      name: 'Two Hand',
      icon: <Unlock className="w-4 h-4" />,
      symbol: 'TH',
      description: 'Two Hand Control',
      silLevel: 'SIL3',
      action: () => onAddElement('two-hand')
    },
    {
      id: 'safety-enable',
      name: 'Safety Enable',
      icon: <CheckCircle2 className="w-4 h-4" />,
      symbol: 'SE',
      description: 'Safety Enable Function',
      silLevel: 'SIL2',
      action: () => onAddElement('safety-enable')
    },
    {
      id: 'safety-monitor',
      name: 'Monitor',
      icon: <Eye className="w-4 h-4" />,
      symbol: 'SM',
      description: 'Safety Monitoring Function',
      silLevel: 'SIL2',
      action: () => onAddElement('safety-monitor')
    },
    {
      id: 'safety-logic',
      name: 'Safety Logic',
      icon: <GitBranch className="w-4 h-4" />,
      symbol: 'SL',
      description: 'Safety Logic Function',
      silLevel: 'SIL3',
      action: () => onAddElement('safety-logic')
    }
  ];

  const getSilColor = (silLevel: string) => {
    switch (silLevel) {
      case 'SIL3': return 'text-red-400 bg-red-400/20';
      case 'SIL2': return 'text-orange-400 bg-orange-400/20';
      case 'SIL1': return 'text-yellow-400 bg-yellow-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  const handleSafetyAction = (action: string) => {
    switch (action) {
      case 'validate':
        alert('Safety logic validation in progress...');
        setTimeout(() => {
          alert('Safety validation passed. All safety functions meet SIL requirements.');
        }, 1500);
        break;
      case 'estop':
        alert('Emergency stop triggered. All safety outputs disabled.');
        break;
      case 'test':
        alert('Safety test mode activated. Follow test procedure to verify all safety functions.');
        break;
      default:
        break;
    }
  };

  return (
    <div className="bg-gray-800 border-b border-gray-700 p-2">
      <div className="flex items-center space-x-1 overflow-x-auto">
        <div className="text-sm text-gray-400 mr-4 whitespace-nowrap">Safety Elements:</div>
        
        {toolbarItems.map(item => (
          <button
            key={item.id}
            onClick={item.action}
            disabled={disabled}
            className="flex flex-col items-center p-2 min-w-20 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed border border-gray-600 rounded transition-colors group relative"
            title={`${item.description}\nSafety Level: ${item.silLevel}`}
          >
            <div className="text-red-400 mb-1">
              {item.icon}
            </div>
            <div className="text-xs text-white font-mono font-bold">
              {item.symbol}
            </div>
            <div className="text-xs text-gray-400 mt-1 text-center leading-tight">
              {item.name}
            </div>
            <div className={`absolute -top-1 -right-1 text-xs px-1 rounded ${getSilColor(item.silLevel)}`}>
              {item.silLevel}
            </div>
          </button>
        ))}
        
        <div className="w-px h-12 bg-gray-600 mx-2" />
        
        {/* Safety Control buttons */}
        <button
          className="flex flex-col items-center p-2 min-w-16 bg-green-700 hover:bg-green-600 border border-green-600 rounded transition-colors"
          title="Validate Safety Logic"
          onClick={() => handleSafetyAction('validate')}
        >
          <CheckCircle2 className="w-4 h-4 text-white mb-1" />
          <div className="text-xs text-white">Validate</div>
        </button>
        
        <button
          className="flex flex-col items-center p-2 min-w-16 bg-red-700 hover:bg-red-600 border border-red-600 rounded transition-colors"
          title="Safety Stop"
          onClick={() => handleSafetyAction('estop')}
        >
          <AlertTriangle className="w-4 h-4 text-white mb-1" />
          <div className="text-xs text-white">E-Stop</div>
        </button>
        
        <button
          className="flex flex-col items-center p-2 min-w-16 bg-blue-700 hover:bg-blue-600 border border-blue-600 rounded transition-colors"
          title="Safety Test Mode"
          onClick={() => handleSafetyAction('test')}
        >
          <Shield className="w-4 h-4 text-white mb-1" />
          <div className="text-xs text-white">Test</div>
        </button>
      </div>
      
      {/* Safety Warning */}
      <div className="mt-2 p-2 bg-red-900/20 border border-red-600/30 rounded">
        <div className="flex items-center space-x-2 text-red-400 text-xs">
          <AlertTriangle className="w-4 h-4" />
          <span>Safety-rated functions require certified validation and testing before deployment</span>
        </div>
      </div>
    </div>
  );
};

export default SafetyToolbar;