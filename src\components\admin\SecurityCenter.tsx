import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Search, 
  Filter, 
  AlertTriangle, 
  CheckCircle2, 
  Clock, 
  User, 
  Refresh<PERSON>w, 
  FileText, 
  Settings, 
  Lock, 
  Key, 
  Eye, 
  EyeOff, 
  X, 
  Save,
  Download,
  Plus,
  Edit,
  Trash2,
  ExternalLink,
  Info
} from 'lucide-react';

interface SecurityEvent {
  id: string;
  eventType: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  source: string;
  description: string;
  user?: string;
  ipAddress?: string;
  timestamp: Date;
  status: 'open' | 'in_progress' | 'resolved' | 'false_positive';
  resolution?: string;
  resolvedBy?: string;
  resolvedAt?: Date;
}

interface SecurityPolicy {
  id: string;
  name: string;
  type: 'password' | 'access' | 'authentication' | 'network' | 'data';
  description: string;
  settings: Record<string, any>;
  enabled: boolean;
  lastModified: Date;
  modifiedBy: string;
}

const SecurityCenter: React.FC = () => {
  const [activeTab, setActiveTab] = useState('events');
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<SecurityEvent[]>([]);
  const [securityPolicies, setSecurityPolicies] = useState<SecurityPolicy[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterSeverity, setFilterSeverity] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedEvent, setSelectedEvent] = useState<SecurityEvent | null>(null);
  const [selectedPolicy, setSelectedPolicy] = useState<SecurityPolicy | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showResolutionModal, setShowResolutionModal] = useState(false);
  const [resolution, setResolution] = useState('');
  const [showPolicyModal, setShowPolicyModal] = useState(false);
  const [editingPolicy, setEditingPolicy] = useState<SecurityPolicy | null>(null);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportType, setReportType] = useState('security_events');
  const [reportTimeframe, setReportTimeframe] = useState('30d');

  // Load mock data
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const mockEvents: SecurityEvent[] = [
        {
          id: '1',
          eventType: 'failed_login',
          severity: 'medium',
          source: 'authentication',
          description: 'Multiple failed login attempts <NAME_EMAIL>',
          user: '<EMAIL>',
          ipAddress: '*************',
          timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
          status: 'open'
        },
        {
          id: '2',
          eventType: 'permission_change',
          severity: 'high',
          source: 'user_management',
          description: 'Admin privileges granted <NAME_EMAIL>',
          user: '<EMAIL>',
          ipAddress: '*************',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          status: 'in_progress'
        },
        {
          id: '3',
          eventType: 'suspicious_access',
          severity: 'critical',
          source: 'api',
          description: 'API access from unusual location <NAME_EMAIL>',
          user: '<EMAIL>',
          ipAddress: '************',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
          status: 'resolved',
          resolution: 'Verified with user - business travel to new location',
          resolvedBy: 'Sarah Admin',
          resolvedAt: new Date(Date.now() - 1000 * 60 * 60 * 4) // 4 hours ago
        },
        {
          id: '4',
          eventType: 'safety_override',
          severity: 'critical',
          source: 'plc_deployment',
          description: 'Safety interlock bypassed during deployment',
          user: '<EMAIL>',
          ipAddress: '*************',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
          status: 'resolved',
          resolution: 'Authorized override for maintenance - proper procedures followed',
          resolvedBy: 'Sarah Admin',
          resolvedAt: new Date(Date.now() - 1000 * 60 * 60 * 23) // 23 hours ago
        },
        {
          id: '5',
          eventType: 'configuration_change',
          severity: 'medium',
          source: 'system_settings',
          description: 'Authentication policy modified',
          user: '<EMAIL>',
          ipAddress: '*************',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 48), // 2 days ago
          status: 'resolved',
          resolution: 'Planned policy update - documented in change management',
          resolvedBy: 'David Manager',
          resolvedAt: new Date(Date.now() - 1000 * 60 * 60 * 47) // 47 hours ago
        },
        {
          id: '6',
          eventType: 'unauthorized_access',
          severity: 'high',
          source: 'file_system',
          description: 'Attempt to access restricted project files',
          user: '<EMAIL>',
          ipAddress: '*************',
          timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
          status: 'open'
        },
        {
          id: '7',
          eventType: 'malware_detected',
          severity: 'critical',
          source: 'upload',
          description: 'Potential malware detected in uploaded file',
          user: '<EMAIL>',
          ipAddress: '*************',
          timestamp: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago
          status: 'false_positive',
          resolution: 'Verified file with security team - false positive',
          resolvedBy: 'Sarah Admin',
          resolvedAt: new Date(Date.now() - 1000 * 60 * 100) // 1 hour 40 minutes ago
        }
      ];

      const mockPolicies: SecurityPolicy[] = [
        {
          id: '1',
          name: 'Password Policy',
          type: 'password',
          description: 'Defines password complexity and expiration requirements',
          settings: {
            minLength: 12,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSpecialChars: true,
            expiryDays: 90,
            preventReuse: 5
          },
          enabled: true,
          lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
          modifiedBy: 'Sarah Admin'
        },
        {
          id: '2',
          name: 'Multi-Factor Authentication',
          type: 'authentication',
          description: 'Configures MFA requirements for different user roles',
          settings: {
            adminRequired: true,
            engineerRequired: true,
            operatorRequired: false,
            viewerRequired: false,
            allowedMethods: ['app', 'email', 'sms'],
            graceLoginCount: 0
          },
          enabled: true,
          lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15), // 15 days ago
          modifiedBy: 'Sarah Admin'
        },
        {
          id: '3',
          name: 'Session Security',
          type: 'access',
          description: 'Configures session timeout and concurrent session limits',
          settings: {
            sessionTimeoutMinutes: 60,
            extendOnActivity: true,
            maxConcurrentSessions: 3,
            forceLogoutOnPasswordChange: true
          },
          enabled: true,
          lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10), // 10 days ago
          modifiedBy: 'David Manager'
        },
        {
          id: '4',
          name: 'IP Restriction',
          type: 'network',
          description: 'Restricts access based on IP address ranges',
          settings: {
            enableRestriction: false,
            allowedRanges: ['***********/16', '10.0.0.0/8'],
            blockList: ['***********/24'],
            alertOnBlock: true
          },
          enabled: false,
          lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5), // 5 days ago
          modifiedBy: 'Sarah Admin'
        },
        {
          id: '5',
          name: 'Data Encryption',
          type: 'data',
          description: 'Configures encryption for sensitive data',
          settings: {
            encryptProjects: true,
            encryptUserData: true,
            encryptionAlgorithm: 'AES-256-GCM',
            rotateKeysInterval: 90 // days
          },
          enabled: true,
          lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24 * 45), // 45 days ago
          modifiedBy: 'Sarah Admin'
        },
        {
          id: '6',
          name: 'Audit Logging',
          type: 'data',
          description: 'Configures audit logging for compliance',
          settings: {
            logUserActions: true,
            logAdminActions: true,
            logSystemEvents: true,
            retentionPeriod: 365, // days
            exportFormat: 'JSON'
          },
          enabled: true,
          lastModified: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60), // 60 days ago
          modifiedBy: 'Sarah Admin'
        }
      ];
      
      setSecurityEvents(mockEvents);
      setFilteredEvents(mockEvents);
      setSecurityPolicies(mockPolicies);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter events based on search and filters
  useEffect(() => {
    let result = securityEvents;
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(event => 
        event.description.toLowerCase().includes(query) || 
        event.eventType.toLowerCase().includes(query) ||
        (event.user && event.user.toLowerCase().includes(query))
      );
    }
    
    if (filterSeverity !== 'all') {
      result = result.filter(event => event.severity === filterSeverity);
    }
    
    if (filterStatus !== 'all') {
      result = result.filter(event => event.status === filterStatus);
    }
    
    setFilteredEvents(result);
  }, [securityEvents, searchQuery, filterSeverity, filterStatus]);

  const handleResolveEvent = () => {
    if (!selectedEvent || !resolution) return;
    
    const updatedEvents = securityEvents.map(event => 
      event.id === selectedEvent.id 
        ? { 
            ...event, 
            status: 'resolved', 
            resolution, 
            resolvedBy: 'Sarah Admin', 
            resolvedAt: new Date() 
          } 
        : event
    );
    
    setSecurityEvents(updatedEvents);
    setSelectedEvent({
      ...selectedEvent,
      status: 'resolved',
      resolution,
      resolvedBy: 'Sarah Admin',
      resolvedAt: new Date()
    });
    setShowResolutionModal(false);
    setResolution('');
  };

  const handleSavePolicy = () => {
    if (!editingPolicy) return;
    
    const updatedPolicies = securityPolicies.map(policy => 
      policy.id === editingPolicy.id 
        ? { 
            ...editingPolicy, 
            lastModified: new Date(),
            modifiedBy: 'Sarah Admin'
          } 
        : policy
    );
    
    setSecurityPolicies(updatedPolicies);
    setSelectedPolicy({
      ...editingPolicy,
      lastModified: new Date(),
      modifiedBy: 'Sarah Admin'
    });
    setShowPolicyModal(false);
    setEditingPolicy(null);
  };

  const handleGenerateReport = () => {
    // In a real implementation, this would generate and download a report
    alert(`Generating ${reportType} report for the last ${reportTimeframe}`);
    setShowReportModal(false);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'high':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'medium':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'low':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'in_progress':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'resolved':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'false_positive':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getPolicyTypeColor = (type: string) => {
    switch (type) {
      case 'password':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'authentication':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'access':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'network':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'data':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const renderEventsTab = () => (
    <div className="h-full flex flex-col">
      {/* Header with search and filters */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Security Events</h3>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowReportModal(true)}
              className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <FileText className="w-4 h-4" />
              <span>Generate Report</span>
            </button>
            <button
              onClick={() => {
                setIsLoading(true);
                setTimeout(() => setIsLoading(false), 1000);
              }}
              className={`p-2 text-gray-300 hover:text-white hover:bg-gray-700 rounded transition-colors ${isLoading ? 'animate-spin text-blue-400' : ''}`}
              disabled={isLoading}
            >
              <RefreshCw className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div className="col-span-3 sm:col-span-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search security events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <select
              value={filterSeverity}
              onChange={(e) => setFilterSeverity(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>
          
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="false_positive">False Positive</option>
            </select>
          </div>
        </div>
      </div>

      {/* Events List and Details */}
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Events List */}
        <div className="lg:col-span-2 bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="flex items-center space-x-2">
                <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />
                <span className="text-white">Loading security events...</span>
              </div>
            </div>
          ) : filteredEvents.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64">
              <Shield className="w-16 h-16 text-gray-600 mb-4" />
              <h3 className="text-lg font-medium text-white">No security events found</h3>
              <p className="text-gray-400 mt-2">Try adjusting your search or filters</p>
            </div>
          ) : (
            <div className="overflow-y-auto max-h-[calc(100vh-300px)]">
              {filteredEvents.map(event => (
                <div 
                  key={event.id} 
                  className={`p-4 border-b border-gray-700 hover:bg-gray-700/50 cursor-pointer transition-colors ${
                    selectedEvent?.id === event.id ? 'bg-gray-700/50' : ''
                  }`}
                  onClick={() => setSelectedEvent(event)}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${getSeverityColor(event.severity)}`}>
                      <AlertTriangle className="w-4 h-4" />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-white font-medium">{event.eventType.replace(/_/g, ' ')}</span>
                          <span className={`px-2 py-1 rounded-full text-xs border ${getSeverityColor(event.severity)}`}>
                            {event.severity}
                          </span>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs border ${getStatusColor(event.status)}`}>
                          {event.status.replace(/_/g, ' ')}
                        </span>
                      </div>
                      <p className="text-gray-300 text-sm mt-1">{event.description}</p>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <User className="w-3 h-3" />
                          <span>{event.user || 'System'}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <Clock className="w-3 h-3" />
                          <span>{event.timestamp.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Event Details */}
        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          {selectedEvent ? (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-white font-medium">Event Details</h3>
                  <span className={`px-2 py-1 rounded text-xs ${getSeverityColor(selectedEvent.severity)}`}>
                    {selectedEvent.severity.toUpperCase()}
                  </span>
                </div>
              </div>
              
              <div className="p-4 flex-1 overflow-y-auto">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-white font-medium mb-2">{selectedEvent.eventType.replace(/_/g, ' ')}</h4>
                    <p className="text-gray-300 text-sm">{selectedEvent.description}</p>
                  </div>
                  
                  <div className="bg-gray-700 rounded-lg p-4">
                    <h5 className="text-white font-medium mb-3">Event Information</h5>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Source</span>
                        <span className="text-white">{selectedEvent.source}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Timestamp</span>
                        <span className="text-white">{selectedEvent.timestamp.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Status</span>
                        <span className={selectedEvent.status === 'open' ? 'text-red-400' : 
                                        selectedEvent.status === 'in_progress' ? 'text-yellow-400' : 
                                        selectedEvent.status === 'resolved' ? 'text-green-400' : 
                                        'text-blue-400'}>
                          {selectedEvent.status.replace(/_/g, ' ')}
                        </span>
                      </div>
                      {selectedEvent.user && (
                        <div className="flex justify-between">
                          <span className="text-gray-400">User</span>
                          <span className="text-white">{selectedEvent.user}</span>
                        </div>
                      )}
                      {selectedEvent.ipAddress && (
                        <div className="flex justify-between">
                          <span className="text-gray-400">IP Address</span>
                          <span className="text-white">{selectedEvent.ipAddress}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {selectedEvent.status === 'resolved' || selectedEvent.status === 'false_positive' ? (
                    <div className="bg-gray-700 rounded-lg p-4">
                      <h5 className="text-white font-medium mb-3">Resolution</h5>
                      <div className="space-y-2">
                        <p className="text-gray-300 text-sm">{selectedEvent.resolution}</p>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Resolved By</span>
                          <span className="text-white">{selectedEvent.resolvedBy}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Resolved At</span>
                          <span className="text-white">{selectedEvent.resolvedAt?.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gray-700 rounded-lg p-4">
                      <h5 className="text-white font-medium mb-3">Recommended Actions</h5>
                      <div className="space-y-2">
                        {selectedEvent.severity === 'critical' && (
                          <div className="flex items-start space-x-2 text-sm">
                            <AlertTriangle className="w-4 h-4 text-red-400 mt-0.5" />
                            <span className="text-gray-300">Immediate investigation required</span>
                          </div>
                        )}
                        {selectedEvent.eventType === 'failed_login' && (
                          <div className="flex items-start space-x-2 text-sm">
                            <Info className="w-4 h-4 text-blue-400 mt-0.5" />
                            <span className="text-gray-300">Check for brute force attempts and consider locking the account</span>
                          </div>
                        )}
                        {selectedEvent.eventType === 'permission_change' && (
                          <div className="flex items-start space-x-2 text-sm">
                            <Info className="w-4 h-4 text-blue-400 mt-0.5" />
                            <span className="text-gray-300">Verify that permission changes were authorized</span>
                          </div>
                        )}
                        {selectedEvent.eventType === 'suspicious_access' && (
                          <div className="flex items-start space-x-2 text-sm">
                            <Info className="w-4 h-4 text-blue-400 mt-0.5" />
                            <span className="text-gray-300">Contact user to verify if access was legitimate</span>
                          </div>
                        )}
                        {selectedEvent.eventType === 'unauthorized_access' && (
                          <div className="flex items-start space-x-2 text-sm">
                            <Info className="w-4 h-4 text-blue-400 mt-0.5" />
                            <span className="text-gray-300">Review access logs and consider changing affected passwords</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="p-4 border-t border-gray-700">
                {selectedEvent.status === 'open' || selectedEvent.status === 'in_progress' ? (
                  <div className="flex space-x-3">
                    <button
                      onClick={() => {
                        setShowResolutionModal(true);
                      }}
                      className="flex-1 flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <CheckCircle2 className="w-4 h-4" />
                      <span>Resolve</span>
                    </button>
                    {selectedEvent.status === 'open' && (
                      <button
                        onClick={() => {
                          const updatedEvents = securityEvents.map(event => 
                            event.id === selectedEvent.id 
                              ? { ...event, status: 'in_progress' } 
                              : event
                          );
                          setSecurityEvents(updatedEvents);
                          setSelectedEvent({ ...selectedEvent, status: 'in_progress' });
                        }}
                        className="flex-1 flex items-center justify-center space-x-2 bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-colors"
                      >
                        <Clock className="w-4 h-4" />
                        <span>Mark In Progress</span>
                      </button>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-400">This event has been {selectedEvent.status.replace(/_/g, ' ')}</span>
                    <button
                      onClick={() => {
                        const updatedEvents = securityEvents.map(event => 
                          event.id === selectedEvent.id 
                            ? { ...event, status: 'open', resolution: undefined, resolvedBy: undefined, resolvedAt: undefined } 
                            : event
                        );
                        setSecurityEvents(updatedEvents);
                        setSelectedEvent({ 
                          ...selectedEvent, 
                          status: 'open', 
                          resolution: undefined, 
                          resolvedBy: undefined, 
                          resolvedAt: undefined 
                        });
                      }}
                      className="text-blue-400 hover:text-blue-300"
                    >
                      Reopen
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="h-full flex flex-col items-center justify-center p-6">
              <Shield className="w-16 h-16 text-gray-600 mb-4" />
              <h3 className="text-lg font-medium text-white">No Event Selected</h3>
              <p className="text-gray-400 mt-2 text-center">Select a security event from the list to view details</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderPoliciesTab = () => (
    <div className="h-full flex flex-col">
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Security Policies</h3>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => {
                // Create new policy
                const newPolicy: SecurityPolicy = {
                  id: (securityPolicies.length + 1).toString(),
                  name: 'New Policy',
                  type: 'access',
                  description: 'New security policy',
                  settings: {},
                  enabled: false,
                  lastModified: new Date(),
                  modifiedBy: 'Sarah Admin'
                };
                setEditingPolicy(newPolicy);
                setShowPolicyModal(true);
              }}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Create Policy</span>
            </button>
          </div>
        </div>
        
        <p className="text-gray-400 text-sm">
          Security policies define the rules and configurations that protect your system and data.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {securityPolicies.map(policy => (
          <div 
            key={policy.id} 
            className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden hover:border-blue-500 transition-colors cursor-pointer"
            onClick={() => setSelectedPolicy(policy)}
          >
            <div className="p-4 border-b border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded text-xs ${getPolicyTypeColor(policy.type)}`}>
                    {policy.type}
                  </span>
                  <h4 className="text-white font-medium">{policy.name}</h4>
                </div>
                <div className="flex items-center space-x-2">
                  {policy.enabled ? (
                    <span className="text-green-400 text-sm flex items-center space-x-1">
                      <CheckCircle2 className="w-4 h-4" />
                      <span>Enabled</span>
                    </span>
                  ) : (
                    <span className="text-gray-400 text-sm flex items-center space-x-1">
                      <XCircle className="w-4 h-4" />
                      <span>Disabled</span>
                    </span>
                  )}
                </div>
              </div>
              <p className="text-gray-400 text-sm">{policy.description}</p>
            </div>
            
            <div className="p-4">
              <div className="space-y-2">
                {policy.type === 'password' && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Min Length</span>
                      <span className="text-white">{policy.settings.minLength}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Expiry</span>
                      <span className="text-white">{policy.settings.expiryDays} days</span>
                    </div>
                  </>
                )}
                
                {policy.type === 'authentication' && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Admin MFA</span>
                      <span className="text-white">{policy.settings.adminRequired ? 'Required' : 'Optional'}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Methods</span>
                      <span className="text-white">{policy.settings.allowedMethods.join(', ')}</span>
                    </div>
                  </>
                )}
                
                {policy.type === 'access' && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Session Timeout</span>
                      <span className="text-white">{policy.settings.sessionTimeoutMinutes} minutes</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Max Sessions</span>
                      <span className="text-white">{policy.settings.maxConcurrentSessions}</span>
                    </div>
                  </>
                )}
                
                {policy.type === 'network' && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">IP Restriction</span>
                      <span className="text-white">{policy.settings.enableRestriction ? 'Enabled' : 'Disabled'}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Allowed Ranges</span>
                      <span className="text-white">{policy.settings.allowedRanges.length}</span>
                    </div>
                  </>
                )}
                
                {policy.type === 'data' && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Encryption</span>
                      <span className="text-white">{policy.settings.encryptionAlgorithm || 'AES-256'}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Key Rotation</span>
                      <span className="text-white">{policy.settings.rotateKeysInterval || 90} days</span>
                    </div>
                  </>
                )}
                
                <div className="flex justify-between text-xs text-gray-500 pt-2">
                  <span>Last modified by {policy.modifiedBy}</span>
                  <span>{policy.lastModified.toLocaleDateString()}</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-700/50 p-3 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditingPolicy({...policy});
                    setShowPolicyModal(true);
                  }}
                  className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    const updatedPolicies = securityPolicies.map(p => 
                      p.id === policy.id 
                        ? { ...p, enabled: !p.enabled, lastModified: new Date(), modifiedBy: 'Sarah Admin' } 
                        : p
                    );
                    setSecurityPolicies(updatedPolicies);
                  }}
                  className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                >
                  {policy.enabled ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  if (confirm('Are you sure you want to delete this policy?')) {
                    setSecurityPolicies(securityPolicies.filter(p => p.id !== policy.id));
                    if (selectedPolicy?.id === policy.id) {
                      setSelectedPolicy(null);
                    }
                  }
                }}
                className="p-1 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Policy Details Modal */}
      {selectedPolicy && (
        <div className="fixed inset-y-0 right-0 w-96 bg-gray-800 border-l border-gray-700 p-6 overflow-y-auto shadow-xl z-10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">Policy Details</h3>
            <button
              onClick={() => setSelectedPolicy(null)}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="space-y-6">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <span className={`px-2 py-1 rounded text-xs ${getPolicyTypeColor(selectedPolicy.type)}`}>
                  {selectedPolicy.type}
                </span>
                {selectedPolicy.enabled ? (
                  <span className="text-green-400 text-sm flex items-center space-x-1">
                    <CheckCircle2 className="w-4 h-4" />
                    <span>Enabled</span>
                  </span>
                ) : (
                  <span className="text-gray-400 text-sm flex items-center space-x-1">
                    <XCircle className="w-4 h-4" />
                    <span>Disabled</span>
                  </span>
                )}
              </div>
              <h4 className="text-white font-medium mb-2">{selectedPolicy.name}</h4>
              <p className="text-gray-300 text-sm">{selectedPolicy.description}</p>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">Policy Settings</h5>
              <div className="space-y-3">
                {selectedPolicy.type === 'password' && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Minimum Length</span>
                      <span className="text-white">{selectedPolicy.settings.minLength}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Require Uppercase</span>
                      <span className="text-white">{selectedPolicy.settings.requireUppercase ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Require Lowercase</span>
                      <span className="text-white">{selectedPolicy.settings.requireLowercase ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Require Numbers</span>
                      <span className="text-white">{selectedPolicy.settings.requireNumbers ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Require Special Characters</span>
                      <span className="text-white">{selectedPolicy.settings.requireSpecialChars ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Expiry (days)</span>
                      <span className="text-white">{selectedPolicy.settings.expiryDays}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Prevent Reuse (count)</span>
                      <span className="text-white">{selectedPolicy.settings.preventReuse}</span>
                    </div>
                  </>
                )}
                
                {selectedPolicy.type === 'authentication' && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Admin MFA Required</span>
                      <span className="text-white">{selectedPolicy.settings.adminRequired ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Engineer MFA Required</span>
                      <span className="text-white">{selectedPolicy.settings.engineerRequired ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Operator MFA Required</span>
                      <span className="text-white">{selectedPolicy.settings.operatorRequired ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Viewer MFA Required</span>
                      <span className="text-white">{selectedPolicy.settings.viewerRequired ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Allowed Methods</span>
                      <span className="text-white">{selectedPolicy.settings.allowedMethods.join(', ')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Grace Login Count</span>
                      <span className="text-white">{selectedPolicy.settings.graceLoginCount}</span>
                    </div>
                  </>
                )}
                
                {selectedPolicy.type === 'access' && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Session Timeout (minutes)</span>
                      <span className="text-white">{selectedPolicy.settings.sessionTimeoutMinutes}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Extend On Activity</span>
                      <span className="text-white">{selectedPolicy.settings.extendOnActivity ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Max Concurrent Sessions</span>
                      <span className="text-white">{selectedPolicy.settings.maxConcurrentSessions}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Force Logout On Password Change</span>
                      <span className="text-white">{selectedPolicy.settings.forceLogoutOnPasswordChange ? 'Yes' : 'No'}</span>
                    </div>
                  </>
                )}
                
                {selectedPolicy.type === 'network' && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-400">IP Restriction Enabled</span>
                      <span className="text-white">{selectedPolicy.settings.enableRestriction ? 'Yes' : 'No'}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Allowed IP Ranges</span>
                      <div className="mt-1 space-y-1">
                        {selectedPolicy.settings.allowedRanges.map((range: string, index: number) => (
                          <div key={index} className="bg-gray-800 rounded px-2 py-1 text-white text-sm">
                            {range}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-400">Blocked IP Ranges</span>
                      <div className="mt-1 space-y-1">
                        {selectedPolicy.settings.blockList.map((range: string, index: number) => (
                          <div key={index} className="bg-gray-800 rounded px-2 py-1 text-white text-sm">
                            {range}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Alert On Block</span>
                      <span className="text-white">{selectedPolicy.settings.alertOnBlock ? 'Yes' : 'No'}</span>
                    </div>
                  </>
                )}
                
                {selectedPolicy.type === 'data' && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Encrypt Projects</span>
                      <span className="text-white">{selectedPolicy.settings.encryptProjects ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Encrypt User Data</span>
                      <span className="text-white">{selectedPolicy.settings.encryptUserData ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Encryption Algorithm</span>
                      <span className="text-white">{selectedPolicy.settings.encryptionAlgorithm}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Key Rotation (days)</span>
                      <span className="text-white">{selectedPolicy.settings.rotateKeysInterval}</span>
                    </div>
                  </>
                )}
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">Policy Metadata</h5>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-400">Last Modified</span>
                  <span className="text-white">{selectedPolicy.lastModified.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Modified By</span>
                  <span className="text-white">{selectedPolicy.modifiedBy}</span>
                </div>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setEditingPolicy({...selectedPolicy});
                  setShowPolicyModal(true);
                }}
                className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>Edit Policy</span>
              </button>
              <button
                onClick={() => {
                  const updatedPolicies = securityPolicies.map(p => 
                    p.id === selectedPolicy.id 
                      ? { 
                          ...p, 
                          enabled: !p.enabled, 
                          lastModified: new Date(),
                          modifiedBy: 'Sarah Admin'
                        } 
                      : p
                  );
                  setSecurityPolicies(updatedPolicies);
                  setSelectedPolicy({
                    ...selectedPolicy,
                    enabled: !selectedPolicy.enabled,
                    lastModified: new Date(),
                    modifiedBy: 'Sarah Admin'
                  });
                }}
                className="flex-1 flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                {selectedPolicy.enabled ? (
                  <>
                    <EyeOff className="w-4 h-4" />
                    <span>Disable</span>
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4" />
                    <span>Enable</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderComplianceTab = () => (
    <div className="h-full flex flex-col">
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Compliance Reports</h3>
          <button
            onClick={() => setShowReportModal(true)}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <FileText className="w-4 h-4" />
            <span>Generate Report</span>
          </button>
        </div>
        
        <p className="text-gray-400 text-sm">
          Generate and view compliance reports for regulatory requirements and internal audits.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-700">
            <h4 className="text-white font-medium">Security Audit Report</h4>
          </div>
          <div className="p-4">
            <p className="text-gray-300 text-sm mb-4">
              Comprehensive security audit report including user access, permission changes, and security events.
            </p>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Last Generated</span>
                <span className="text-white">June 15, 2023</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Generated By</span>
                <span className="text-white">Sarah Admin</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Time Period</span>
                <span className="text-white">Q2 2023</span>
              </div>
            </div>
            <button
              onClick={() => {
                // Download report
              }}
              className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Download Report</span>
            </button>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-700">
            <h4 className="text-white font-medium">Safety Compliance Report</h4>
          </div>
          <div className="p-4">
            <p className="text-gray-300 text-sm mb-4">
              Safety compliance report for industrial automation systems, including safety function verification.
            </p>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Last Generated</span>
                <span className="text-white">May 30, 2023</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Generated By</span>
                <span className="text-white">Mike Safety</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Standards</span>
                <span className="text-white">IEC 61508, ISO 13849</span>
              </div>
            </div>
            <button
              onClick={() => {
                // Download report
              }}
              className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Download Report</span>
            </button>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-700">
            <h4 className="text-white font-medium">User Activity Report</h4>
          </div>
          <div className="p-4">
            <p className="text-gray-300 text-sm mb-4">
              Detailed user activity report showing logins, actions, and resource access patterns.
            </p>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Last Generated</span>
                <span className="text-white">June 10, 2023</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Generated By</span>
                <span className="text-white">David Manager</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Users Covered</span>
                <span className="text-white">All (42)</span>
              </div>
            </div>
            <button
              onClick={() => {
                // Download report
              }}
              className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Download Report</span>
            </button>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-700">
            <h4 className="text-white font-medium">Deployment Audit Report</h4>
          </div>
          <div className="p-4">
            <p className="text-gray-300 text-sm mb-4">
              Audit report of all deployments, including approvals, changes, and verification steps.
            </p>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Last Generated</span>
                <span className="text-white">June 5, 2023</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Generated By</span>
                <span className="text-white">Sarah Admin</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Deployments</span>
                <span className="text-white">124</span>
              </div>
            </div>
            <button
              onClick={() => {
                // Download report
              }}
              className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Download Report</span>
            </button>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-700">
            <h4 className="text-white font-medium">Data Protection Report</h4>
          </div>
          <div className="p-4">
            <p className="text-gray-300 text-sm mb-4">
              Report on data protection measures, encryption status, and privacy compliance.
            </p>
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Last Generated</span>
                <span className="text-white">May 20, 2023</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Generated By</span>
                <span className="text-white">Sarah Admin</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-400">Compliance</span>
                <span className="text-green-400">GDPR, CCPA</span>
              </div>
            </div>
            <button
              onClick={() => {
                // Download report
              }}
              className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Download Report</span>
            </button>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="p-4 border-b border-gray-700">
            <h4 className="text-white font-medium">Custom Report</h4>
          </div>
          <div className="p-4">
            <p className="text-gray-300 text-sm mb-4">
              Generate a custom compliance report with specific parameters and time ranges.
            </p>
            <button
              onClick={() => setShowReportModal(true)}
              className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Create Custom Report</span>
            </button>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-gray-800 rounded-lg p-4 border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Compliance Resources</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a 
            href="#" 
            className="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
          >
            <FileText className="w-5 h-5 text-blue-400" />
            <div>
              <h4 className="text-white font-medium">IEC 61508 Compliance Guide</h4>
              <p className="text-gray-400 text-sm">Guide for implementing functional safety standards</p>
            </div>
            <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
          </a>
          
          <a 
            href="#" 
            className="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
          >
            <FileText className="w-5 h-5 text-blue-400" />
            <div>
              <h4 className="text-white font-medium">ISO 13849 Safety Guide</h4>
              <p className="text-gray-400 text-sm">Implementation guide for machine safety</p>
            </div>
            <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
          </a>
          
          <a 
            href="#" 
            className="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
          >
            <FileText className="w-5 h-5 text-blue-400" />
            <div>
              <h4 className="text-white font-medium">GDPR Compliance Checklist</h4>
              <p className="text-gray-400 text-sm">Data protection compliance requirements</p>
            </div>
            <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
          </a>
          
          <a 
            href="#" 
            className="flex items-start space-x-3 p-3 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
          >
            <FileText className="w-5 h-5 text-blue-400" />
            <div>
              <h4 className="text-white font-medium">Security Best Practices</h4>
              <p className="text-gray-400 text-sm">Industrial automation security guidelines</p>
            </div>
            <ExternalLink className="w-4 h-4 text-gray-400 ml-auto" />
          </a>
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Tabs */}
      <div className="mb-6">
        <div className="flex border-b border-gray-700">
          <button
            className={`px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === 'events' 
                ? 'text-white border-b-2 border-blue-500' 
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('events')}
          >
            Security Events
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === 'policies' 
                ? 'text-white border-b-2 border-blue-500' 
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('policies')}
          >
            Security Policies
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === 'compliance' 
                ? 'text-white border-b-2 border-blue-500' 
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('compliance')}
          >
            Compliance
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'events' && renderEventsTab()}
        {activeTab === 'policies' && renderPoliciesTab()}
        {activeTab === 'compliance' && renderComplianceTab()}
      </div>

      {/* Resolution Modal */}
      {showResolutionModal && selectedEvent && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Resolve Security Event</h3>
              <button
                onClick={() => setShowResolutionModal(false)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="mb-4">
              <div className="bg-gray-700/50 rounded-lg p-3 mb-4">
                <div className="flex items-center space-x-2 mb-1">
                  <AlertTriangle className={`w-4 h-4 ${
                    selectedEvent.severity === 'critical' ? 'text-red-400' :
                    selectedEvent.severity === 'high' ? 'text-orange-400' :
                    selectedEvent.severity === 'medium' ? 'text-yellow-400' :
                    'text-blue-400'
                  }`} />
                  <span className="text-white font-medium">{selectedEvent.eventType.replace(/_/g, ' ')}</span>
                </div>
                <p className="text-gray-300 text-sm">{selectedEvent.description}</p>
              </div>
              
              <label className="block text-gray-300 mb-2">Resolution</label>
              <textarea
                value={resolution}
                onChange={(e) => setResolution(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-32 resize-none"
                placeholder="Describe how this security event was resolved..."
              ></textarea>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => {
                  const updatedEvents = securityEvents.map(event => 
                    event.id === selectedEvent.id 
                      ? { 
                          ...event, 
                          status: 'false_positive', 
                          resolution: resolution || 'Marked as false positive', 
                          resolvedBy: 'Sarah Admin', 
                          resolvedAt: new Date() 
                        } 
                      : event
                  );
                  
                  setSecurityEvents(updatedEvents);
                  setSelectedEvent({
                    ...selectedEvent,
                    status: 'false_positive',
                    resolution: resolution || 'Marked as false positive',
                    resolvedBy: 'Sarah Admin',
                    resolvedAt: new Date()
                  });
                  setShowResolutionModal(false);
                  setResolution('');
                }}
                className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Info className="w-4 h-4" />
                <span>False Positive</span>
              </button>
              <button
                onClick={handleResolveEvent}
                className="flex-1 flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
                disabled={!resolution}
              >
                <CheckCircle2 className="w-4 h-4" />
                <span>Resolve</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Policy Edit Modal */}
      {showPolicyModal && editingPolicy && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-lg p-6 border border-gray-700 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">
                {editingPolicy.id.includes('new') ? 'Create Policy' : 'Edit Policy'}
              </h3>
              <button
                onClick={() => {
                  setShowPolicyModal(false);
                  setEditingPolicy(null);
                }}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Policy Name</label>
                <input
                  type="text"
                  value={editingPolicy.name}
                  onChange={(e) => setEditingPolicy({...editingPolicy, name: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Policy Type</label>
                <select
                  value={editingPolicy.type}
                  onChange={(e) => setEditingPolicy({...editingPolicy, type: e.target.value as any})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="password">Password</option>
                  <option value="authentication">Authentication</option>
                  <option value="access">Access Control</option>
                  <option value="network">Network Security</option>
                  <option value="data">Data Protection</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Description</label>
                <textarea
                  value={editingPolicy.description}
                  onChange={(e) => setEditingPolicy({...editingPolicy, description: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-20 resize-none"
                ></textarea>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Policy Settings</label>
                <div className="bg-gray-700 border border-gray-600 rounded-lg p-4 space-y-4">
                  {editingPolicy.type === 'password' && (
                    <>
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Minimum Length</label>
                        <input
                          type="number"
                          min="8"
                          max="32"
                          value={editingPolicy.settings.minLength || 12}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              minLength: parseInt(e.target.value)
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="requireUppercase"
                          checked={editingPolicy.settings.requireUppercase || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              requireUppercase: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="requireUppercase" className="text-white">Require uppercase letters</label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="requireLowercase"
                          checked={editingPolicy.settings.requireLowercase || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              requireLowercase: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="requireLowercase" className="text-white">Require lowercase letters</label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="requireNumbers"
                          checked={editingPolicy.settings.requireNumbers || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              requireNumbers: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="requireNumbers" className="text-white">Require numbers</label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="requireSpecialChars"
                          checked={editingPolicy.settings.requireSpecialChars || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              requireSpecialChars: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="requireSpecialChars" className="text-white">Require special characters</label>
                      </div>
                      
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Password Expiry (days)</label>
                        <input
                          type="number"
                          min="0"
                          max="365"
                          value={editingPolicy.settings.expiryDays || 90}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              expiryDays: parseInt(e.target.value)
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <p className="text-xs text-gray-400 mt-1">Set to 0 for no expiration</p>
                      </div>
                      
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Prevent Password Reuse (count)</label>
                        <input
                          type="number"
                          min="0"
                          max="24"
                          value={editingPolicy.settings.preventReuse || 5}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              preventReuse: parseInt(e.target.value)
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </>
                  )}
                  
                  {editingPolicy.type === 'authentication' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="adminRequired"
                          checked={editingPolicy.settings.adminRequired || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              adminRequired: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="adminRequired" className="text-white">Require MFA for Admins</label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="engineerRequired"
                          checked={editingPolicy.settings.engineerRequired || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              engineerRequired: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="engineerRequired" className="text-white">Require MFA for Engineers</label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="operatorRequired"
                          checked={editingPolicy.settings.operatorRequired || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              operatorRequired: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="operatorRequired" className="text-white">Require MFA for Operators</label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="viewerRequired"
                          checked={editingPolicy.settings.viewerRequired || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              viewerRequired: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="viewerRequired" className="text-white">Require MFA for Viewers</label>
                      </div>
                      
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Allowed MFA Methods</label>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id="mfaApp"
                              checked={(editingPolicy.settings.allowedMethods || []).includes('app')}
                              onChange={(e) => {
                                const methods = [...(editingPolicy.settings.allowedMethods || [])];
                                if (e.target.checked) {
                                  if (!methods.includes('app')) methods.push('app');
                                } else {
                                  const index = methods.indexOf('app');
                                  if (index !== -1) methods.splice(index, 1);
                                }
                                setEditingPolicy({
                                  ...editingPolicy, 
                                  settings: {
                                    ...editingPolicy.settings,
                                    allowedMethods: methods
                                  }
                                });
                              }}
                              className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                            />
                            <label htmlFor="mfaApp" className="text-white">Authenticator App</label>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id="mfaEmail"
                              checked={(editingPolicy.settings.allowedMethods || []).includes('email')}
                              onChange={(e) => {
                                const methods = [...(editingPolicy.settings.allowedMethods || [])];
                                if (e.target.checked) {
                                  if (!methods.includes('email')) methods.push('email');
                                } else {
                                  const index = methods.indexOf('email');
                                  if (index !== -1) methods.splice(index, 1);
                                }
                                setEditingPolicy({
                                  ...editingPolicy, 
                                  settings: {
                                    ...editingPolicy.settings,
                                    allowedMethods: methods
                                  }
                                });
                              }}
                              className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                            />
                            <label htmlFor="mfaEmail" className="text-white">Email</label>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              id="mfaSms"
                              checked={(editingPolicy.settings.allowedMethods || []).includes('sms')}
                              onChange={(e) => {
                                const methods = [...(editingPolicy.settings.allowedMethods || [])];
                                if (e.target.checked) {
                                  if (!methods.includes('sms')) methods.push('sms');
                                } else {
                                  const index = methods.indexOf('sms');
                                  if (index !== -1) methods.splice(index, 1);
                                }
                                setEditingPolicy({
                                  ...editingPolicy, 
                                  settings: {
                                    ...editingPolicy.settings,
                                    allowedMethods: methods
                                  }
                                });
                              }}
                              className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                            />
                            <label htmlFor="mfaSms" className="text-white">SMS</label>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Grace Login Count</label>
                        <input
                          type="number"
                          min="0"
                          max="10"
                          value={editingPolicy.settings.graceLoginCount || 0}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              graceLoginCount: parseInt(e.target.value)
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                        <p className="text-xs text-gray-400 mt-1">Number of logins allowed without MFA after setup</p>
                      </div>
                    </>
                  )}
                  
                  {editingPolicy.type === 'access' && (
                    <>
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Session Timeout (minutes)</label>
                        <input
                          type="number"
                          min="5"
                          max="1440"
                          value={editingPolicy.settings.sessionTimeoutMinutes || 60}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              sessionTimeoutMinutes: parseInt(e.target.value)
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="extendOnActivity"
                          checked={editingPolicy.settings.extendOnActivity || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              extendOnActivity: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="extendOnActivity" className="text-white">Extend session on activity</label>
                      </div>
                      
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Max Concurrent Sessions</label>
                        <input
                          type="number"
                          min="1"
                          max="10"
                          value={editingPolicy.settings.maxConcurrentSessions || 3}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              maxConcurrentSessions: parseInt(e.target.value)
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="forceLogoutOnPasswordChange"
                          checked={editingPolicy.settings.forceLogoutOnPasswordChange || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              forceLogoutOnPasswordChange: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="forceLogoutOnPasswordChange" className="text-white">Force logout on password change</label>
                      </div>
                    </>
                  )}
                  
                  {editingPolicy.type === 'network' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="enableRestriction"
                          checked={editingPolicy.settings.enableRestriction || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              enableRestriction: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="enableRestriction" className="text-white">Enable IP restrictions</label>
                      </div>
                      
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Allowed IP Ranges (one per line)</label>
                        <textarea
                          value={(editingPolicy.settings.allowedRanges || []).join('\n')}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              allowedRanges: e.target.value.split('\n').filter(Boolean)
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none"
                          placeholder="***********/16&#10;10.0.0.0/8"
                        ></textarea>
                      </div>
                      
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Blocked IP Ranges (one per line)</label>
                        <textarea
                          value={(editingPolicy.settings.blockList || []).join('\n')}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              blockList: e.target.value.split('\n').filter(Boolean)
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none"
                          placeholder="***********/24"
                        ></textarea>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="alertOnBlock"
                          checked={editingPolicy.settings.alertOnBlock || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              alertOnBlock: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="alertOnBlock" className="text-white">Generate alert on blocked access attempts</label>
                      </div>
                    </>
                  )}
                  
                  {editingPolicy.type === 'data' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="encryptProjects"
                          checked={editingPolicy.settings.encryptProjects || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              encryptProjects: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="encryptProjects" className="text-white">Encrypt project data</label>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="encryptUserData"
                          checked={editingPolicy.settings.encryptUserData || false}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              encryptUserData: e.target.checked
                            }
                          })}
                          className="rounded bg-gray-600 border-gray-500 text-blue-500 focus:ring-blue-500"
                        />
                        <label htmlFor="encryptUserData" className="text-white">Encrypt user data</label>
                      </div>
                      
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Encryption Algorithm</label>
                        <select
                          value={editingPolicy.settings.encryptionAlgorithm || 'AES-256-GCM'}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              encryptionAlgorithm: e.target.value
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="AES-256-GCM">AES-256-GCM</option>
                          <option value="AES-256-CBC">AES-256-CBC</option>
                          <option value="ChaCha20-Poly1305">ChaCha20-Poly1305</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-gray-300 mb-2 text-sm">Key Rotation Interval (days)</label>
                        <input
                          type="number"
                          min="30"
                          max="365"
                          value={editingPolicy.settings.rotateKeysInterval || 90}
                          onChange={(e) => setEditingPolicy({
                            ...editingPolicy, 
                            settings: {
                              ...editingPolicy.settings,
                              rotateKeysInterval: parseInt(e.target.value)
                            }
                          })}
                          className="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-2 mt-4">
                <input
                  type="checkbox"
                  id="policyEnabled"
                  checked={editingPolicy.enabled}
                  onChange={(e) => setEditingPolicy({...editingPolicy, enabled: e.target.checked})}
                  className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                />
                <label htmlFor="policyEnabled" className="text-white">Enable policy</label>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowPolicyModal(false);
                  setEditingPolicy(null);
                }}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleSavePolicy}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                disabled={!editingPolicy.name}
              >
                Save Policy
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Report Modal */}
      {showReportModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Generate Report</h3>
              <button
                onClick={() => setShowReportModal(false)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Report Type</label>
                <select
                  value={reportType}
                  onChange={(e) => setReportType(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="security_events">Security Events</option>
                  <option value="user_activity">User Activity</option>
                  <option value="policy_changes">Policy Changes</option>
                  <option value="access_control">Access Control</option>
                  <option value="compliance">Compliance Summary</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Time Range</label>
                <select
                  value={reportTimeframe}
                  onChange={(e) => setReportTimeframe(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="7d">Last 7 days</option>
                  <option value="30d">Last 30 days</option>
                  <option value="90d">Last 90 days</option>
                  <option value="1y">Last year</option>
                  <option value="custom">Custom range</option>
                </select>
              </div>
              
              {reportTimeframe === 'custom' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-300 mb-2">Start Date</label>
                    <input
                      type="date"
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-gray-300 mb-2">End Date</label>
                    <input
                      type="date"
                      className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              )}
              
              <div>
                <label className="block text-gray-300 mb-2">Format</label>
                <div className="grid grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="formatPdf"
                      name="reportFormat"
                      value="pdf"
                      defaultChecked
                      className="text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="formatPdf" className="text-white">PDF</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="formatCsv"
                      name="reportFormat"
                      value="csv"
                      className="text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="formatCsv" className="text-white">CSV</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="formatJson"
                      name="reportFormat"
                      value="json"
                      className="text-blue-500 focus:ring-blue-500"
                    />
                    <label htmlFor="formatJson" className="text-white">JSON</label>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="includeResolved"
                  defaultChecked
                  className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                />
                <label htmlFor="includeResolved" className="text-white">Include resolved events</label>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowReportModal(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleGenerateReport}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Generate Report
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityCenter;