import React, { useState, useEffect } from 'react';
import { usePLCStore } from '../../store/plcStore';
import { PLCTag } from '../../types/plc';
import { Play, Square, RotateCcw, Zap, CheckCircle2, AlertTriangle, Clock, Activity, Cpu, MemoryStick as Memory, Database, HardDrive } from 'lucide-react';

const PLCSimulator: React.FC = () => {
  const { 
    currentProject, 
    simulationMode, 
    toggleSimulation, 
    updateTagValue 
  } = usePLCStore();
  
  const [scanCycle, setScanCycle] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [cpuLoad, setCpuLoad] = useState(5);
  const [memoryUsage, setMemoryUsage] = useState(25);
  const [scanTime, setScanTime] = useState(2.3);
  const [simulationSpeed, setSimulationSpeed] = useState(1);
  const [showLogicMonitor, setShowLogicMonitor] = useState(false);
  const [logicSteps, setLogicSteps] = useState<string[]>([]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (simulationMode && isRunning) {
      interval = setInterval(() => {
        setScanCycle(prev => prev + 1);
        simulateLogic();
        
        // Simulate varying system metrics
        setCpuLoad(prev => Math.max(3, Math.min(30, prev + (Math.random() - 0.5) * 5)));
        setMemoryUsage(prev => Math.max(20, Math.min(40, prev + (Math.random() - 0.5) * 3)));
        setScanTime(prev => Math.max(1.5, Math.min(3.5, prev + (Math.random() - 0.5) * 0.2)));
      }, 100 / simulationSpeed); // Adjust for simulation speed
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [simulationMode, isRunning, simulationSpeed]);

  const simulateLogic = () => {
    if (!currentProject) return;

    // Clear previous logic steps
    const steps: string[] = [];

    // Simple ladder logic simulation
    const tags = currentProject.globalTags;
    const startButton = tags.find(t => t.name === 'Start_Button');
    const emergencyStop = tags.find(t => t.name === 'Emergency_Stop');
    const pumpRun = tags.find(t => t.name === 'Pump_Run');
    const motorOutput = tags.find(t => t.name === 'Motor_Output');

    if (startButton && emergencyStop && pumpRun && motorOutput) {
      // Rung 0: Start_Button AND /Emergency_Stop -> Pump_Run
      steps.push(`Evaluating: Start_Button(${startButton.value}) AND NOT Emergency_Stop(${!emergencyStop.value})`);
      
      const shouldRun = startButton.value && emergencyStop.value;
      steps.push(`Result: ${shouldRun ? 'TRUE' : 'FALSE'}`);
      
      if (shouldRun && !pumpRun.value) {
        updateTagValue(pumpRun.id, true);
        steps.push(`Action: Set Pump_Run to TRUE`);
      } else if (!emergencyStop.value) {
        updateTagValue(pumpRun.id, false);
        steps.push(`Action: Set Pump_Run to FALSE (Emergency Stop active)`);
      }

      // Rung 1: Pump_Run -> Motor_Output
      steps.push(`Evaluating: Pump_Run(${pumpRun.value}) -> Motor_Output`);
      if (pumpRun.value !== motorOutput.value) {
        updateTagValue(motorOutput.id, pumpRun.value);
        steps.push(`Action: Set Motor_Output to ${pumpRun.value}`);
      } else {
        steps.push(`No change to Motor_Output (already ${motorOutput.value})`);
      }
    }

    // Update logic steps
    setLogicSteps(steps);
  };

  const handleTagValueChange = (tag: PLCTag, newValue: any) => {
    let parsedValue = newValue;
    
    switch (tag.type) {
      case 'BOOL':
        parsedValue = newValue === 'true' || newValue === true;
        break;
      case 'INT':
      case 'DINT':
        parsedValue = parseInt(newValue) || 0;
        break;
      case 'REAL':
        parsedValue = parseFloat(newValue) || 0.0;
        break;
      default:
        parsedValue = newValue;
    }
    
    updateTagValue(tag.id, parsedValue);
  };

  const renderTagValue = (tag: PLCTag) => {
    switch (tag.type) {
      case 'BOOL':
        return (
          <select
            value={tag.value.toString()}
            onChange={(e) => handleTagValueChange(tag, e.target.value)}
            className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-sm text-white"
            disabled={!simulationMode || tag.scope === 'OUTPUT'}
          >
            <option value="false">FALSE</option>
            <option value="true">TRUE</option>
          </select>
        );
      case 'INT':
      case 'DINT':
        return (
          <input
            type="number"
            value={tag.value}
            onChange={(e) => handleTagValueChange(tag, e.target.value)}
            className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-sm text-white w-20"
            disabled={!simulationMode || tag.scope === 'OUTPUT'}
          />
        );
      case 'REAL':
        return (
          <input
            type="number"
            step="0.1"
            value={tag.value}
            onChange={(e) => handleTagValueChange(tag, e.target.value)}
            className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-sm text-white w-20"
            disabled={!simulationMode || tag.scope === 'OUTPUT'}
          />
        );
      default:
        return (
          <input
            type="text"
            value={tag.value}
            onChange={(e) => handleTagValueChange(tag, e.target.value)}
            className="bg-gray-800 border border-gray-700 rounded px-2 py-1 text-sm text-white w-24"
            disabled={!simulationMode || tag.scope === 'OUTPUT'}
          />
        );
    }
  };

  const getTagStatusColor = (tag: PLCTag) => {
    if (tag.type === 'BOOL') {
      return tag.value ? 'text-green-400' : 'text-gray-400';
    }
    return 'text-blue-400';
  };

  const handleStartSimulation = () => {
    if (!simulationMode) {
      toggleSimulation();
    }
    setIsRunning(true);
  };

  const handleStopSimulation = () => {
    setIsRunning(false);
  };

  const handleResetSimulation = () => {
    // Reset all tag values to defaults
    if (currentProject) {
      currentProject.globalTags.forEach(tag => {
        let defaultValue;
        switch (tag.type) {
          case 'BOOL':
            defaultValue = false;
            break;
          case 'INT':
          case 'DINT':
            defaultValue = 0;
            break;
          case 'REAL':
            defaultValue = 0.0;
            break;
          case 'STRING':
            defaultValue = '';
            break;
          default:
            defaultValue = null;
        }
        updateTagValue(tag.id, defaultValue);
      });
    }
    
    // Reset simulation counters
    setScanCycle(0);
    setCpuLoad(5);
    setMemoryUsage(25);
    setScanTime(2.3);
  };

  if (!currentProject) {
    return (
      <div className="h-full flex items-center justify-center text-gray-400">
        <div className="text-center">
          <Zap className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No project loaded</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Simulation Controls */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-lg font-semibold text-white">PLC Simulator</h3>
            <div className={`px-3 py-1 rounded-full text-sm ${
              simulationMode 
                ? 'bg-green-600/20 text-green-400 border border-green-600/30' 
                : 'bg-gray-600/20 text-gray-400 border border-gray-600/30'
            }`}>
              {simulationMode ? 'SIMULATION' : 'OFFLINE'}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {!simulationMode ? (
              <button
                onClick={toggleSimulation}
                className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Start Simulation</span>
              </button>
            ) : !isRunning ? (
              <button
                onClick={handleStartSimulation}
                className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors"
              >
                <Play className="w-4 h-4" />
                <span>Run</span>
              </button>
            ) : (
              <button
                onClick={handleStopSimulation}
                className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
              >
                <Square className="w-4 h-4" />
                <span>Stop</span>
              </button>
            )}
            
            <button
              onClick={handleResetSimulation}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Reset Simulation"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
            
            <button
              onClick={() => setShowLogicMonitor(!showLogicMonitor)}
              className={`p-2 rounded transition-colors ${
                showLogicMonitor ? 'bg-blue-600/20 text-blue-400' : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
              title="Logic Monitor"
            >
              <Activity className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {simulationMode && (
          <div className="mt-4 flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">Scan Cycle:</span>
              <span className="text-white font-mono">{scanCycle}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">Status:</span>
              <span className={`font-semibold ${isRunning ? 'text-green-400' : 'text-amber-400'}`}>
                {isRunning ? 'RUNNING' : 'PAUSED'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">Speed:</span>
              <select
                value={simulationSpeed}
                onChange={(e) => setSimulationSpeed(Number(e.target.value))}
                className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs"
              >
                <option value={0.5}>0.5x</option>
                <option value={1}>1x</option>
                <option value={2}>2x</option>
                <option value={5}>5x</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* System Metrics */}
      {simulationMode && (
        <div className="bg-gray-800/50 border-b border-gray-700 p-4">
          <div className="grid grid-cols-4 gap-4">
            <div className="bg-gray-800 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Cpu className="w-4 h-4 text-blue-400" />
                  <span className="text-white text-sm">CPU Load</span>
                </div>
                <span className="text-white text-sm font-mono">{cpuLoad.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${cpuLoad}%` }}
                />
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Memory className="w-4 h-4 text-purple-400" />
                  <span className="text-white text-sm">Memory</span>
                </div>
                <span className="text-white text-sm font-mono">{memoryUsage.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${memoryUsage}%` }}
                />
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-green-400" />
                  <span className="text-white text-sm">Scan Time</span>
                </div>
                <span className="text-white text-sm font-mono">{scanTime.toFixed(1)} ms</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(scanTime / 10) * 100}%` }}
                />
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Activity className="w-4 h-4 text-yellow-400" />
                  <span className="text-white text-sm">Status</span>
                </div>
                <span className="text-green-400 text-sm font-mono">HEALTHY</span>
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <CheckCircle2 className="w-3 h-3 text-green-400" />
                <span className="text-xs text-gray-400">All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-4">
        <div className="grid grid-cols-2 gap-6 h-full">
          {/* Tag Monitor */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold text-white mb-3">Global Tags</h4>
            <div className="space-y-2 overflow-y-auto max-h-[calc(100vh-300px)]">
              {currentProject.globalTags.map(tag => (
                <div key={tag.id} className="bg-gray-800/50 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <span className="text-white font-medium">{tag.name}</span>
                      <span className={`text-xs px-2 py-1 rounded ${
                        tag.scope === 'INPUT' ? 'bg-blue-600/20 text-blue-400' :
                        tag.scope === 'OUTPUT' ? 'bg-green-600/20 text-green-400' :
                        'bg-gray-600/20 text-gray-400'
                      }`}>
                        {tag.scope}
                      </span>
                      <span className="text-xs text-gray-400">{tag.type}</span>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <span className={`font-mono text-sm ${getTagStatusColor(tag)}`}>
                        {tag.type === 'BOOL' ? (tag.value ? 'TRUE' : 'FALSE') : tag.value.toString()}
                      </span>
                      {renderTagValue(tag)}
                    </div>
                  </div>
                  
                  {tag.description && (
                    <div className="text-xs text-gray-400">{tag.description}</div>
                  )}
                  
                  {tag.lastUpdated && simulationMode && (
                    <div className="text-xs text-gray-500 mt-1">
                      Updated: {tag.lastUpdated.toLocaleTimeString()}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* Logic Monitor or System Info */}
          <div>
            {showLogicMonitor ? (
              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-white mb-3">Logic Monitor</h4>
                <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-white">Execution Steps</span>
                    <span className="text-xs text-gray-400">Scan Cycle: {scanCycle}</span>
                  </div>
                  <div className="bg-gray-900 rounded-lg p-3 max-h-[calc(100vh-350px)] overflow-y-auto font-mono text-sm">
                    {logicSteps.length > 0 ? (
                      <div className="space-y-2">
                        {logicSteps.map((step, index) => (
                          <div key={index} className="text-gray-300">
                            {step.includes('Evaluating') ? (
                              <div className="text-blue-400">{step}</div>
                            ) : step.includes('Result') ? (
                              <div className={step.includes('TRUE') ? 'text-green-400' : 'text-red-400'}>
                                {step}
                              </div>
                            ) : step.includes('Action') ? (
                              <div className="text-yellow-400">{step}</div>
                            ) : (
                              <div>{step}</div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-gray-500 text-center py-4">
                        No logic execution data available.
                        {!isRunning && simulationMode && (
                          <div className="mt-2">
                            <button 
                              onClick={handleStartSimulation}
                              className="text-blue-400 hover:text-blue-300"
                            >
                              Start simulation to see logic execution
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <h5 className="text-white text-sm font-semibold mb-3">Active Rungs</h5>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between bg-gray-700/50 p-2 rounded">
                      <span className="text-white text-sm">Rung 0: Start/Stop Logic</span>
                      <span className="text-green-400 text-xs">ACTIVE</span>
                    </div>
                    <div className="flex items-center justify-between bg-gray-700/50 p-2 rounded">
                      <span className="text-white text-sm">Rung 1: Motor Control</span>
                      <span className="text-green-400 text-xs">ACTIVE</span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-white mb-3">System Information</h4>
                <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <h5 className="text-white text-sm font-semibold mb-3">PLC Configuration</h5>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-gray-400 text-xs">Model:</span>
                      <div className="text-white text-sm">Siemens S7-1500</div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-xs">Firmware:</span>
                      <div className="text-white text-sm">V2.9.2</div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-xs">IP Address:</span>
                      <div className="text-white text-sm">*************</div>
                    </div>
                    <div>
                      <span className="text-gray-400 text-xs">Status:</span>
                      <div className="text-green-400 text-sm">RUNNING</div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <h5 className="text-white text-sm font-semibold mb-3">Memory Usage</h5>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Program Memory</span>
                        <span className="text-white">128 KB / 2 MB</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full" style={{ width: '6.4%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Data Memory</span>
                        <span className="text-white">256 KB / 1 MB</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div className="bg-purple-500 h-2 rounded-full" style={{ width: '25.6%' }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-400">Retain Memory</span>
                        <span className="text-white">32 KB / 128 KB</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '25%' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                  <h5 className="text-white text-sm font-semibold mb-3">I/O Configuration</h5>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <Database className="w-5 h-5 text-blue-400" />
                      <div>
                        <div className="text-white text-sm">Digital Inputs</div>
                        <div className="text-gray-400 text-xs">16 points configured</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Database className="w-5 h-5 text-green-400" />
                      <div>
                        <div className="text-white text-sm">Digital Outputs</div>
                        <div className="text-gray-400 text-xs">16 points configured</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Database className="w-5 h-5 text-yellow-400" />
                      <div>
                        <div className="text-white text-sm">Analog Inputs</div>
                        <div className="text-gray-400 text-xs">4 points configured</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Database className="w-5 h-5 text-purple-400" />
                      <div>
                        <div className="text-white text-sm">Analog Outputs</div>
                        <div className="text-gray-400 text-xs">2 points configured</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Status Bar */}
      <div className="bg-gray-800 border-t border-gray-700 p-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span className="text-gray-400">Mode:</span>
            <span className={simulationMode ? 'text-green-400' : 'text-gray-400'}>
              {simulationMode ? 'SIMULATION' : 'OFFLINE'}
            </span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-400">Scan Time:</span>
            <span className="text-white">{scanTime.toFixed(1)} ms</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-400">Tags:</span>
            <span className="text-white">{currentProject.globalTags.length} total</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {simulationMode && isRunning ? (
              <CheckCircle2 className="w-4 h-4 text-green-400" />
            ) : simulationMode ? (
              <AlertTriangle className="w-4 h-4 text-yellow-400" />
            ) : (
              <AlertTriangle className="w-4 h-4 text-red-400" />
            )}
            <span className={simulationMode && isRunning ? 'text-green-400' : simulationMode ? 'text-yellow-400' : 'text-red-400'}>
              {simulationMode && isRunning ? 'RUNNING' : simulationMode ? 'PAUSED' : 'STOPPED'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PLCSimulator;