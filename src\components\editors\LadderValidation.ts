// Modular Ladder Logic Validation System

export interface ValidationError {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  rungId?: string;
  elementId?: string;
  position?: { x: number; y: number };
  suggestion?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  info: ValidationError[];
}

export class LadderValidator {
  validateProgram(rungs: any[]): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const info: ValidationError[] = [];

    rungs.forEach(rung => {
      const rungValidation = this.validateRung(rung);
      errors.push(...rungValidation.errors);
      warnings.push(...rungValidation.warnings);
      info.push(...rungValidation.info);
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      info
    };
  }

  private validateRung(rung: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const info: ValidationError[] = [];

    // Check for empty rungs
    if (!rung.elements || rung.elements.length === 0) {
      warnings.push({
        id: `empty-rung-${rung.id}`,
        type: 'warning',
        message: `Rung ${rung.number} is empty`,
        rungId: rung.id,
        suggestion: 'Add logic elements to this rung or remove it'
      });
      return { isValid: true, errors, warnings, info };
    }

    // Check for unassigned tags
    rung.elements.forEach((element: any) => {
      if (!element.tag || element.tag.trim() === '') {
        errors.push({
          id: `unassigned-tag-${element.id}`,
          type: 'error',
          message: `Element at position (${element.position.x}, ${element.position.y}) has no tag assigned`,
          rungId: rung.id,
          elementId: element.id,
          position: element.position,
          suggestion: 'Assign a tag name to this element'
        });
      }
    });

    // Check for proper power flow
    const powerFlowValidation = this.validatePowerFlow(rung);
    errors.push(...powerFlowValidation.errors);
    warnings.push(...powerFlowValidation.warnings);

    // Check for safety logic compliance
    if (rung.safetyRung) {
      const safetyValidation = this.validateSafetyRung(rung);
      errors.push(...safetyValidation.errors);
      warnings.push(...safetyValidation.warnings);
    }

    // Check for best practices
    const bestPracticesValidation = this.validateBestPractices(rung);
    warnings.push(...bestPracticesValidation.warnings);
    info.push(...bestPracticesValidation.info);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      info
    };
  }

  private validatePowerFlow(rung: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const info: ValidationError[] = [];

    // Check if there's at least one output element
    const outputs = rung.elements.filter((e: any) => e.type === 'coil');
    if (outputs.length === 0) {
      warnings.push({
        id: `no-output-${rung.id}`,
        type: 'warning',
        message: `Rung ${rung.number} has no output elements`,
        rungId: rung.id,
        suggestion: 'Add a coil or other output element'
      });
    }

    // Check for disconnected elements
    const connectedElements = this.findConnectedElements(rung.elements);
    rung.elements.forEach((element: any) => {
      if (!connectedElements.has(element.id)) {
        errors.push({
          id: `disconnected-${element.id}`,
          type: 'error',
          message: `Element "${element.tag || 'Untagged'}" is not connected to power flow`,
          rungId: rung.id,
          elementId: element.id,
          position: element.position,
          suggestion: 'Connect this element to the power rail or other elements'
        });
      }
    });

    return { isValid: errors.length === 0, errors, warnings, info };
  }

  private findConnectedElements(elements: any[]): Set<string> {
    const connected = new Set<string>();
    const visited = new Set<string>();

    // Find leftmost elements (connected to left power rail)
    const leftmostX = Math.min(...elements.map(e => e.position.x));
    const startElements = elements.filter(e => e.position.x === leftmostX);

    // Traverse from each start element
    startElements.forEach(element => {
      this.traverseConnections(element, elements, connected, visited);
    });

    return connected;
  }

  private traverseConnections(element: any, allElements: any[], connected: Set<string>, visited: Set<string>): void {
    if (visited.has(element.id)) return;
    
    visited.add(element.id);
    connected.add(element.id);

    // Follow connections
    element.connections.forEach((connId: string) => {
      const connectedElement = allElements.find(e => e.id === connId);
      if (connectedElement) {
        this.traverseConnections(connectedElement, allElements, connected, visited);
      }
    });

    // Also check for elements at adjacent positions
    const adjacentElements = allElements.filter(e => 
      Math.abs(e.position.x - element.position.x) === 1 && 
      e.position.y === element.position.y
    );
    
    adjacentElements.forEach(adjElement => {
      this.traverseConnections(adjElement, allElements, connected, visited);
    });
  }

  private validateSafetyRung(rung: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const info: ValidationError[] = [];

    // Check for safety-rated elements
    const safetyElements = rung.elements.filter((e: any) => e.safetyElement);
    if (safetyElements.length === 0) {
      warnings.push({
        id: `no-safety-elements-${rung.id}`,
        type: 'warning',
        message: `Safety rung ${rung.number} contains no safety-rated elements`,
        rungId: rung.id,
        suggestion: 'Use safety-rated function blocks for safety applications'
      });
    }

    // Check for proper emergency stop implementation
    const emergencyStops = rung.elements.filter((e: any) => 
      e.tag && e.tag.toLowerCase().includes('emergency') || e.tag.toLowerCase().includes('estop')
    );
    
    emergencyStops.forEach(estop => {
      if (estop.properties?.normally !== 'closed') {
        errors.push({
          id: `estop-config-${estop.id}`,
          type: 'error',
          message: `Emergency stop "${estop.tag}" should be normally closed`,
          rungId: rung.id,
          elementId: estop.id,
          suggestion: 'Configure emergency stop as normally closed contact'
        });
      }
    });

    return { isValid: errors.length === 0, errors, warnings, info };
  }

  private validateBestPractices(rung: any): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];
    const info: ValidationError[] = [];

    // Check for seal-in circuits
    const coils = rung.elements.filter((e: any) => e.type === 'coil');
    const contacts = rung.elements.filter((e: any) => e.type === 'contact');
    
    coils.forEach(coil => {
      const sealInContact = contacts.find(contact => contact.tag === coil.tag);
      if (!sealInContact && coil.tag && !coil.tag.includes('_PULSE')) {
        info.push({
          id: `missing-sealin-${coil.id}`,
          type: 'info',
          message: `Consider adding seal-in contact for "${coil.tag}"`,
          rungId: rung.id,
          elementId: coil.id,
          suggestion: 'Add auxiliary contact for maintained operation'
        });
      }
    });

    // Check for proper naming conventions
    rung.elements.forEach(element => {
      if (element.tag && !this.isValidTagName(element.tag)) {
        warnings.push({
          id: `naming-convention-${element.id}`,
          type: 'warning',
          message: `Tag "${element.tag}" doesn't follow naming conventions`,
          rungId: rung.id,
          elementId: element.id,
          suggestion: 'Use descriptive names with underscores (e.g., Motor_Start_PB)'
        });
      }
    });

    return { isValid: true, errors, warnings, info };
  }

  private isValidTagName(tagName: string): boolean {
    // Check for proper naming convention
    const validPattern = /^[A-Za-z][A-Za-z0-9_]*$/;
    return validPattern.test(tagName) && tagName.length <= 32;
  }

  // Utility method to get validation summary
  getValidationSummary(result: ValidationResult): string {
    const { errors, warnings, info } = result;
    const parts = [];
    
    if (errors.length > 0) {
      parts.push(`${errors.length} error${errors.length !== 1 ? 's' : ''}`);
    }
    if (warnings.length > 0) {
      parts.push(`${warnings.length} warning${warnings.length !== 1 ? 's' : ''}`);
    }
    if (info.length > 0) {
      parts.push(`${info.length} suggestion${info.length !== 1 ? 's' : ''}`);
    }
    
    return parts.length > 0 ? parts.join(', ') : 'No issues found';
  }
}

export const ladderValidator = new LadderValidator();