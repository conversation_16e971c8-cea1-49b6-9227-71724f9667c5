import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Download, 
  Upload, 
  Tag, 
  Code, 
  Grid3X3, 
  Workflow, 
  Shield, 
  X, 
  Save,
  RefreshCw,
  Check,
  AlertTriangle,
  Eye,
  EyeOff
} from 'lucide-react';

interface TemplateData {
  id: string;
  name: string;
  category: string;
  description: string;
  type: 'ladder' | 'st' | 'fbd' | 'sfc' | 'safety';
  tags: string[];
  rungCount: number;
  createdBy: string;
  createdAt: Date;
  lastModified: Date;
  isPublic: boolean;
  isVerified: boolean;
}

const TemplateManager: React.FC = () => {
  const [templates, setTemplates] = useState<TemplateData[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<TemplateData[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateData | null>(null);
  const [showAddTemplateModal, setShowAddTemplateModal] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    category: 'Motor Control',
    description: '',
    type: 'ladder' as const,
    tags: '',
    isPublic: true
  });

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const mockTemplates: TemplateData[] = [
        {
          id: '1',
          name: 'Motor Start/Stop with Safety',
          category: 'Motor Control',
          description: 'Standard motor control with start/stop buttons and emergency stop',
          type: 'ladder',
          tags: ['motor', 'safety', 'start-stop'],
          rungCount: 12,
          createdBy: 'John Engineer',
          createdAt: new Date(2023, 5, 15),
          lastModified: new Date(2023, 8, 22),
          isPublic: true,
          isVerified: true
        },
        {
          id: '2',
          name: 'Light Curtain Safety Logic',
          category: 'Safety',
          description: 'Safety logic for light curtain with muting capability',
          type: 'safety',
          tags: ['safety', 'light-curtain', 'muting'],
          rungCount: 15,
          createdBy: 'Mike Safety',
          createdAt: new Date(2023, 3, 10),
          lastModified: new Date(2023, 7, 5),
          isPublic: true,
          isVerified: true
        },
        {
          id: '3',
          name: 'PID Temperature Control',
          category: 'Process Control',
          description: 'PID controller for temperature regulation with auto-tuning',
          type: 'st',
          tags: ['pid', 'temperature', 'control'],
          rungCount: 0,
          createdBy: 'Sarah Admin',
          createdAt: new Date(2023, 2, 18),
          lastModified: new Date(2023, 6, 30),
          isPublic: true,
          isVerified: true
        },
        {
          id: '4',
          name: 'Conveyor Sequence Control',
          category: 'Sequence Control',
          description: 'Sequential control for multi-stage conveyor system',
          type: 'sfc',
          tags: ['conveyor', 'sequence', 'automation'],
          rungCount: 0,
          createdBy: 'John Engineer',
          createdAt: new Date(2023, 4, 5),
          lastModified: new Date(2023, 9, 12),
          isPublic: true,
          isVerified: false
        },
        {
          id: '5',
          name: 'Pump Station Control',
          category: 'Process Control',
          description: 'Complete pump station control with level monitoring',
          type: 'ladder',
          tags: ['pump', 'level', 'control'],
          rungCount: 18,
          createdBy: 'Lisa Operator',
          createdAt: new Date(2023, 7, 20),
          lastModified: new Date(2023, 10, 15),
          isPublic: false,
          isVerified: false
        },
        {
          id: '6',
          name: 'Two-Hand Safety Control',
          category: 'Safety',
          description: 'Two-hand control safety circuit with timing requirements',
          type: 'safety',
          tags: ['safety', 'two-hand', 'press'],
          rungCount: 10,
          createdBy: 'Mike Safety',
          createdAt: new Date(2023, 6, 8),
          lastModified: new Date(2023, 8, 14),
          isPublic: true,
          isVerified: true
        },
        {
          id: '7',
          name: 'Batch Process Control',
          category: 'Process Control',
          description: 'Batch processing with recipe management',
          type: 'fbd',
          tags: ['batch', 'recipe', 'process'],
          rungCount: 0,
          createdBy: 'David Manager',
          createdAt: new Date(2023, 9, 3),
          lastModified: new Date(2023, 11, 7),
          isPublic: true,
          isVerified: false
        }
      ];
      
      setTemplates(mockTemplates);
      setFilteredTemplates(mockTemplates);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter templates based on search and filters
  useEffect(() => {
    let result = templates;
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(template => 
        template.name.toLowerCase().includes(query) || 
        template.description.toLowerCase().includes(query) ||
        template.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    if (filterCategory !== 'all') {
      result = result.filter(template => template.category === filterCategory);
    }
    
    if (filterType !== 'all') {
      result = result.filter(template => template.type === filterType);
    }
    
    setFilteredTemplates(result);
  }, [templates, searchQuery, filterCategory, filterType]);

  const handleAddTemplate = () => {
    if (!newTemplate.name || !newTemplate.description) return;
    
    const template: TemplateData = {
      id: (templates.length + 1).toString(),
      name: newTemplate.name,
      category: newTemplate.category,
      description: newTemplate.description,
      type: newTemplate.type,
      tags: newTemplate.tags.split(',').map(tag => tag.trim()),
      rungCount: newTemplate.type === 'ladder' || newTemplate.type === 'safety' ? 10 : 0,
      createdBy: 'Sarah Admin',
      createdAt: new Date(),
      lastModified: new Date(),
      isPublic: newTemplate.isPublic,
      isVerified: false
    };
    
    setTemplates([...templates, template]);
    setShowAddTemplateModal(false);
    setNewTemplate({
      name: '',
      category: 'Motor Control',
      description: '',
      type: 'ladder',
      tags: '',
      isPublic: true
    });
  };

  const handleDeleteTemplate = () => {
    if (!selectedTemplate) return;
    
    setTemplates(templates.filter(template => template.id !== selectedTemplate.id));
    setSelectedTemplate(null);
    setShowDeleteConfirmation(false);
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'ladder':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'st':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'fbd':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'sfc':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'safety':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'ladder':
        return <Grid3X3 className="w-4 h-4" />;
      case 'st':
        return <Code className="w-4 h-4" />;
      case 'fbd':
        return <Workflow className="w-4 h-4" />;
      case 'sfc':
        return <Workflow className="w-4 h-4" />;
      case 'safety':
        return <Shield className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const getCategoryBadgeColor = (category: string) => {
    switch (category) {
      case 'Motor Control':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'Safety':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'Process Control':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'Sequence Control':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'Communication':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with search and filters */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Template Library</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => {
                // Import template functionality
              }}
              className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Upload className="w-4 h-4" />
              <span>Import</span>
            </button>
            <button
              onClick={() => setShowAddTemplateModal(true)}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Create Template</span>
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div className="col-span-3 sm:col-span-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              <option value="Motor Control">Motor Control</option>
              <option value="Safety">Safety</option>
              <option value="Process Control">Process Control</option>
              <option value="Sequence Control">Sequence Control</option>
              <option value="Communication">Communication</option>
            </select>
          </div>
          
          <div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="ladder">Ladder Logic</option>
              <option value="st">Structured Text</option>
              <option value="fbd">Function Block</option>
              <option value="sfc">Sequential Function Chart</option>
              <option value="safety">Safety Logic</option>
            </select>
          </div>
        </div>
      </div>

      {/* Template Grid */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center space-x-2">
              <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />
              <span className="text-white">Loading templates...</span>
            </div>
          </div>
        ) : filteredTemplates.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 bg-gray-800 rounded-lg border border-gray-700">
            <FileText className="w-16 h-16 text-gray-600 mb-4" />
            <h3 className="text-lg font-medium text-white">No templates found</h3>
            <p className="text-gray-400 mt-2">Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.map(template => (
              <div 
                key={template.id} 
                className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden hover:border-blue-500 transition-colors cursor-pointer"
                onClick={() => setSelectedTemplate(template)}
              >
                <div className="p-4 border-b border-gray-700">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className={getTypeBadgeColor(template.type)}>
                        {getTypeIcon(template.type)}
                      </span>
                      <h3 className="text-white font-medium truncate">{template.name}</h3>
                    </div>
                    {template.isVerified && (
                      <div className="flex items-center space-x-1 text-green-400">
                        <Check className="w-4 h-4" />
                        <span className="text-xs">Verified</span>
                      </div>
                    )}
                  </div>
                  <p className="text-gray-400 text-sm line-clamp-2">{template.description}</p>
                </div>
                
                <div className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <span className={`px-2 py-1 rounded text-xs ${getCategoryBadgeColor(template.category)}`}>
                      {template.category}
                    </span>
                    {template.type === 'ladder' || template.type === 'safety' ? (
                      <span className="text-xs text-gray-400">{template.rungCount} rungs</span>
                    ) : null}
                  </div>
                  
                  <div className="flex flex-wrap gap-2 mb-3">
                    {template.tags.slice(0, 3).map((tag, index) => (
                      <span key={index} className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                    {template.tags.length > 3 && (
                      <span className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-xs">
                        +{template.tags.length - 3}
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <span>By {template.createdBy}</span>
                    <span>{template.lastModified.toLocaleDateString()}</span>
                  </div>
                </div>
                
                <div className="bg-gray-700/50 p-3 flex items-center justify-between">
                  <div className="flex items-center space-x-1 text-xs">
                    {template.isPublic ? (
                      <>
                        <Eye className="w-3 h-3 text-blue-400" />
                        <span className="text-blue-400">Public</span>
                      </>
                    ) : (
                      <>
                        <EyeOff className="w-3 h-3 text-gray-400" />
                        <span className="text-gray-400">Private</span>
                      </>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Copy template functionality
                      }}
                      className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // Download template functionality
                      }}
                      className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Template Details Sidebar - Show when a template is selected */}
      {selectedTemplate && (
        <div className="fixed inset-y-0 right-0 w-96 bg-gray-800 border-l border-gray-700 p-6 overflow-y-auto shadow-xl z-10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">Template Details</h3>
            <button
              onClick={() => setSelectedTemplate(null)}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="space-y-6">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <span className={getTypeBadgeColor(selectedTemplate.type)}>
                    {getTypeIcon(selectedTemplate.type)}
                  </span>
                  <h4 className="text-white font-medium">{selectedTemplate.name}</h4>
                </div>
                {selectedTemplate.isVerified && (
                  <div className="flex items-center space-x-1 text-green-400">
                    <Check className="w-4 h-4" />
                    <span className="text-xs">Verified</span>
                  </div>
                )}
              </div>
              <p className="text-gray-300 text-sm mb-3">{selectedTemplate.description}</p>
              <div className="flex items-center justify-between text-xs text-gray-400">
                <span>Created by {selectedTemplate.createdBy}</span>
                <span>{selectedTemplate.createdAt.toLocaleDateString()}</span>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">Template Information</h5>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Category</span>
                  <span className={`px-2 py-1 rounded text-xs ${getCategoryBadgeColor(selectedTemplate.category)}`}>
                    {selectedTemplate.category}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Type</span>
                  <span className={`px-2 py-1 rounded text-xs ${getTypeBadgeColor(selectedTemplate.type)}`}>
                    {selectedTemplate.type.toUpperCase()}
                  </span>
                </div>
                {(selectedTemplate.type === 'ladder' || selectedTemplate.type === 'safety') && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Rungs</span>
                    <span className="text-white">{selectedTemplate.rungCount}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-400">Last Modified</span>
                  <span className="text-white">{selectedTemplate.lastModified.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Visibility</span>
                  <span className="text-white">{selectedTemplate.isPublic ? 'Public' : 'Private'}</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">Tags</h5>
              <div className="flex flex-wrap gap-2">
                {selectedTemplate.tags.map((tag, index) => (
                  <span key={index} className="px-2 py-1 bg-gray-600 text-gray-300 rounded text-xs">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => setShowPreview(!showPreview)}
                className="flex-1 flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Eye className="w-4 h-4" />
                <span>{showPreview ? 'Hide Preview' : 'Preview'}</span>
              </button>
              <button
                onClick={() => {
                  // Edit template functionality
                }}
                className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>Edit</span>
              </button>
            </div>
            
            <button
              onClick={() => setShowDeleteConfirmation(true)}
              className="w-full flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Trash2 className="w-4 h-4" />
              <span>Delete Template</span>
            </button>
            
            {showPreview && (
              <div className="bg-gray-700 rounded-lg p-4 mt-4">
                <h5 className="text-white font-medium mb-3">Template Preview</h5>
                <div className="bg-gray-800 rounded-lg p-3 max-h-96 overflow-y-auto">
                  {selectedTemplate.type === 'ladder' || selectedTemplate.type === 'safety' ? (
                    <div className="space-y-3">
                      {Array.from({ length: Math.min(5, selectedTemplate.rungCount) }).map((_, index) => (
                        <div key={index} className="border border-gray-700 rounded p-2">
                          <div className="flex items-center space-x-2 mb-2">
                            <span className="text-gray-400 text-xs">{index.toString().padStart(4, '0')}</span>
                            <div className="flex-1 h-px bg-gray-700"></div>
                          </div>
                          <div className="flex items-center">
                            <div className="w-4 h-px bg-blue-500"></div>
                            <div className="flex items-center space-x-2">
                              <div className="px-3 py-2 border-2 border-green-400 text-green-400 rounded">][</div>
                              <div className="px-3 py-2 border-2 border-red-400 text-red-400 rounded">/]</div>
                              <div className="px-3 py-2 border-2 border-blue-400 text-blue-400 rounded bg-blue-400/10">( )</div>
                            </div>
                            <div className="flex-1 h-px bg-blue-500 ml-2"></div>
                          </div>
                        </div>
                      ))}
                      {selectedTemplate.rungCount > 5 && (
                        <div className="text-center text-gray-400 text-sm py-2">
                          + {selectedTemplate.rungCount - 5} more rungs
                        </div>
                      )}
                    </div>
                  ) : selectedTemplate.type === 'st' ? (
                    <pre className="text-gray-300 text-xs font-mono whitespace-pre-wrap">
{`FUNCTION_BLOCK ${selectedTemplate.name.replace(/\s+/g, '_')}
VAR_INPUT
    Start_Command : BOOL;
    Stop_Command : BOOL;
    Emergency_Stop : BOOL;
END_VAR

VAR_OUTPUT
    Motor_Run : BOOL;
    Status : INT;
END_VAR

VAR
    State : INT := 0;
END_VAR

// Main logic
IF Emergency_Stop THEN
    State := 0;
    Motor_Run := FALSE;
ELSE
    CASE State OF
        0: // Stopped state
            IF Start_Command THEN
                State := 1;
            END_IF;
            Motor_Run := FALSE;
            
        1: // Running state
            IF Stop_Command THEN
                State := 0;
            END_IF;
            Motor_Run := TRUE;
    END_CASE;
END_IF;

Status := State;

END_FUNCTION_BLOCK`}
                    </pre>
                  ) : (
                    <div className="flex items-center justify-center h-40 text-gray-400">
                      <p>Preview not available for this template type</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Add Template Modal */}
      {showAddTemplateModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Create New Template</h3>
              <button
                onClick={() => setShowAddTemplateModal(false)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Template Name</label>
                <input
                  type="text"
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter template name"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Category</label>
                <select
                  value={newTemplate.category}
                  onChange={(e) => setNewTemplate({...newTemplate, category: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Motor Control">Motor Control</option>
                  <option value="Safety">Safety</option>
                  <option value="Process Control">Process Control</option>
                  <option value="Sequence Control">Sequence Control</option>
                  <option value="Communication">Communication</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Template Type</label>
                <select
                  value={newTemplate.type}
                  onChange={(e) => setNewTemplate({...newTemplate, type: e.target.value as any})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="ladder">Ladder Logic</option>
                  <option value="st">Structured Text</option>
                  <option value="fbd">Function Block</option>
                  <option value="sfc">Sequential Function Chart</option>
                  <option value="safety">Safety Logic</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Description</label>
                <textarea
                  value={newTemplate.description}
                  onChange={(e) => setNewTemplate({...newTemplate, description: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none"
                  placeholder="Enter template description"
                ></textarea>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Tags (comma separated)</label>
                <input
                  type="text"
                  value={newTemplate.tags}
                  onChange={(e) => setNewTemplate({...newTemplate, tags: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="motor, safety, control"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="isPublic"
                  checked={newTemplate.isPublic}
                  onChange={(e) => setNewTemplate({...newTemplate, isPublic: e.target.checked})}
                  className="rounded bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                />
                <label htmlFor="isPublic" className="text-gray-300">Make template public</label>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddTemplateModal(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleAddTemplate}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                disabled={!newTemplate.name || !newTemplate.description}
              >
                Create Template
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && selectedTemplate && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center space-x-3 text-red-400 mb-4">
              <AlertTriangle className="w-6 h-6" />
              <h3 className="text-xl font-semibold">Confirm Deletion</h3>
            </div>
            
            <p className="text-white mb-2">Are you sure you want to delete this template?</p>
            <p className="text-gray-400 mb-6">This action cannot be undone. All users will lose access to this template.</p>
            
            <div className="bg-gray-700/50 rounded-lg p-4 mb-6">
              <h4 className="text-white font-medium mb-2">{selectedTemplate.name}</h4>
              <p className="text-gray-400 text-sm">{selectedTemplate.description}</p>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirmation(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteTemplate}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Delete Template
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateManager;