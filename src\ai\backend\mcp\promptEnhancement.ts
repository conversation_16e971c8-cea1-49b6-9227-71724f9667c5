import { ContextItem, ContextItemType, PromptEnhancementRequest, PromptEnhancementResponse } from '../../types/mcp';
import { db } from '../backend/db';
import crypto from 'crypto';

export class PromptEnhancementService {
  async enhancePrompt(request: PromptEnhancementRequest): Promise<PromptEnhancementResponse> {
    try {
      // Get context items of specified types
      const contextTypes = request.contextTypes || ['tag', 'program', 'rung', 'safety', 'doc', 'version', 'standard'];
      const maxTokens = request.maxTokens || 1000;
      
      // Query database for context items
      const contextItems = await this.getContextItems(contextTypes);
      
      // Serialize context
      const serializedContext = this.serializeContext(contextItems);
      
      // Optimize for token limit
      const optimizedContext = this.optimizeContext(serializedContext, maxTokens);
      
      // Combine with prompt
      const enhancedPrompt = `${optimizedContext}\n\nUser Request: ${request.prompt}`;
      
      // Calculate confidence based on context completeness
      const confidence = this.calculateConfidence(contextItems, contextTypes);
      
      // Generate a deterministic fingerprint for this context combination
      const contextFingerprint = this.generateContextFingerprint(contextItems);
      
      // Record context usage for analytics
      await this.recordContextUsage(contextItems.map(item => item.id), request.prompt);
      
      return {
        enhancedPrompt,
        contextItems: contextItems.map(item => item.id),
        confidence,
        contextFingerprint
      };
    } catch (error) {
      console.error('Error enhancing prompt:', error);
      // Return minimal enhancement if error occurs
      return {
        enhancedPrompt: `User Request: ${request.prompt}`,
        contextItems: [],
        confidence: 0.1,
        contextFingerprint: this.generateContextFingerprint([])
      };
    }
  }
  
  private async getContextItems(types: ContextItemType[]): Promise<ContextItem[]> {
    try {
      const placeholders = types.map((_, i) => `$${i + 1}`).join(', ');
      const query = `
        SELECT * FROM mcp_context_items 
        WHERE type IN (${placeholders})
        ORDER BY timestamp DESC
        LIMIT 50
      `;
      
      const result = await db.query(query, types);
      
      return result.rows.map((row: any) => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : {}
      }));
    } catch (error) {
      console.error('Error getting context items:', error);
      return [];
    }
  }
  
  private serializeContext(contextItems: ContextItem[]): string {
    // Group items by type for better organization
    const groupedItems: Record<string, ContextItem[]> = {};
    
    contextItems.forEach(item => {
      if (!groupedItems[item.type]) {
        groupedItems[item.type] = [];
      }
      groupedItems[item.type].push(item);
    });
    
    // Build the prompt with sections for each type
    let result = '# Context Information\n\n';
    
    // Add standards and versions first as they're global context
    if (groupedItems['standard']) {
      result += '## Standards\n';
      groupedItems['standard'].forEach(item => {
        result += `- ${item.value}\n`;
      });
      result += '\n';
    }
    
    if (groupedItems['version']) {
      result += '## Versions\n';
      groupedItems['version'].forEach(item => {
        result += `- ${item.value}\n`;
      });
      result += '\n';
    }
    
    // Add safety context next as it's critical
    if (groupedItems['safety']) {
      result += '## Safety Context\n';
      groupedItems['safety'].forEach(item => {
        result += `- ${item.value}\n`;
      });
      result += '\n';
    }
    
    // Add program context
    if (groupedItems['program']) {
      result += '## Program Context\n';
      groupedItems['program'].forEach(item => {
        result += `- ${item.value}\n`;
      });
      result += '\n';
    }
    
    // Add rung context
    if (groupedItems['rung']) {
      result += '## Rung Context\n';
      groupedItems['rung'].forEach(item => {
        result += `- ${item.value}\n`;
      });
      result += '\n';
    }
    
    // Add tag context
    if (groupedItems['tag']) {
      result += '## Tags\n';
      groupedItems['tag'].forEach(item => {
        result += `- ${item.value}\n`;
      });
      result += '\n';
    }
    
    // Add documentation context
    if (groupedItems['doc']) {
      result += '## Documentation\n';
      groupedItems['doc'].forEach(item => {
        result += `- ${item.value}\n`;
      });
      result += '\n';
    }
    
    return result;
  }
  
  private optimizeContext(context: string, maxTokens: number): string {
    // Simple token estimation (4 chars ≈ 1 token)
    const estimatedTokens = context.length / 4;
    
    if (estimatedTokens <= maxTokens) {
      return context;
    }
    
    // If over token limit, truncate context
    const reductionFactor = maxTokens / estimatedTokens;
    const lines = context.split('\n');
    const sectionsToKeep = Math.ceil(lines.length * reductionFactor);
    
    return lines.slice(0, sectionsToKeep).join('\n');
  }
  
  private calculateConfidence(contextItems: ContextItem[], requestedTypes: ContextItemType[]): number {
    // Define weights for different context types
    const weights: Record<ContextItemType, number> = {
      program: 0.3,
      tag: 0.2,
      rung: 0.2,
      safety: 0.15,
      standard: 0.1,
      version: 0.05,
      doc: 0.1
    };
    
    // Check which context types are present
    const presentTypes = new Set(contextItems.map(item => item.type));
    
    // Calculate confidence score
    let confidence = 0;
    let totalWeight = 0;
    
    requestedTypes.forEach(type => {
      totalWeight += weights[type] || 0.1;
      if (presentTypes.has(type)) {
        confidence += weights[type] || 0.1;
      }
    });
    
    // Normalize to 0-1 range
    return totalWeight > 0 ? confidence / totalWeight : 0;
  }
  
  private generateContextFingerprint(contextItems: ContextItem[]): string {
    // Generate a deterministic "fingerprint" for this context combination
    const contextTypes = contextItems.map(item => item.type).sort().join('-');
    const contextCount = contextItems.length;
    const contextIds = contextItems.map(item => item.id).sort().join('-');
    
    const hash = crypto.createHash('sha256')
      .update(`${contextTypes}-${contextCount}-${contextIds}`)
      .digest('hex');
    
    return hash.substring(0, 8);
  }
  
  private async recordContextUsage(contextItemIds: string[], prompt: string): Promise<void> {
    try {
      // Create a unique request ID
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      // Record usage for each context item
      for (const contextId of contextItemIds) {
        await db.query(
          `INSERT INTO mcp_context_usage 
           (context_id, request_id, prompt_type, timestamp)
           VALUES ($1, $2, $3, NOW())`,
          [contextId, requestId, this.inferPromptType(prompt)]
        );
      }
    } catch (error) {
      console.error('Error recording context usage:', error);
    }
  }
  
  private inferPromptType(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase();
    
    if (lowerPrompt.includes('explain') || lowerPrompt.includes('what is')) {
      return 'explain';
    } else if (lowerPrompt.includes('generate') || lowerPrompt.includes('create')) {
      return 'generate';
    } else if (lowerPrompt.includes('debug') || lowerPrompt.includes('fix')) {
      return 'debug';
    } else if (lowerPrompt.includes('refactor') || lowerPrompt.includes('improve')) {
      return 'refactor';
    } else if (lowerPrompt.includes('suggest') || lowerPrompt.includes('recommend')) {
      return 'suggest';
    } else {
      return 'general';
    }
  }
}

export const promptEnhancementService = new PromptEnhancementService();