{"name": "relay-plc-ide", "private": true, "version": "1.0.0", "type": "module", "scripts": {"ai-server": "cd src/ai/backend && npm run dev", "ai-server:prod": "cd src/ai/backend && npm run start", "dev:full": "concurrently \"npm run dev\" \"npm run ai-server\"", "test:ai": "cd src/ai/backend && npm test", "build:ai": "cd src/ai/backend && npm run build"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "compression": "^1.7.4", "cors": "^2.8.5", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "dotenv": "^16.4.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.2.1", "helmet": "^7.1.0", "jszip": "^3.10.1", "lucide-react": "^0.344.0", "monaco-editor": "^0.45.0", "mqtt": "^5.3.4", "pg": "^8.16.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-flow-renderer": "^10.3.17", "react-hotkeys-hook": "^4.4.1", "react-virtualized": "^9.22.5", "recharts": "^2.8.0", "redis": "^4.6.13", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "uuid": "^9.0.1", "xml2js": "^0.6.2", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.1", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/node": "^24.0.4", "@types/pg": "^8.11.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.7", "@types/xml2js": "^0.4.14", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "autocannon": "^8.0.0", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^30.0.2", "postcss": "^8.4.35", "supertest": "^7.1.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.0.0"}}