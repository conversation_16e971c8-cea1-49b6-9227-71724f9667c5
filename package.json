{"name": "relay-plc-ide", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "ai-server": "node --loader ts-node/esm src/ai/backend/server.ts", "dev:full": "concurrently \"npm run dev\" \"npm run ai-server\""}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-flow-renderer": "^10.3.17", "monaco-editor": "^0.45.0", "@monaco-editor/react": "^4.6.0", "zustand": "^4.4.7", "uuid": "^9.0.1", "date-fns": "^2.30.0", "socket.io-client": "^4.7.4", "crypto-js": "^4.2.0", "jszip": "^3.10.1", "xml2js": "^0.6.2", "mqtt": "^5.3.4", "recharts": "^2.8.0", "react-hotkeys-hook": "^4.4.1", "react-virtualized": "^9.22.5", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "socket.io": "^4.7.4", "pg": "^8.11.3", "redis": "^4.6.13", "dotenv": "^16.4.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.7", "@types/crypto-js": "^4.2.1", "@types/xml2js": "^0.4.14", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/pg": "^8.11.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "ts-node": "^10.9.1", "concurrently": "^8.2.2"}}