import crypto from 'crypto';

export class DigitalSignatureService {
  async signAIOutput(content: string, metadata: any) {
    const sign = crypto.createSign('SHA256');
    sign.update(content);
    sign.update(JSON.stringify(metadata));

    const signature = sign.sign('your-private-key', 'hex');

    return {
      signature,
      algorithm: 'SHA256',
      timestamp: new Date()
    };
  }
}

export const digitalSignatureService = new DigitalSignatureService();