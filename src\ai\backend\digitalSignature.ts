import crypto from 'crypto';

export interface DigitalSignature {
  algorithm: string;
  hash: string;
  signature: string;
  timestamp: Date;
  keyId: string;
  valid: boolean;
}

export interface SignedContent {
  content: string;
  metadata: any;
  signature: DigitalSignature;
}

class DigitalSignatureService {
  private privateKey: string;
  private publicKey: string;
  private keyId: string;

  constructor() {
    // In production, these would be loaded from secure key management
    this.keyId = 'ai-signing-key-v1';
    this.generateKeyPair();
  }

  private generateKeyPair(): void {
    // Generate RSA key pair for signing
    const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });

    this.privateKey = privateKey;
    this.publicKey = publicKey;
  }

  async signAIOutput(content: string, metadata: any): Promise<SignedContent> {
    const timestamp = new Date();
    const dataToSign = JSON.stringify({ content, metadata, timestamp });
    
    // Create hash of the data
    const hash = crypto.createHash('sha256').update(dataToSign).digest('hex');
    
    // Sign the hash
    const signature = crypto.sign('sha256', Buffer.from(hash), {
      key: this.privateKey,
      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
    }).toString('base64');

    const digitalSignature: DigitalSignature = {
      algorithm: 'RSA-PSS-SHA256',
      hash,
      signature,
      timestamp,
      keyId: this.keyId,
      valid: true
    };

    return {
      content,
      metadata: { ...metadata, signedAt: timestamp },
      signature: digitalSignature
    };
  }

  async verifySignature(signedContent: SignedContent): Promise<boolean> {
    try {
      const { content, metadata, signature } = signedContent;
      
      // Recreate the original data
      const dataToVerify = JSON.stringify({ 
        content, 
        metadata: { ...metadata, signedAt: signature.timestamp }, 
        timestamp: signature.timestamp 
      });
      
      // Verify hash
      const computedHash = crypto.createHash('sha256').update(dataToVerify).digest('hex');
      if (computedHash !== signature.hash) {
        return false;
      }

      // Verify signature
      const isValid = crypto.verify(
        'sha256',
        Buffer.from(signature.hash),
        {
          key: this.publicKey,
          padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
        },
        Buffer.from(signature.signature, 'base64')
      );

      return isValid;
    } catch (error) {
      console.error('Signature verification failed:', error);
      return false;
    }
  }

  async createIntegrityProof(content: string): Promise<string> {
    // Create a tamper-evident proof of the content
    const timestamp = Date.now();
    const nonce = crypto.randomBytes(16).toString('hex');
    const proof = crypto.createHash('sha256')
      .update(`${content}:${timestamp}:${nonce}`)
      .digest('hex');
    
    return `${timestamp}:${nonce}:${proof}`;
  }

  async verifyIntegrityProof(content: string, proof: string): Promise<boolean> {
    try {
      const [timestamp, nonce, expectedProof] = proof.split(':');
      const computedProof = crypto.createHash('sha256')
        .update(`${content}:${timestamp}:${nonce}`)
        .digest('hex');
      
      return computedProof === expectedProof;
    } catch (error) {
      return false;
    }
  }

  getPublicKey(): string {
    return this.publicKey;
  }

  getKeyId(): string {
    return this.keyId;
  }
}

export const digitalSignatureService = new DigitalSignatureService();