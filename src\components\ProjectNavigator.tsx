import React, { useState } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  Folder, 
  FolderOpen, 
  FileCode, 
  Cpu, 
  Network,
  Settings,
  GitBranch,
  Circle,
  CheckCircle2,
  Plus,
  Trash2,
  Edit,
  Copy,
  Download,
  Upload,
  Tag,
  Shield,
  Code,
  Grid3X3,
  Workflow,
  Save,
  RefreshCw,
  AlertTriangle
} from 'lucide-react';
import { usePLCStore, availablePLCModels } from '../store/plcStore';

interface TreeNode {
  id: string;
  name: string;
  type: 'folder' | 'program' | 'subroutine' | 'tag' | 'target';
  children?: TreeNode[];
  icon?: React.ReactNode;
  status?: 'synced' | 'modified' | 'error';
  fileType?: string;
}

const ProjectNavigator: React.FC = () => {
  const { 
    currentProject, 
    activeProgram, 
    setActiveProgram, 
    createProgram, 
    deleteProgram,
    updateProgram,
    createTag,
    deleteTag,
    addTarget,
    removeTarget,
    createProject
  } = usePLCStore();
  
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(
    new Set(['root', 'programs', 'targets', 'tags'])
  );
  const [selectedNode, setSelectedNode] = useState<string>(activeProgram || '');
  const [showContextMenu, setShowContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    nodeId: string;
    nodeType: string;
    nodeName: string;
  }>({
    visible: false,
    x: 0,
    y: 0,
    nodeId: '',
    nodeType: '',
    nodeName: ''
  });
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);
  const [newProjectData, setNewProjectData] = useState({
    name: '',
    description: '',
    plcBrand: 'siemens',
    plcModel: 'CPU 1511-1 PN'
  });

  const toggleNode = (nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  };

  const handleNodeClick = (nodeId: string, nodeType: string) => {
    setSelectedNode(nodeId);
    if (nodeType === 'program') {
      setActiveProgram(nodeId);
    }
  };

  const handleContextMenu = (e: React.MouseEvent, nodeId: string, nodeType: string, nodeName: string) => {
    e.preventDefault();
    setShowContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      nodeId,
      nodeType,
      nodeName
    });
  };

  const handleCreateProgram = (type: 'ladder' | 'st' | 'fbd' | 'sfc' | 'safety') => {
    const name = prompt(`Enter name for new ${type.toUpperCase()} program:`);
    if (name) {
      createProgram(name, type);
    }
  };

  const handleDeleteProgram = (programId: string, programName: string) => {
    if (confirm(`Are you sure you want to delete "${programName}"?`)) {
      deleteProgram(programId);
    }
  };

  const handleRenameProgram = (programId: string, currentName: string) => {
    const newName = prompt('Enter new program name:', currentName);
    if (newName && newName !== currentName) {
      updateProgram(programId, { name: newName });
    }
  };

  const handleDuplicateProgram = (programId: string, programName: string) => {
    const newName = prompt('Enter name for duplicate program:', `${programName}_copy`);
    if (newName) {
      const program = currentProject?.programs.find(p => p.id === programId);
      if (program) {
        createProgram(newName, program.type);
        // In a real implementation, we would also copy the content
      }
    }
  };

  const handleCreateTag = (scope: 'INPUT' | 'OUTPUT' | 'GLOBAL') => {
    const name = prompt(`Enter name for new ${scope.toLowerCase()} tag:`);
    if (name) {
      createTag({
        name,
        type: 'BOOL',
        value: false,
        scope,
        description: `New ${scope.toLowerCase()} tag`
      });
    }
  };

  const handleDeleteTag = (tagId: string, tagName: string) => {
    if (confirm(`Are you sure you want to delete tag "${tagName}"?`)) {
      deleteTag(tagId);
    }
  };

  const handleCreateTarget = () => {
    const name = prompt('Enter name for new PLC target:');
    if (name) {
      addTarget({
        name,
        brand: 'siemens',
        model: 'CPU 1511-1 PN',
        connected: false,
        status: 'stop'
      });
    }
  };

  const handleDeleteTarget = (targetId: string, targetName: string) => {
    if (confirm(`Are you sure you want to delete target "${targetName}"?`)) {
      removeTarget(targetId);
    }
  };

  const handleClickOutside = () => {
    setShowContextMenu(prev => ({ ...prev, visible: false }));
  };

  const handleCreateNewProject = () => {
    if (!newProjectData.name) {
      alert('Project name is required');
      return;
    }
    
    createProject(newProjectData.name, newProjectData.description);
    
    // In a real implementation, we would also set the PLC brand and model
    // for the target in the new project
    
    setShowNewProjectModal(false);
    setNewProjectData({
      name: '',
      description: '',
      plcBrand: 'siemens',
      plcModel: 'CPU 1511-1 PN'
    });
  };

  React.useEffect(() => {
    if (showContextMenu.visible) {
      document.addEventListener('click', handleClickOutside);
      return () => {
        document.removeEventListener('click', handleClickOutside);
      };
    }
  }, [showContextMenu.visible]);

  if (!currentProject) {
    return (
      <div className="w-80 bg-base-dark border-r border-control-600 flex items-center justify-center">
        <div className="text-center text-control-400">
          <Folder className="w-12 h-12 mx-auto mb-4" />
          <p>No Project Loaded</p>
          <div className="mt-4">
            <button
              onClick={() => setShowNewProjectModal(true)}
              className="bg-accent hover:bg-accent/80 text-white px-4 py-2 rounded transition-colors"
            >
              Create New Project
            </button>
          </div>
        </div>
      </div>
    );
  }

  const projectTree: TreeNode = {
    id: 'root',
    name: currentProject.name,
    type: 'folder',
    children: [
      {
        id: 'targets',
        name: 'PLC Targets',
        type: 'folder',
        icon: <Cpu className="w-4 h-4" />,
        children: currentProject.targets.map(target => ({
          id: target.id,
          name: target.name,
          type: 'target' as const,
          status: target.connected ? 'synced' : 'error',
          icon: <Network className="w-4 h-4" />
        }))
      },
      {
        id: 'programs',
        name: 'Programs',
        type: 'folder',
        icon: <Folder className="w-4 h-4" />,
        children: currentProject.programs.map(program => ({
          id: program.id,
          name: program.name,
          type: 'program' as const,
          status: program.modified ? 'modified' : 'synced',
          fileType: program.type,
          icon: <FileCode className="w-4 h-4" />
        }))
      },
      {
        id: 'tags',
        name: 'Global Tags',
        type: 'folder',
        icon: <Settings className="w-4 h-4" />,
        children: [
          {
            id: 'input-tags',
            name: `Input Tags (${currentProject.globalTags.filter(t => t.scope === 'INPUT').length})`,
            type: 'tag' as const,
            status: 'synced',
            icon: <Circle className="w-3 h-3" />
          },
          {
            id: 'output-tags',
            name: `Output Tags (${currentProject.globalTags.filter(t => t.scope === 'OUTPUT').length})`,
            type: 'tag' as const,
            status: 'synced',
            icon: <Circle className="w-3 h-3" />
          }
        ]
      }
    ]
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'synced': return 'text-success';
      case 'modified': return 'text-primary';
      case 'error': return 'text-error';
      default: return 'text-control-400';
    }
  };

  const getFileTypeColor = (fileType?: string) => {
    switch (fileType) {
      case 'ladder': return 'text-accent';
      case 'st': return 'text-success';
      case 'fbd': return 'text-secondary';
      case 'sfc': return 'text-primary';
      case 'safety': return 'text-error';
      default: return 'text-control-400';
    }
  };

  const getFileTypeIcon = (fileType?: string) => {
    switch (fileType) {
      case 'ladder': return <Grid3X3 className="w-4 h-4" />;
      case 'st': return <Code className="w-4 h-4" />;
      case 'fbd': return <Workflow className="w-4 h-4" />;
      case 'sfc': return <GitBranch className="w-4 h-4" />;
      case 'safety': return <Shield className="w-4 h-4" />;
      default: return <FileCode className="w-4 h-4" />;
    }
  };

  const renderContextMenu = () => {
    if (!showContextMenu.visible) return null;

    const { nodeType, nodeId, nodeName } = showContextMenu;

    let menuItems: { label: string; icon: React.ReactNode; action: () => void; color?: string }[] = [];

    switch (nodeType) {
      case 'program':
        menuItems = [
          { 
            label: 'Open', 
            icon: <FileCode className="w-4 h-4" />, 
            action: () => handleNodeClick(nodeId, nodeType) 
          },
          { 
            label: 'Rename', 
            icon: <Edit className="w-4 h-4" />, 
            action: () => handleRenameProgram(nodeId, nodeName) 
          },
          { 
            label: 'Duplicate', 
            icon: <Copy className="w-4 h-4" />, 
            action: () => handleDuplicateProgram(nodeId, nodeName) 
          },
          { 
            label: 'Delete', 
            icon: <Trash2 className="w-4 h-4" />, 
            action: () => handleDeleteProgram(nodeId, nodeName),
            color: 'text-error'
          }
        ];
        break;
      case 'folder':
        if (nodeId === 'programs') {
          menuItems = [
            { 
              label: 'New Ladder Program', 
              icon: <Grid3X3 className="w-4 h-4" />, 
              action: () => handleCreateProgram('ladder') 
            },
            { 
              label: 'New ST Program', 
              icon: <Code className="w-4 h-4" />, 
              action: () => handleCreateProgram('st') 
            },
            { 
              label: 'New FBD Program', 
              icon: <Workflow className="w-4 h-4" />, 
              action: () => handleCreateProgram('fbd') 
            },
            { 
              label: 'New Safety Program', 
              icon: <Shield className="w-4 h-4" />, 
              action: () => handleCreateProgram('safety') 
            }
          ];
        } else if (nodeId === 'tags') {
          menuItems = [
            { 
              label: 'New Input Tag', 
              icon: <Tag className="w-4 h-4" />, 
              action: () => handleCreateTag('INPUT') 
            },
            { 
              label: 'New Output Tag', 
              icon: <Tag className="w-4 h-4" />, 
              action: () => handleCreateTag('OUTPUT') 
            },
            { 
              label: 'New Global Tag', 
              icon: <Tag className="w-4 h-4" />, 
              action: () => handleCreateTag('GLOBAL') 
            }
          ];
        } else if (nodeId === 'targets') {
          menuItems = [
            { 
              label: 'Add PLC Target', 
              icon: <Cpu className="w-4 h-4" />, 
              action: handleCreateTarget 
            }
          ];
        }
        break;
      case 'target':
        menuItems = [
          { 
            label: 'Connect', 
            icon: <Network className="w-4 h-4" />, 
            action: () => alert(`Connecting to ${nodeName}...`) 
          },
          { 
            label: 'Download to PLC', 
            icon: <Download className="w-4 h-4" />, 
            action: () => alert(`Downloading to ${nodeName}...`) 
          },
          { 
            label: 'Upload from PLC', 
            icon: <Upload className="w-4 h-4" />, 
            action: () => alert(`Uploading from ${nodeName}...`) 
          },
          { 
            label: 'Delete Target', 
            icon: <Trash2 className="w-4 h-4" />, 
            action: () => handleDeleteTarget(nodeId, nodeName),
            color: 'text-error'
          }
        ];
        break;
      case 'tag':
        if (nodeId === 'input-tags' || nodeId === 'output-tags') {
          const scope = nodeId === 'input-tags' ? 'INPUT' : 'OUTPUT';
          menuItems = [
            { 
              label: `New ${scope.toLowerCase()} Tag`, 
              icon: <Plus className="w-4 h-4" />, 
              action: () => handleCreateTag(scope) 
            }
          ];
        } else {
          menuItems = [
            { 
              label: 'Edit Tag', 
              icon: <Edit className="w-4 h-4" />, 
              action: () => alert(`Editing tag ${nodeName}...`) 
            },
            { 
              label: 'Delete Tag', 
              icon: <Trash2 className="w-4 h-4" />, 
              action: () => handleDeleteTag(nodeId, nodeName),
              color: 'text-error'
            }
          ];
        }
        break;
    }

    return (
      <div 
        className="fixed bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 py-1"
        style={{ 
          left: showContextMenu.x, 
          top: showContextMenu.y,
          minWidth: '180px'
        }}
      >
        {menuItems.map((item, index) => (
          <button
            key={index}
            className={`w-full flex items-center space-x-2 px-4 py-2 text-sm hover:bg-gray-700 transition-colors text-left ${item.color || 'text-white'}`}
            onClick={(e) => {
              e.stopPropagation();
              item.action();
              setShowContextMenu(prev => ({ ...prev, visible: false }));
            }}
          >
            {item.icon}
            <span>{item.label}</span>
          </button>
        ))}
      </div>
    );
  };

  const renderNewProjectModal = () => {
    if (!showNewProjectModal) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md">
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Create New Project</h3>
            <button
              onClick={() => setShowNewProjectModal(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Project Name *</label>
              <input
                type="text"
                value={newProjectData.name}
                onChange={(e) => setNewProjectData({...newProjectData, name: e.target.value})}
                placeholder="Enter project name"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Description</label>
              <textarea
                value={newProjectData.description}
                onChange={(e) => setNewProjectData({...newProjectData, description: e.target.value})}
                placeholder="Enter project description"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white h-20 resize-none"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">PLC Brand</label>
              <select
                value={newProjectData.plcBrand}
                onChange={(e) => {
                  const brand = e.target.value;
                  const models = availablePLCModels.find(b => b.brand === brand)?.models || [];
                  setNewProjectData({
                    ...newProjectData, 
                    plcBrand: brand,
                    plcModel: models[0] || ''
                  });
                }}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                {availablePLCModels.map(brand => (
                  <option key={brand.brand} value={brand.brand}>
                    {brand.brand.charAt(0).toUpperCase() + brand.brand.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">PLC Model</label>
              <select
                value={newProjectData.plcModel}
                onChange={(e) => setNewProjectData({...newProjectData, plcModel: e.target.value})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                {availablePLCModels
                  .find(b => b.brand === newProjectData.plcBrand)?.models
                  .map(model => (
                    <option key={model} value={model}>
                      {model}
                    </option>
                  ))
                }
              </select>
            </div>
            
            <div className="pt-4 border-t border-gray-700 flex justify-end">
              <button
                onClick={() => setShowNewProjectModal(false)}
                className="text-gray-400 hover:text-white px-4 py-2 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateNewProject}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
                disabled={!newProjectData.name}
              >
                Create Project
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderNode = (node: TreeNode, depth: number = 0) => {
    const isExpanded = expandedNodes.has(node.id);
    const isSelected = selectedNode === node.id;
    const hasChildren = node.children && node.children.length > 0;

    return (
      <div key={node.id}>
        <div
          className={`flex items-center px-2 py-1 text-sm cursor-pointer hover:bg-control-800/50 transition-colors duration-150 group ${
            isSelected ? 'bg-accent/20 border-r-2 border-accent' : ''
          }`}
          style={{ paddingLeft: `${depth * 16 + 8}px` }}
          onClick={() => {
            if (hasChildren) {
              toggleNode(node.id);
            }
            handleNodeClick(node.id, node.type);
          }}
          onContextMenu={(e) => handleContextMenu(e, node.id, node.type, node.name)}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown className="w-4 h-4 mr-1" />
            ) : (
              <ChevronRight className="w-4 h-4 mr-1" />
            )
          ) : (
            <div className="w-4 mr-1" />
          )}
          
          {node.icon ? (
            <span className={`mr-2 ${getFileTypeColor(node.fileType)}`}>
              {node.icon}
            </span>
          ) : node.fileType ? (
            <span className={`mr-2 ${getFileTypeColor(node.fileType)}`}>
              {getFileTypeIcon(node.fileType)}
            </span>
          ) : (
            <span className="mr-2 text-control-400">
              {node.type === 'folder' ? (isExpanded ? <FolderOpen className="w-4 h-4" /> : <Folder className="w-4 h-4" />) : <FileCode className="w-4 h-4" />}
            </span>
          )}
          
          <span className="flex-1 truncate text-neutral">{node.name}</span>
          
          {node.fileType && (
            <span className={`ml-2 text-xs px-1.5 py-0.5 rounded ${getFileTypeColor(node.fileType)} bg-control-800`}>
              {node.fileType.toUpperCase()}
            </span>
          )}
          
          {node.status && (
            <span className={`ml-2 ${getStatusColor(node.status)}`}>
              {node.status === 'synced' ? (
                <CheckCircle2 className="w-3 h-3" />
              ) : node.status === 'modified' ? (
                <Circle className="w-3 h-3" />
              ) : (
                <AlertTriangle className="w-3 h-3" />
              )}
            </span>
          )}

          {/* Context menu trigger */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleContextMenu(e, node.id, node.type, node.name);
            }}
            className="ml-2 p-1 opacity-0 group-hover:opacity-100 text-control-400 hover:text-neutral hover:bg-control-800/50 rounded transition-all"
          >
            <Settings className="w-3 h-3" />
          </button>
        </div>
        
        {hasChildren && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderNode(child, depth + 1))}
            {/* Add buttons */}
            {node.id === 'programs' && (
              <div className="ml-8 mt-2 space-y-1">
                <button
                  onClick={() => handleCreateProgram('ladder')}
                  className="flex items-center space-x-2 text-xs text-accent hover:text-accent/80 hover:bg-accent/10 px-2 py-1 rounded transition-colors"
                >
                  <Plus className="w-3 h-3" />
                  <span>Add Ladder Program</span>
                </button>
                <button
                  onClick={() => handleCreateProgram('st')}
                  className="flex items-center space-x-2 text-xs text-success hover:text-success/80 hover:bg-success/10 px-2 py-1 rounded transition-colors"
                >
                  <Plus className="w-3 h-3" />
                  <span>Add ST Program</span>
                </button>
                <button
                  onClick={() => handleCreateProgram('safety')}
                  className="flex items-center space-x-2 text-xs text-error hover:text-error/80 hover:bg-error/10 px-2 py-1 rounded transition-colors"
                >
                  <Plus className="w-3 h-3" />
                  <span>Add Safety Program</span>
                </button>
              </div>
            )}
            {node.id === 'tags' && (
              <div className="ml-8 mt-2 space-y-1">
                <button
                  onClick={() => handleCreateTag('INPUT')}
                  className="flex items-center space-x-2 text-xs text-blue-400 hover:text-blue-300 hover:bg-blue-400/10 px-2 py-1 rounded transition-colors"
                >
                  <Plus className="w-3 h-3" />
                  <span>Add Input Tag</span>
                </button>
                <button
                  onClick={() => handleCreateTag('OUTPUT')}
                  className="flex items-center space-x-2 text-xs text-green-400 hover:text-green-300 hover:bg-green-400/10 px-2 py-1 rounded transition-colors"
                >
                  <Plus className="w-3 h-3" />
                  <span>Add Output Tag</span>
                </button>
              </div>
            )}
            {node.id === 'targets' && (
              <div className="ml-8 mt-2 space-y-1">
                <button
                  onClick={handleCreateTarget}
                  className="flex items-center space-x-2 text-xs text-purple-400 hover:text-purple-300 hover:bg-purple-400/10 px-2 py-1 rounded transition-colors"
                >
                  <Plus className="w-3 h-3" />
                  <span>Add PLC Target</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-80 bg-base-dark border-r border-control-600 flex flex-col">
      <div className="p-4 border-b border-control-600">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-neutral">Project Explorer</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => alert('Refreshing project...')}
              className="p-1 text-control-400 hover:text-neutral hover:bg-control-800/50 rounded transition-colors"
              title="Refresh"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
            <button
              onClick={() => alert('Saving project...')}
              className="p-1 text-control-400 hover:text-neutral hover:bg-control-800/50 rounded transition-colors"
              title="Save Project"
            >
              <Save className="w-4 h-4" />
            </button>
          </div>
        </div>
        <p className="text-xs text-control-400 mt-1">IEC 61131-3 Compliant</p>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {renderNode(projectTree)}
      </div>

      {/* Project Status Bar */}
      <div className="p-3 border-t border-control-600 flex items-center justify-between text-xs text-control-400">
        <div className="flex items-center space-x-2">
          <GitBranch className="w-3 h-3" />
          <span>main</span>
        </div>
        <div className="flex items-center space-x-2">
          <span>{currentProject.programs.length} programs</span>
          <span>•</span>
          <span>{currentProject.globalTags.length} tags</span>
        </div>
      </div>

      {renderContextMenu()}
      {renderNewProjectModal()}
    </div>
  );
};

// X icon component
const X = ({ className }: { className?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M18 6 6 18"></path>
    <path d="m6 6 12 12"></path>
  </svg>
);

export default ProjectNavigator;