import React, { useState, useEffect } from 'react';
import { usePLCStore } from '../../store/plcStore';
import { LadderRung, LadderElement } from '../../types/plc';
import { 
  Zap, 
  Play, 
  Square, 
  RotateCcw, 
  Eye,
  EyeOff,
  Target,
  GitBranch,
  Activity,
  AlertTriangle,
  CheckCircle2,
  HelpCircle
} from 'lucide-react';

interface TraceStep {
  elementId: string;
  rungId: string;
  tagName?: string;
  value: boolean;
  timestamp: Date;
  type: 'input' | 'logic' | 'output';
  path: string[];
}

interface SignalPath {
  id: string;
  elements: string[];
  active: boolean;
  powerFlow: boolean;
}

const SignalTracer: React.FC = () => {
  const { currentProject, activeProgram, simulationMode, updateTagValue, toggleSimulation } = usePLCStore();
  const [tracingActive, setTracingActive] = useState(false);
  const [selectedTag, setSelectedTag] = useState<string>('');
  const [traceHistory, setTraceHistory] = useState<TraceStep[]>([]);
  const [highlightedElements, setHighlightedElements] = useState<Set<string>>(new Set());
  const [signalPaths, setSignalPaths] = useState<SignalPath[]>([]);
  const [traceSpeed, setTraceSpeed] = useState(500); // ms
  const [traceInterval, setTraceIntervalState] = useState<NodeJS.Timeout | null>(null);

  const program = currentProject?.programs.find(p => p.id === activeProgram);
  const rungs = (program?.content as LadderRung[]) || [];

  // Clear trace when simulation mode changes
  useEffect(() => {
    if (!simulationMode && tracingActive) {
      stopTracing();
    }
  }, [simulationMode]);

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      if (traceInterval) {
        clearInterval(traceInterval);
      }
    };
  }, [traceInterval]);

  const traceSignalPath = (tagName: string) => {
    const tag = currentProject?.globalTags.find(t => t.name === tagName);
    if (!tag) return;

    const newTraceSteps: TraceStep[] = [];
    const highlighted = new Set<string>();
    const paths: SignalPath[] = [];

    // Find all elements using this tag
    rungs.forEach(rung => {
      rung.elements.forEach(element => {
        if (element.tag === tagName) {
          const step: TraceStep = {
            elementId: element.id,
            rungId: rung.id,
            tagName,
            value: Boolean(tag.value),
            timestamp: new Date(),
            type: element.type === 'coil' ? 'output' : 'input',
            path: []
          };
          
          newTraceSteps.push(step);
          highlighted.add(element.id);
          
          // Trace power flow path
          if (element.type === 'contact' && tag.value) {
            const path = traceConnectedElements(rung, element, highlighted, newTraceSteps);
            if (path.length > 0) {
              paths.push({
                id: `path_${element.id}`,
                elements: path,
                active: true,
                powerFlow: tag.value
              });
            }
          }
        }
      });
    });

    setTraceHistory(prev => [...prev.slice(-50), ...newTraceSteps]);
    setHighlightedElements(highlighted);
    setSignalPaths(paths);
  };

  const traceConnectedElements = (
    rung: LadderRung, 
    startElement: LadderElement, 
    highlighted: Set<string>,
    traceSteps: TraceStep[]
  ): string[] => {
    const visited = new Set<string>();
    const path: string[] = [];
    const queue = [{ element: startElement, pathSoFar: [startElement.id] }];

    while (queue.length > 0) {
      const { element: current, pathSoFar } = queue.shift()!;
      if (visited.has(current.id)) continue;
      visited.add(current.id);
      path.push(current.id);

      current.connections.forEach(connId => {
        const connectedElement = rung.elements.find(e => e.id === connId);
        if (connectedElement && !visited.has(connectedElement.id)) {
          highlighted.add(connectedElement.id);
          
          const tag = currentProject?.globalTags.find(t => t.name === connectedElement.tag);
          const step: TraceStep = {
            elementId: connectedElement.id,
            rungId: rung.id,
            tagName: connectedElement.tag,
            value: Boolean(tag?.value),
            timestamp: new Date(),
            type: connectedElement.type === 'coil' ? 'output' : 'logic',
            path: [...pathSoFar, connectedElement.id]
          };
          
          traceSteps.push(step);
          queue.push({ 
            element: connectedElement, 
            pathSoFar: [...pathSoFar, connectedElement.id] 
          });
        }
      });
    }

    return path;
  };

  const startTracing = () => {
    if (!selectedTag) return;
    
    // Enable simulation mode if not already active
    if (!simulationMode) {
      toggleSimulation();
    }
    
    setTracingActive(true);
    setTraceHistory([]);
    setHighlightedElements(new Set());
    setSignalPaths([]);
    
    // Initial trace
    traceSignalPath(selectedTag);
    
    // Set up interval for continuous tracing
    const interval = setInterval(() => {
      traceSignalPath(selectedTag);
    }, traceSpeed);
    
    setTraceIntervalState(interval);
  };

  const stopTracing = () => {
    setTracingActive(false);
    if (traceInterval) {
      clearInterval(traceInterval);
      setTraceIntervalState(null);
    }
  };

  const clearTrace = () => {
    setTraceHistory([]);
    setHighlightedElements(new Set());
    setSignalPaths([]);
  };

  const simulateTagChange = (tagName: string, value: boolean) => {
    const tag = currentProject?.globalTags.find(t => t.name === tagName);
    if (tag) {
      updateTagValue(tag.id, value);
    }
  };

  const renderTraceStep = (step: TraceStep, index: number) => {
    const getStepColor = () => {
      if (step.type === 'input') return 'text-blue-400';
      if (step.type === 'output') return 'text-green-400';
      return 'text-purple-400';
    };

    const getStepIcon = () => {
      if (step.type === 'input') return <Target className="w-4 h-4" />;
      if (step.type === 'output') return <Zap className="w-4 h-4" />;
      return <GitBranch className="w-4 h-4" />;
    };

    return (
      <div key={`${step.elementId}-${index}`} className="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg border border-gray-700">
        <span className={getStepColor()}>
          {getStepIcon()}
        </span>
        
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <span className="text-white text-sm font-medium">
              {step.tagName || 'Unknown'}
            </span>
            <span className={`text-xs px-2 py-1 rounded ${
              step.value ? 'bg-green-600/20 text-green-400 border border-green-600/30' : 'bg-gray-600/20 text-gray-400 border border-gray-600/30'
            }`}>
              {step.value ? 'TRUE' : 'FALSE'}
            </span>
            <span className="text-xs text-gray-500">
              Rung {rungs.find(r => r.id === step.rungId)?.number}
            </span>
          </div>
          <div className="text-xs text-gray-400 mt-1">
            {step.type} • Path: {step.path.length} elements
          </div>
        </div>
        
        <div className="text-xs text-gray-500">
          {step.timestamp.toLocaleTimeString()}
        </div>
      </div>
    );
  };

  const renderSignalPath = (path: SignalPath) => (
    <div key={path.id} className="bg-gray-800/30 rounded-lg p-3 border border-gray-600">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-white">Signal Path {path.id.split('_')[1]}</span>
        <div className="flex items-center space-x-2">
          <Activity className={`w-4 h-4 ${path.powerFlow ? 'text-green-400' : 'text-gray-400'}`} />
          <span className={`text-xs ${path.powerFlow ? 'text-green-400' : 'text-gray-400'}`}>
            {path.powerFlow ? 'ENERGIZED' : 'DE-ENERGIZED'}
          </span>
        </div>
      </div>
      <div className="text-xs text-gray-400">
        Elements: {path.elements.length} • Active: {path.active ? 'Yes' : 'No'}
      </div>
    </div>
  );

  const availableTags = currentProject?.globalTags.filter(tag => 
    rungs.some(rung => 
      rung.elements.some(element => element.tag === tag.name)
    )
  ) || [];

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Controls */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white">Signal Tracer</h3>
          <div className={`px-3 py-1 rounded-full text-sm ${
            tracingActive 
              ? 'bg-green-600/20 text-green-400 border border-green-600/30' 
              : 'bg-gray-600/20 text-gray-400 border border-gray-600/30'
          }`}>
            {tracingActive ? 'TRACING' : 'STOPPED'}
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm text-gray-400 mb-2">Select Tag to Trace</label>
            <select
              value={selectedTag}
              onChange={(e) => setSelectedTag(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              disabled={tracingActive}
            >
              <option value="">Choose a tag...</option>
              {availableTags.map(tag => (
                <option key={tag.id} value={tag.name}>
                  {tag.name} ({tag.type})
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-2">Trace Speed (ms)</label>
            <select
              value={traceSpeed}
              onChange={(e) => setTraceSpeed(Number(e.target.value))}
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              disabled={tracingActive}
            >
              <option value={100}>Fast (100ms)</option>
              <option value={500}>Normal (500ms)</option>
              <option value={1000}>Slow (1000ms)</option>
            </select>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {!tracingActive ? (
            <button
              onClick={startTracing}
              disabled={!selectedTag}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded transition-colors"
            >
              <Play className="w-4 h-4" />
              <span>Start Trace</span>
            </button>
          ) : (
            <button
              onClick={stopTracing}
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors"
            >
              <Square className="w-4 h-4" />
              <span>Stop Trace</span>
            </button>
          )}
          
          <button
            onClick={clearTrace}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title="Clear Trace History"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          
          {!simulationMode && (
            <button
              onClick={toggleSimulation}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
            >
              <Zap className="w-4 h-4" />
              <span>Enable Simulation</span>
            </button>
          )}
        </div>
        
        {!simulationMode && (
          <div className="mt-3 p-3 bg-amber-600/20 border border-amber-600/30 rounded">
            <div className="flex items-center space-x-2 text-amber-400 text-sm">
              <AlertTriangle className="w-4 h-4" />
              <span>Signal tracing requires simulation mode to be active. Click "Enable Simulation" to start.</span>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        <div className="grid grid-cols-2 gap-4 p-4 h-full">
          {/* Trace History */}
          <div className="flex flex-col">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-white font-semibold">Trace History</h4>
              <div className="text-sm text-gray-400">
                {traceHistory.length} steps recorded
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto space-y-2">
              {traceHistory.length === 0 ? (
                <div className="text-center py-12 text-gray-400">
                  <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No trace data</p>
                  <p className="text-sm">Start tracing to see signal flow</p>
                </div>
              ) : (
                traceHistory.slice().reverse().map((step, index) => 
                  renderTraceStep(step, traceHistory.length - index - 1)
                )
              )}
            </div>
          </div>

          {/* Signal Paths & Controls */}
          <div className="flex flex-col">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-white font-semibold">Signal Paths</h4>
              <div className="text-sm text-gray-400">
                {signalPaths.length} active paths
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto space-y-2">
              {signalPaths.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  <GitBranch className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No signal paths detected</p>
                </div>
              ) : (
                signalPaths.map(renderSignalPath)
              )}
            </div>

            {/* Tag Simulation Controls */}
            {selectedTag && simulationMode && (
              <div className="mt-4 p-4 bg-gray-800/50 rounded-lg border border-gray-700">
                <h5 className="text-white font-semibold mb-3">Tag Simulation</h5>
                <div className="space-y-2">
                  {availableTags
                    .filter(tag => tag.scope === 'INPUT')
                    .slice(0, 4)
                    .map(tag => (
                      <div key={tag.id} className="flex items-center justify-between">
                        <span className="text-sm text-white">{tag.name}</span>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => simulateTagChange(tag.name, false)}
                            className={`px-2 py-1 rounded text-xs transition-colors ${
                              !tag.value 
                                ? 'bg-red-600 text-white' 
                                : 'bg-gray-600 hover:bg-red-600 text-gray-300'
                            }`}
                          >
                            FALSE
                          </button>
                          <button
                            onClick={() => simulateTagChange(tag.name, true)}
                            className={`px-2 py-1 rounded text-xs transition-colors ${
                              tag.value 
                                ? 'bg-green-600 text-white' 
                                : 'bg-gray-600 hover:bg-green-600 text-gray-300'
                            }`}
                          >
                            TRUE
                          </button>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="bg-gray-800 border-t border-gray-700 p-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span className="text-gray-400">Highlighted Elements:</span>
            <span className="text-white">{highlightedElements.size}</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-400">Active Paths:</span>
            <span className="text-white">{signalPaths.filter(p => p.active).length}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {tracingActive ? (
              <Activity className="w-4 h-4 text-green-400 animate-pulse" />
            ) : (
              <EyeOff className="w-4 h-4 text-gray-400" />
            )}
            <span className={tracingActive ? 'text-green-400' : 'text-gray-400'}>
              {tracingActive ? 'Tracing Active' : 'Tracing Stopped'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignalTracer;