import React, { useState, useEffect } from 'react';
import { syncEngine } from '../../services/syncEngine';
import { 
  Wifi, 
  WifiOff, 
  Cloud, 
  CloudOff, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle2,
  Clock,
  Download,
  Upload
} from 'lucide-react';

interface SyncStatus {
  isOnline: boolean;
  pendingOperations: number;
  syncInProgress: boolean;
  lastSync: Date | null;
}

const SyncStatusBar: React.FC = () => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: navigator.onLine,
    pendingOperations: 0,
    syncInProgress: false,
    lastSync: null
  });
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const updateStatus = () => {
      setSyncStatus(syncEngine.getSyncStatus());
    };

    // Update status every second
    const interval = setInterval(updateStatus, 1000);

    // Listen for sync events
    const handleSyncStart = () => {
      setSyncStatus(prev => ({ ...prev, syncInProgress: true }));
    };

    const handleSyncComplete = () => {
      setSyncStatus(prev => ({ 
        ...prev, 
        syncInProgress: false,
        lastSync: new Date()
      }));
    };

    const handleSyncConflict = (event: CustomEvent) => {
      // Handle sync conflicts
      console.log('Sync conflict detected:', event.detail);
    };

    window.addEventListener('sync-start', handleSyncStart);
    window.addEventListener('sync-complete', handleSyncComplete);
    window.addEventListener('sync-conflict', handleSyncConflict as EventListener);

    return () => {
      clearInterval(interval);
      window.removeEventListener('sync-start', handleSyncStart);
      window.removeEventListener('sync-complete', handleSyncComplete);
      window.removeEventListener('sync-conflict', handleSyncConflict as EventListener);
    };
  }, []);

  const handleManualSync = () => {
    syncEngine.triggerSync();
  };

  const getStatusColor = () => {
    if (!syncStatus.isOnline) return 'text-red-400';
    if (syncStatus.syncInProgress) return 'text-yellow-400';
    if (syncStatus.pendingOperations > 0) return 'text-orange-400';
    return 'text-green-400';
  };

  const getStatusIcon = () => {
    if (!syncStatus.isOnline) return <WifiOff className="w-4 h-4" />;
    if (syncStatus.syncInProgress) return <RefreshCw className="w-4 h-4 animate-spin" />;
    if (syncStatus.pendingOperations > 0) return <Upload className="w-4 h-4" />;
    return <CheckCircle2 className="w-4 h-4" />;
  };

  const getStatusText = () => {
    if (!syncStatus.isOnline) return 'Offline';
    if (syncStatus.syncInProgress) return 'Syncing...';
    if (syncStatus.pendingOperations > 0) return `${syncStatus.pendingOperations} pending`;
    return 'Synced';
  };

  const formatLastSync = () => {
    if (!syncStatus.lastSync) return 'Never';
    
    const now = new Date();
    const diff = now.getTime() - syncStatus.lastSync.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    return syncStatus.lastSync.toLocaleDateString();
  };

  return (
    <div className="relative">
      <button
        onClick={() => setShowDetails(!showDetails)}
        className={`flex items-center space-x-2 px-3 py-1 rounded transition-colors hover:bg-gray-800 ${getStatusColor()}`}
      >
        {getStatusIcon()}
        <span className="text-sm">{getStatusText()}</span>
        {syncStatus.isOnline && (
          <Cloud className="w-3 h-3" />
        )}
      </button>

      {showDetails && (
        <div className="absolute top-10 right-0 mb-2 w-80 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-white font-semibold">Sync Status</h3>
              <button
                onClick={handleManualSync}
                disabled={syncStatus.syncInProgress || !syncStatus.isOnline}
                className="p-1 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                title="Manual sync"
              >
                <RefreshCw className={`w-4 h-4 ${syncStatus.syncInProgress ? 'animate-spin' : ''}`} />
              </button>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">Connection</span>
                <div className="flex items-center space-x-2">
                  {syncStatus.isOnline ? (
                    <>
                      <Wifi className="w-4 h-4 text-green-400" />
                      <span className="text-green-400">Online</span>
                    </>
                  ) : (
                    <>
                      <WifiOff className="w-4 h-4 text-red-400" />
                      <span className="text-red-400">Offline</span>
                    </>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-400">Pending Changes</span>
                <span className={`${syncStatus.pendingOperations > 0 ? 'text-orange-400' : 'text-green-400'}`}>
                  {syncStatus.pendingOperations}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-gray-400">Last Sync</span>
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3 text-gray-400" />
                  <span className="text-white text-sm">{formatLastSync()}</span>
                </div>
              </div>

              {syncStatus.syncInProgress && (
                <div className="bg-yellow-600/20 border border-yellow-600/30 rounded p-2">
                  <div className="flex items-center space-x-2 text-yellow-400 text-sm">
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    <span>Synchronizing changes...</span>
                  </div>
                </div>
              )}

              {!syncStatus.isOnline && syncStatus.pendingOperations > 0 && (
                <div className="bg-orange-600/20 border border-orange-600/30 rounded p-2">
                  <div className="flex items-center space-x-2 text-orange-400 text-sm">
                    <AlertTriangle className="w-4 h-4" />
                    <span>Changes will sync when online</span>
                  </div>
                </div>
              )}

              {syncStatus.isOnline && syncStatus.pendingOperations === 0 && (
                <div className="bg-green-600/20 border border-green-600/30 rounded p-2">
                  <div className="flex items-center space-x-2 text-green-400 text-sm">
                    <CheckCircle2 className="w-4 h-4" />
                    <span>All changes synchronized</span>
                  </div>
                </div>
              )}
            </div>

            <div className="mt-4 pt-3 border-t border-gray-700">
              <div className="text-xs text-gray-400">
                <p>• Changes are automatically saved locally</p>
                <p>• Sync occurs every 30 seconds when online</p>
                <p>• Conflicts are resolved automatically when possible</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SyncStatusBar;