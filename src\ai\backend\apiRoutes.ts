// Express.js API routes for AI backend services
import express from 'express';
import { llmClient } from './llmClient';
import { auditLogger } from './auditLogger';
import { rateLimiter } from './rateLimiter';
import { aiJobQueue } from './jobQueue';
import { promptCache } from './promptCache';
import { digitalSignatureService } from './digitalSignature';
import { usageMeteringService } from './usageMetering';
import { feedbackCollector } from './feedbackCollector';

const router = express.Router();

// Middleware for authentication and rate limiting
const authenticateUser = async (req: any, res: any, next: any) => {
  // In production, validate JWT token
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  
  // Mock user for demo
  req.user = { id: 'user-1', plan: 'pro', organizationId: 'org-1' };
  next();
};

const checkRateLimit = async (req: any, res: any, next: any) => {
  const { user } = req;
  const result = await rateLimiter.checkRateLimit(user.id, user.plan);
  
  if (!result.allowed) {
    return res.status(429).json({
      error: 'Rate limit exceeded',
      retryAfter: result.resetTime,
      remaining: result.remaining
    });
  }
  
  res.setHeader('X-RateLimit-Remaining', result.remaining.toString());
  res.setHeader('X-RateLimit-Reset', result.resetTime.toString());
  next();
};

// AI Request Endpoint
router.post('/request', authenticateUser, checkRateLimit, async (req, res) => {
  try {
    const { type, prompt, context, model = 'gpt-3.5-turbo' } = req.body;
    const { user } = req;

    // Check usage limits
    const usageCheck = await usageMeteringService.checkUsageLimits(
      user.id, 
      user.plan, 
      model, 
      type
    );

    if (!usageCheck.allowed) {
      return res.status(403).json({ error: usageCheck.reason });
    }

    // Check cache first
    const cached = await promptCache.getCachedResponse(prompt, context);
    if (cached) {
      return res.json({
        id: `cached_${Date.now()}`,
        content: cached.response,
        confidence: cached.confidence,
        cached: true,
        timestamp: new Date()
      });
    }

    // Process request
    const response = await llmClient.invoke(model, prompt, { temperature: 0.7 });
    
    // Sign the response
    const signedResponse = await digitalSignatureService.signAIOutput(
      response.content,
      { model, type, userId: user.id }
    );

    // Cache the response
    await promptCache.cacheResponse(
      prompt,
      context,
      response.content,
      response.confidence,
      { model, type }
    );

    // Record usage
    await usageMeteringService.recordUsage(
      user.id,
      model,
      type,
      response.content.length / 4, // Rough token estimate
      user.organizationId
    );

    // Audit log
    await auditLogger.logAIInteraction({
      user_id: user.id,
      session_id: req.sessionID || 'unknown',
      prompt_hash: require('crypto').createHash('sha256').update(prompt).digest('hex'),
      response_hash: require('crypto').createHash('sha256').update(response.content).digest('hex'),
      model_used: model,
      confidence_score: response.confidence,
      ip_address: req.ip,
      request_type: type,
      safety_validated: response.validation?.isValid || false,
      compliance_issues: response.validation?.complianceIssues || []
    });

    res.json({
      ...response,
      signature: signedResponse.signature
    });

  } catch (error) {
    console.error('AI request failed:', error);
    res.status(500).json({ 
      error: 'AI request failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Streaming AI Request Endpoint
router.post('/stream', authenticateUser, checkRateLimit, async (req, res) => {
  try {
    const { prompt, context, model = 'gpt-3.5-turbo' } = req.body;
    const { user } = req;

    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    const stream = llmClient.stream(model, prompt);
    
    for await (const chunk of stream) {
      res.write(`data: ${JSON.stringify({ chunk })}\n\n`);
    }
    
    res.write('data: [DONE]\n\n');
    res.end();

  } catch (error) {
    console.error('Streaming failed:', error);
    res.status(500).json({ error: 'Streaming failed' });
  }
});

// Job Queue Endpoints
router.post('/jobs', authenticateUser, async (req, res) => {
  try {
    const { request, priority = 'normal' } = req.body;
    const { user } = req;

    const jobId = await aiJobQueue.addJob(request, user.id, priority);
    res.json({ jobId });

  } catch (error) {
    res.status(500).json({ error: 'Failed to queue job' });
  }
});

router.get('/jobs/:jobId', authenticateUser, async (req, res) => {
  try {
    const { jobId } = req.params;
    const job = await aiJobQueue.getJob(jobId);
    
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }

    res.json(job);

  } catch (error) {
    res.status(500).json({ error: 'Failed to get job' });
  }
});

// Feedback Endpoints
router.post('/feedback', authenticateUser, async (req, res) => {
  try {
    const { suggestionId, rating, category, comment, context } = req.body;
    const { user } = req;

    const feedbackId = await feedbackCollector.submitFeedback({
      userId: user.id,
      suggestionId,
      rating,
      category,
      comment,
      context
    });

    res.json({ feedbackId });

  } catch (error) {
    res.status(500).json({ error: 'Failed to submit feedback' });
  }
});

// Usage Analytics Endpoints
router.get('/usage', authenticateUser, async (req, res) => {
  try {
    const { user } = req;
    const { period = 'monthly' } = req.query;

    const usage = await usageMeteringService.getUserUsage(
      user.id, 
      period as 'daily' | 'monthly'
    );

    res.json(usage);

  } catch (error) {
    res.status(500).json({ error: 'Failed to get usage data' });
  }
});

// Audit Endpoints
router.get('/audit', authenticateUser, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const report = await auditLogger.generateComplianceReport(
      startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate ? new Date(endDate as string) : new Date()
    );

    res.json(report);

  } catch (error) {
    res.status(500).json({ error: 'Failed to generate audit report' });
  }
});

// Cache Management Endpoints
router.get('/cache/stats', authenticateUser, async (req, res) => {
  try {
    const stats = promptCache.getStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get cache stats' });
  }
});

router.delete('/cache', authenticateUser, async (req, res) => {
  try {
    await promptCache.clear();
    res.json({ message: 'Cache cleared' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to clear cache' });
  }
});

// Health Check
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      llm: 'operational',
      cache: 'operational',
      queue: 'operational',
      audit: 'operational'
    }
  });
});

export default router;