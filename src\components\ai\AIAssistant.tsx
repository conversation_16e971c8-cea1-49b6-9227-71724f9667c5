import React, { useState, useRef, useEffect } from 'react';
import { usePLCStore } from '../../store/plcStore';
import { MessageSquare, Send, Sparkles, Code, Lightbulb, Bug } from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  context?: string;
}

const AIAssistant: React.FC = () => {
  const { currentProject, activeProgram } = usePLCStore();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: 'Hello! I\'m your PLC programming assistant. I can help you understand ladder logic, optimize your code, debug issues, and explain IEC 61131-3 standards. What would you like to know?',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const generateAIResponse = async (userMessage: string): Promise<string> => {
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const lowerMessage = userMessage.toLowerCase();
    
    // Context-aware responses based on current program
    const program = currentProject?.programs.find(p => p.id === activeProgram);
    const programContext = program ? `Current program: ${program.name} (${program.type})` : '';

    // Pattern matching for common PLC questions
    if (lowerMessage.includes('emergency stop') || lowerMessage.includes('e-stop')) {
      return `For emergency stop implementation, consider these best practices:

• Use normally closed (NC) contacts for emergency stop buttons
• Implement hardware safety circuits separate from PLC logic
• Add diagnostic feedback to verify safety circuit integrity
• Consider using safety-rated PLCs for critical applications
• Test emergency stops regularly as part of safety procedures

${programContext ? `In your ${program?.name}, I notice you should verify the emergency stop logic uses proper NC contacts and includes fault detection.` : ''}`;
    }

    if (lowerMessage.includes('ladder logic') || lowerMessage.includes('rung')) {
      return `Ladder logic fundamentals:

• Each rung represents a logical statement
• Power flows from left (input conditions) to right (outputs)
• Contacts represent input conditions (normally open/closed)
• Coils represent outputs or internal memory
• Use parallel branches for OR logic, series for AND logic

${programContext ? `Your current ladder program has ${Array.isArray(program?.content) ? program?.content.length : 0} rungs. Would you like me to analyze any specific rung?` : ''}`;
    }

    if (lowerMessage.includes('timer') || lowerMessage.includes('ton')) {
      return `Timer instructions (TON - Timer On Delay):

• TON starts timing when input goes TRUE
• Output goes TRUE after preset time expires
• Common uses: motor start delays, alarm delays
• Always reset timer when not needed to avoid confusion
• Consider using retentive timers (RTO) for applications requiring memory

Example:
\`\`\`
Timer_1(IN := Start_Condition, PT := T#5S);
Output := Timer_1.Q;
\`\`\``;
    }

    if (lowerMessage.includes('optimize') || lowerMessage.includes('improve')) {
      return `Code optimization suggestions:

• Minimize scan time by organizing frequently used logic first
• Use function blocks for reusable code
• Avoid unnecessary data type conversions
• Group related I/O operations together
• Use structured programming with clear naming conventions
• Comment your logic for maintainability

${programContext ? `For your ${program?.name}, I can analyze specific areas for optimization. Share the code section you'd like me to review.` : ''}`;
    }

    if (lowerMessage.includes('debug') || lowerMessage.includes('troubleshoot')) {
      return `Debugging PLC programs:

• Use online monitoring to watch tag values in real-time
• Check input/output status indicators
• Verify wiring and field device connections
• Use forcing cautiously and only for testing
• Check scan time and CPU diagnostics
• Review fault logs and diagnostic buffers

Common issues:
- Inputs not updating: Check wiring, power supply
- Outputs not activating: Verify logic, check output modules
- Intermittent faults: Look for loose connections, noise`;
    }

    if (lowerMessage.includes('safety') || lowerMessage.includes('sil')) {
      return `Safety considerations in PLC programming:

• Follow IEC 61508/61511 for safety instrumented systems
• Use proven-in-use safety functions
• Implement 1oo2 (1 out of 2) voting for critical functions
• Regular proof testing of safety functions
• Separate safety logic from process control logic
• Document safety requirements and validation

For SIL-rated applications, consider dedicated safety PLCs with certified function blocks.`;
    }

    // Default responses for general queries
    const responses = [
      `That's an interesting question about PLC programming. Could you provide more specific details about what you're trying to accomplish?`,
      
      `Based on your question, I'd recommend checking the IEC 61131-3 standard for best practices. What specific aspect would you like me to explain further?`,
      
      `For PLC programming, it's important to consider both functionality and safety. What type of application are you working on?`,
      
      `I can help you with ladder logic, structured text, function blocks, or general PLC concepts. What would you like to focus on?`
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    try {
      const response = await generateAIResponse(inputValue);
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'I apologize, but I encountered an error processing your request. Please try again.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const quickActions = [
    { icon: Code, label: 'Explain Logic', prompt: 'Can you explain the logic in my current program?' },
    { icon: Lightbulb, label: 'Optimize Code', prompt: 'How can I optimize my PLC code for better performance?' },
    { icon: Bug, label: 'Debug Help', prompt: 'I\'m having trouble debugging my PLC program. Can you help?' },
    { icon: Sparkles, label: 'Best Practices', prompt: 'What are the best practices for PLC programming?' }
  ];

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
            <MessageSquare className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-white">AI Assistant</h3>
            <p className="text-xs text-gray-400">PLC Programming Expert</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map(message => (
          <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-3xl ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
              <div className={`flex items-start space-x-3 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  message.type === 'user' 
                    ? 'bg-blue-600' 
                    : 'bg-gradient-to-r from-purple-500 to-blue-500'
                }`}>
                  {message.type === 'user' ? (
                    <span className="text-xs text-white font-semibold">U</span>
                  ) : (
                    <MessageSquare className="w-4 h-4 text-white" />
                  )}
                </div>
                
                <div className={`rounded-lg p-3 ${
                  message.type === 'user'
                    ? 'bg-blue-600/20 border border-blue-500/30'
                    : 'bg-gray-800 border border-gray-700'
                }`}>
                  <div className="text-sm text-gray-300 whitespace-pre-wrap">
                    {message.content}
                  </div>
                  <div className="text-xs text-gray-500 mt-2">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div className="flex justify-start">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                <MessageSquare className="w-4 h-4 text-white" />
              </div>
              <div className="bg-gray-800 border border-gray-700 rounded-lg p-3">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      {messages.length <= 1 && (
        <div className="p-4 border-t border-gray-800">
          <div className="mb-3">
            <h4 className="text-sm font-semibold text-white mb-2">Quick Actions</h4>
            <div className="grid grid-cols-2 gap-2">
              {quickActions.map((action, index) => {
                const IconComponent = action.icon;
                return (
                  <button
                    key={index}
                    onClick={() => setInputValue(action.prompt)}
                    className="flex items-center space-x-2 p-2 bg-gray-800/50 hover:bg-gray-800 border border-gray-700 rounded transition-colors text-left"
                  >
                    <IconComponent className="w-4 h-4 text-purple-400" />
                    <span className="text-sm text-gray-300">{action.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Input */}
      <div className="border-t border-gray-800 p-4">
        <div className="flex items-end space-x-2">
          <div className="flex-1">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask about your PLC code, debugging, or best practices..."
              className="w-full bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white resize-none focus:border-purple-500 focus:outline-none"
              rows={1}
              style={{ minHeight: '40px', maxHeight: '120px' }}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isTyping}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700 disabled:cursor-not-allowed text-white p-2 rounded-lg transition-colors"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIAssistant;