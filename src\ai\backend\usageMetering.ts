export interface UsageMetrics {
  userId: string;
  organizationId?: string;
  period: 'daily' | 'monthly';
  aiRequests: number;
  tokensConsumed: number;
  costEstimate: number;
  modelUsage: Record<string, number>;
  featureUsage: Record<string, number>;
  timestamp: Date;
}

export interface UsageLimits {
  maxRequestsPerDay: number;
  maxRequestsPerMonth: number;
  maxTokensPerDay: number;
  maxTokensPerMonth: number;
  allowedModels: string[];
  allowedFeatures: string[];
}

class UsageMeteringService {
  private usage: Map<string, UsageMetrics> = new Map();
  private limits: Map<string, UsageLimits> = new Map();

  constructor() {
    this.setupDefaultLimits();
  }

  private setupDefaultLimits(): void {
    // Free tier limits
    this.limits.set('free', {
      maxRequestsPerDay: 50,
      maxRequestsPerMonth: 1000,
      maxTokensPerDay: 10000,
      maxTokensPerMonth: 200000,
      allowedModels: ['gpt-3.5-turbo'],
      allowedFeatures: ['explain', 'suggest']
    });

    // Pro tier limits
    this.limits.set('pro', {
      maxRequestsPerDay: 500,
      maxRequestsPerMonth: 10000,
      maxTokensPerDay: 100000,
      maxTokensPerMonth: 2000000,
      allowedModels: ['gpt-3.5-turbo', 'gpt-4'],
      allowedFeatures: ['explain', 'suggest', 'generate', 'refactor']
    });

    // Enterprise tier limits
    this.limits.set('enterprise', {
      maxRequestsPerDay: -1, // unlimited
      maxRequestsPerMonth: -1,
      maxTokensPerDay: -1,
      maxTokensPerMonth: -1,
      allowedModels: ['gpt-3.5-turbo', 'gpt-4', 'claude-3-opus'],
      allowedFeatures: ['explain', 'suggest', 'generate', 'refactor', 'debug']
    });
  }

  async recordUsage(
    userId: string,
    model: string,
    feature: string,
    tokensUsed: number,
    organizationId?: string
  ): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    const monthKey = new Date().toISOString().slice(0, 7); // YYYY-MM

    // Daily usage
    const dailyKey = `${userId}:daily:${today}`;
    await this.updateUsageMetrics(dailyKey, userId, 'daily', model, feature, tokensUsed, organizationId);

    // Monthly usage
    const monthlyKey = `${userId}:monthly:${monthKey}`;
    await this.updateUsageMetrics(monthlyKey, userId, 'monthly', model, feature, tokensUsed, organizationId);
  }

  private async updateUsageMetrics(
    key: string,
    userId: string,
    period: 'daily' | 'monthly',
    model: string,
    feature: string,
    tokensUsed: number,
    organizationId?: string
  ): Promise<void> {
    let metrics = this.usage.get(key);

    if (!metrics) {
      metrics = {
        userId,
        organizationId,
        period,
        aiRequests: 0,
        tokensConsumed: 0,
        costEstimate: 0,
        modelUsage: {},
        featureUsage: {},
        timestamp: new Date()
      };
    }

    // Update metrics
    metrics.aiRequests++;
    metrics.tokensConsumed += tokensUsed;
    metrics.modelUsage[model] = (metrics.modelUsage[model] || 0) + 1;
    metrics.featureUsage[feature] = (metrics.featureUsage[feature] || 0) + 1;
    metrics.costEstimate = this.calculateCost(metrics.tokensConsumed, metrics.modelUsage);
    metrics.timestamp = new Date();

    this.usage.set(key, metrics);
  }

  async checkUsageLimits(userId: string, userPlan: string, model: string, feature: string): Promise<{
    allowed: boolean;
    reason?: string;
    dailyUsage: UsageMetrics | null;
    monthlyUsage: UsageMetrics | null;
  }> {
    const limits = this.limits.get(userPlan);
    if (!limits) {
      return { allowed: false, reason: 'Invalid user plan', dailyUsage: null, monthlyUsage: null };
    }

    // Check model access
    if (!limits.allowedModels.includes(model)) {
      return { 
        allowed: false, 
        reason: `Model ${model} not available in ${userPlan} plan`,
        dailyUsage: null,
        monthlyUsage: null
      };
    }

    // Check feature access
    if (!limits.allowedFeatures.includes(feature)) {
      return { 
        allowed: false, 
        reason: `Feature ${feature} not available in ${userPlan} plan`,
        dailyUsage: null,
        monthlyUsage: null
      };
    }

    const today = new Date().toISOString().split('T')[0];
    const monthKey = new Date().toISOString().slice(0, 7);

    const dailyUsage = this.usage.get(`${userId}:daily:${today}`);
    const monthlyUsage = this.usage.get(`${userId}:monthly:${monthKey}`);

    // Check daily limits
    if (limits.maxRequestsPerDay > 0 && dailyUsage && dailyUsage.aiRequests >= limits.maxRequestsPerDay) {
      return { 
        allowed: false, 
        reason: 'Daily request limit exceeded',
        dailyUsage,
        monthlyUsage
      };
    }

    if (limits.maxTokensPerDay > 0 && dailyUsage && dailyUsage.tokensConsumed >= limits.maxTokensPerDay) {
      return { 
        allowed: false, 
        reason: 'Daily token limit exceeded',
        dailyUsage,
        monthlyUsage
      };
    }

    // Check monthly limits
    if (limits.maxRequestsPerMonth > 0 && monthlyUsage && monthlyUsage.aiRequests >= limits.maxRequestsPerMonth) {
      return { 
        allowed: false, 
        reason: 'Monthly request limit exceeded',
        dailyUsage,
        monthlyUsage
      };
    }

    if (limits.maxTokensPerMonth > 0 && monthlyUsage && monthlyUsage.tokensConsumed >= limits.maxTokensPerMonth) {
      return { 
        allowed: false, 
        reason: 'Monthly token limit exceeded',
        dailyUsage,
        monthlyUsage
      };
    }

    return { allowed: true, dailyUsage, monthlyUsage };
  }

  async getUserUsage(userId: string, period: 'daily' | 'monthly' = 'monthly'): Promise<UsageMetrics | null> {
    const key = period === 'daily' 
      ? `${userId}:daily:${new Date().toISOString().split('T')[0]}`
      : `${userId}:monthly:${new Date().toISOString().slice(0, 7)}`;
    
    return this.usage.get(key) || null;
  }

  async getOrganizationUsage(organizationId: string, period: 'daily' | 'monthly' = 'monthly'): Promise<UsageMetrics[]> {
    const results: UsageMetrics[] = [];
    const periodKey = period === 'daily' 
      ? new Date().toISOString().split('T')[0]
      : new Date().toISOString().slice(0, 7);

    for (const [key, metrics] of this.usage.entries()) {
      if (metrics.organizationId === organizationId && 
          metrics.period === period && 
          key.includes(periodKey)) {
        results.push(metrics);
      }
    }

    return results;
  }

  private calculateCost(tokens: number, modelUsage: Record<string, number>): number {
    // Simplified cost calculation (per 1K tokens)
    const modelCosts = {
      'gpt-3.5-turbo': 0.002,
      'gpt-4': 0.03,
      'claude-3-opus': 0.015,
      'claude-3-sonnet': 0.003
    };

    let totalCost = 0;
    for (const [model, usage] of Object.entries(modelUsage)) {
      const costPerToken = (modelCosts[model as keyof typeof modelCosts] || 0.002) / 1000;
      totalCost += usage * costPerToken * (tokens / Object.values(modelUsage).reduce((a, b) => a + b, 1));
    }

    return totalCost;
  }

  // Cleanup old usage data
  cleanup(): void {
    const cutoff = new Date();
    cutoff.setMonth(cutoff.getMonth() - 3); // Keep 3 months of data

    for (const [key, metrics] of this.usage.entries()) {
      if (metrics.timestamp < cutoff) {
        this.usage.delete(key);
      }
    }
  }
}

export const usageMeteringService = new UsageMeteringService();

// Cleanup old data daily
setInterval(() => {
  usageMeteringService.cleanup();
}, 24 * 60 * 60 * 1000);