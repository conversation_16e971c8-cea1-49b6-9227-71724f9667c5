// Express.js API routes for AI backend services
import express, { RequestH<PERSON><PERSON>, Request as ExpressRequest, NextFunction } from 'express';
import { llmClient } from './llmClient';
import { auditLogger } from './auditLogger';
import { rateLimiter } from './rateLimiter';
import { aiJobQueue } from './jobQueue';
import { promptCache } from './promptCache';
import { digitalSignatureService } from './digitalSignature';
import { usageMeteringService } from './usageMetering';
import { feedbackCollector } from './feedbackCollector';
import { monitoringService } from './monitoring';
import { requestBatcher } from './requestBatcher';
import { responseOptimizer } from './responseOptimizer';
import { AIResponse, ValidationResult } from '../hooks/useAI';

const router = express.Router();

// Middleware for authentication and rate limiting
interface AuthenticatedRequest extends ExpressRequest {
  user?: { id: string; plan: string; organizationId: string };
}

const authenticateUser: RequestHandler = async (
  req: ExpressRequest,
  res: express.Response,
  next: NextFunction
) => {
  const req2 = req as Request;
  // In production, validate JWT token
  const token = req2.headers.authorization?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // For development, accept 'dev-token'
  if (process.env.NODE_ENV === 'development' && token === 'dev-token') {
    (req2 as any).user = {
      id: 'dev-user-1',
      plan: 'pro',
      organizationId: 'dev-org-1'
    };
    return next();
  }

  // Mock user for demo (remove in production)
  if (token === 'test-token' || token === 'dev-token') {
    (req2 as any).user = { 
      id: 'user-1', 
      plan: 'pro', 
      organizationId: 'org-1' 
    };
    return next();
  }

  // TODO: Implement proper JWT validation in production
  return res.status(401).json({ error: 'Invalid token' });
};

const checkRateLimit: RequestHandler = async (
  req: ExpressRequest,
  res: express.Response,
  next: NextFunction
) => {
  const req2 = req as Request;
  const { user } = req2;
  if (!user) {
    return res.status(401).json({ error: 'User not authenticated' });
  }
  const result = await rateLimiter.checkRateLimit(user.id, user.plan);

  if (!result.allowed) {
    return res.status(429).json({
      error: 'Rate limit exceeded',
      retryAfter: result.resetTime,
      remaining: result.remaining
    });
  }

  res.setHeader('X-RateLimit-Remaining', result.remaining.toString());
  res.setHeader('X-RateLimit-Reset', result.resetTime.toString());
  next();
};

// Add request batching middleware
const batchSimilarRequests: RequestHandler = async (
  req: ExpressRequest,
  res: express.Response,
  next: NextFunction
) => {
  const req2 = req as Request;
  const batchResult = await requestBatcher.tryBatch(req2.body);
  if (batchResult.batched) {
    return res.json(batchResult.response);
  }
  next();
};

function sanitizeInput(input: string): string {
  // Implement input sanitization logic here
  // This is a placeholder - replace with actual sanitization
  return input.replace(/</g, '<').replace(/>/g, '>');
}

// AI Request Endpoint
router.post('/request',
  authenticateUser,
  checkRateLimit,
  batchSimilarRequests, // Add batching
  async (req: Request, res: express.Response, next: NextFunction) => {
    try {
      // Validate request body
      const { type, prompt, context, model = 'gpt-3.5-turbo' } = (req as any).body;

      if (!type || !prompt || !context) {
        return res.status(400).json({ error: 'Missing required parameters' });
      }

      // Sanitize input
      const sanitizedPrompt = sanitizeInput ? sanitizeInput(prompt) : prompt;
      const sanitizedContext = sanitizeInput ? sanitizeInput(JSON.stringify(context)) : JSON.stringify(context);

      const { user } = req;

      // Check usage limits
      const usageCheck = await usageMeteringService.checkUsageLimits(
        user?.id || 'unknown',
        user?.plan || 'basic',
        model,
        type
      );

      if (!usageCheck.allowed) {
        return res.status(403).json({ error: usageCheck.reason });
      }

      // Add request deduplication
      const requestId = require('crypto')
        .createHash('sha256')
        .update(JSON.stringify({ prompt, context, model }))
        .digest('hex');

      // Check for duplicate in-flight requests
      const inFlightResponse = await requestBatcher.getInFlightRequest(requestId);
      if (inFlightResponse) {
        return res.json(inFlightResponse);
      }

      // Check cache first
      const cached = await promptCache.getCachedResponse(prompt, context);
      if (cached) {
        return res.json({
          id: `cached_${Date.now()}`,
          content: cached.response,
          confidence: cached.confidence,
          cached: true,
          timestamp: new Date()
        });
      }

      // Optimize prompt before sending to LLM
      const optimizedPrompt = await responseOptimizer.optimizePrompt(prompt, context);

      // Process request with optimization
      const response: AIResponse = await llmClient.invoke(model, optimizedPrompt, {
        temperature: 0.7,
        maxTokens: responseOptimizer.calculateOptimalTokens(type, context)
      });

      // Sign the response
      const signedResponse = await digitalSignatureService.signAIOutput(
        response.content,
        { model, type, userId: user?.id }
      );

      // Cache the response
      await promptCache.cacheResponse(
        prompt,
        context,
        response?.content,
        response?.confidence,
        { model, type }
      );

      // Record usage
      await usageMeteringService.recordUsage(
        user?.id || 'unknown',
        model,
        type,
        response.content.length / 4, // Rough token estimate
        user?.organizationId
      );

      // Audit log
      await auditLogger.logAIInteraction({
        user_id: user?.id || 'unknown',
        session_id: req.sessionID || 'unknown',
        prompt_hash: require('crypto').createHash('sha256').update(prompt).digest('hex'),
        response_hash: require('crypto').createHash('sha256').update(response.content).digest('hex'),
        model_used: model,
        confidence_score: response.confidence,
        ip_address: req.ip || 'unknown',
        request_type: type,
        safety_validated: response.validation?.isValid || false,
        compliance_issues: response.validation?.complianceIssues || []
      });

      res.json({
        ...response,
        signature: signedResponse.signature
      });

    } catch (error: any) {
      // Enhanced error handling
      const { type, model } = req.body;
      const errorMessage = error instanceof Error ? error.message : String(error);
      await auditLogger.logError({
        userId: req.user?.id || 'unknown',
        error: errorMessage,
        stack: String(error),
        requestData: { type: (req as any).body.type, model: (req as any).body.model }
      });

      console.error('AI request failed:', error);
      res.status(500).json({
        error: 'AI request failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });


// Streaming AI Request Endpoint
router.post('/stream', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    const { prompt, context, model = 'gpt-3.5-turbo' } = req.body;
    const user = (req as any).user;

    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    const stream = llmClient.stream(model, prompt);

    for await (const chunk of stream) {
      res.write(`data: ${JSON.stringify({ chunk })}\n\n`);
    }

    res.write('data: [DONE]\n\n');
    res.end();

  } catch (error) {
    console.error('Streaming failed:', error);
    res.status(500).json({ error: 'Streaming failed' });
  }
});

// Job Queue Endpoints
router.post('/jobs', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    const { request, priority = 'normal' } = req.body;
    const user = (req as any).user;

    const jobId = await aiJobQueue.addJob(request, user.id, priority);
    res.json({ jobId });

  } catch (error) {
    res.status(500).json({ error: 'Failed to queue job' });
  }
});

// Job Queue Endpoints
router.get('/jobs/:jobId', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    const { jobId } = req.params;
    const user = (req as any).user;
    const job = await aiJobQueue.getJob(jobId);

    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }

    res.json(job);

  } catch (error) {
    res.status(500).json({ error: 'Failed to get job' });
  }
});

// Feedback Endpoints
router.post('/feedback', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    const { suggestionId, rating, category, comment, context } = req.body;
    const user = (req as any).user;

    const feedbackId = await feedbackCollector.submitFeedback({
      userId: user?.id || 'unknown',
      suggestionId,
      rating,
      category,
      comment,
      context
    });

    res.json({ feedbackId });

  } catch (error) {
    res.status(500).json({ error: 'Failed to submit feedback' });
  }
});

// Usage Analytics Endpoints
router.get('/usage', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    const user = (req as any).user;
    const { period = 'monthly' } = req.query;

    const usage = await usageMeteringService.getUserUsage(
      user?.id || 'unknown',
      period as 'daily' | 'monthly'
    );

    res.json(usage);

  } catch (error) {
    console.error('Usage analytics failed:', error);
    res.status(500).json({ error: 'Failed to get usage data' });
  }
});

// Audit Endpoints
router.get('/audit', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    const { startDate, endDate } = req.query;

    const report = await auditLogger.generateComplianceReport(
      startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate ? new Date(endDate as string) : new Date()
    );

    res.json(report);

  } catch (error) {
    res.status(500).json({ error: 'Failed to generate audit report' });
  }
});

// Cache Management Endpoints
router.get('/cache/stats', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    const stats = await promptCache.getStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get cache stats' });
  }
});

router.delete('/cache', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    await promptCache.clear();
    res.json({ message: 'Cache cleared' });
  } catch (error) {
    res.status(500).json({ error: 'Failed to clear cache' });
  }
});

// Health Check
router.get('/health', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    const health = await monitoringService.getHealthStatus();
    const statusCode = health?.status === 'healthy' ? 200 : 503;

    res.status(statusCode).json({
      ...health,
      timestamp: new Date().toISOString(),
      version: process.env.APP_VERSION || '1.0.0'
    });
  } catch (error: any) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Add detailed diagnostics endpoint
router.get('/diagnostics', authenticateUser, checkRateLimit, batchSimilarRequests, async (req: Request, res: express.Response, next: NextFunction) => {
  try {
    const diagnostics = {
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      },
      services: await monitoringService.getDetailedStatus(),
      performance: await monitoringService.getPerformanceMetrics()
    };

    res.json(diagnostics);
  } catch (error: any) {
    console.error('Diagnostics failed:', error);
    res.status(500).json({ error: 'Diagnostics failed' });
  }
});


export default router;