"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useAI = void 0;
const react_1 = require("react");
const aiClient_1 = require("../services/aiClient");
const mcpService_1 = require("../services/mcpService");
const MCPStore_1 = require("../context/MCPStore");
const useAI = () => {
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    const [history, setHistory] = (0, react_1.useState)([]);
    const [metrics, setMetrics] = (0, react_1.useState)({
        timeSaved: 0,
        linesGenerated: 0,
        suggestionsAccepted: 0,
        errorsAvoided: 0
    });
    const [lastPrompt, setLastPrompt] = (0, react_1.useState)('');
    const [requireReview, setRequireReview] = (0, react_1.useState)(false);
    // Get MCP context
    const { serializeForPrompt, getContextConfidence } = (0, MCPStore_1.useMCPContext)();
    const request = (0, react_1.useCallback)(async (request) => {
        setIsLoading(true);
        setError(null);
        const startTime = Date.now();
        try {
            // Try to enhance the prompt with MCP service
            let enhancedPrompt = '';
            if (request.prompt) {
                try {
                    const enhancementResponse = await mcpService_1.mcpService.enhancePrompt({
                        prompt: request.prompt,
                        contextTypes: ['program', 'tag', 'safety', 'standard']
                    });
                    enhancedPrompt = enhancementResponse.enhancedPrompt;
                    // Update the request with the enhanced prompt
                    request.prompt = enhancedPrompt;
                }
                catch (error) {
                    console.warn('Failed to enhance prompt with MCP service, using original prompt');
                }
            }
            const response = await aiClient_1.aiClient.request(request);
            const timeTaken = (Date.now() - startTime) / 1000;
            // Adjust confidence based on context quality
            const contextConfidence = getContextConfidence();
            const adjustedConfidence = (response.confidence + contextConfidence) / 2;
            const enhancedResponse = {
                ...response,
                confidence: adjustedConfidence,
                prompt: request.prompt,
                timeTaken
            };
            setHistory(prev => [...prev, enhancedResponse]);
            setLastPrompt(request.prompt || '');
            return enhancedResponse;
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'AI request failed';
            setError(errorMessage);
            throw err;
        }
        finally {
            setIsLoading(false);
        }
    }, [serializeForPrompt, getContextConfidence]);
    const explainCode = (0, react_1.useCallback)(async (code, language) => {
        return request({
            type: 'explain',
            context: { code, language },
            prompt: `Explain this ${language} code in simple terms for a PLC engineer`
        });
    }, [request]);
    const suggestRefactor = (0, react_1.useCallback)(async (code, language) => {
        return request({
            type: 'refactor',
            context: { code, language },
            prompt: 'Suggest improvements for better performance, safety, and maintainability'
        });
    }, [request]);
    const generateCode = (0, react_1.useCallback)(async (description, language) => {
        return request({
            type: 'generate',
            context: { description, language },
            prompt: `Generate ${language} code for: ${description}`
        });
    }, [request]);
    const suggestTags = (0, react_1.useCallback)(async (description) => {
        return request({
            type: 'suggest',
            context: { description },
            prompt: `Suggest appropriate PLC tag names and data types for: ${description}`
        });
    }, [request]);
    const debugLogic = (0, react_1.useCallback)(async (elements, issue) => {
        return request({
            type: 'debug',
            context: { elements, issue },
            prompt: `Help debug this ladder logic issue: ${issue}`
        });
    }, [request]);
    const acceptSuggestion = (0, react_1.useCallback)((responseId, estimatedTimeSaved = 0) => {
        setMetrics(prev => ({
            ...prev,
            suggestionsAccepted: prev.suggestionsAccepted + 1,
            timeSaved: prev.timeSaved + estimatedTimeSaved
        }));
        // Update history to mark as accepted
        setHistory(prev => prev.map(item => item.id === responseId ? { ...item, accepted: true } : item));
    }, []);
    const rejectSuggestion = (0, react_1.useCallback)((responseId) => {
        // Update history to mark as rejected
        setHistory(prev => prev.map(item => item.id === responseId ? { ...item, accepted: false } : item));
    }, []);
    const updateMetrics = (0, react_1.useCallback)((updates) => {
        setMetrics(prev => ({ ...prev, ...updates }));
    }, []);
    return {
        request,
        explainCode,
        suggestRefactor,
        generateCode,
        suggestTags,
        debugLogic,
        acceptSuggestion,
        rejectSuggestion,
        updateMetrics,
        isLoading,
        error,
        history,
        metrics,
        lastPrompt,
        requireReview,
        setRequireReview,
        clearHistory: () => setHistory([]),
        clearMetrics: () => setMetrics({
            timeSaved: 0,
            linesGenerated: 0,
            suggestionsAccepted: 0,
            errorsAvoided: 0
        })
    };
};
exports.useAI = useAI;
