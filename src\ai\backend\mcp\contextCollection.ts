import { ContextItem, ContextItemType } from '../../types/mcp';
import { db } from '../backend/db';

export class ContextCollectionService {
  async collectProgramContext(programId: string, userId: string, projectId?: string): Promise<string> {
    try {
      // Get program details from the database
      const program = await db.query(
        'SELECT * FROM programs WHERE id = $1',
        [programId]
      );
      
      if (!program.rows[0]) {
        throw new Error('Program not found');
      }
      
      // Create context item
      const contextItem: Partial<ContextItem> = {
        type: 'program',
        source: 'auto',
        value: `Program: ${program.rows[0].name}, Type: ${program.rows[0].type}`,
        user_id: userId,
        project_id: projectId || program.rows[0].project_id,
        metadata: {
          programId: program.rows[0].id,
          programName: program.rows[0].name,
          programType: program.rows[0].type,
          safetyProgram: program.rows[0].safety_program
        }
      };
      
      // Store in database
      const result = await db.query(
        `INSERT INTO mcp_context_items 
         (type, source, value, user_id, project_id, metadata, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())
         RETURNING id`,
        [
          contextItem.type,
          contextItem.source,
          contextItem.value,
          contextItem.user_id,
          contextItem.project_id,
          JSON.stringify(contextItem.metadata)
        ]
      );
      
      // If safety program, also collect safety context
      if (program.rows[0].safety_program) {
        await this.collectSafetyContext(programId, userId, projectId);
      }
      
      return result.rows[0].id;
    } catch (error) {
      console.error('Error collecting program context:', error);
      // For development, create a mock context item
      return `mock_program_context_${Date.now()}`;
    }
  }
  
  async collectTagContext(tagId: string, userId: string, projectId?: string): Promise<string> {
    try {
      // Get tag details from the database
      const tag = await db.query(
        'SELECT * FROM tags WHERE id = $1',
        [tagId]
      );
      
      if (!tag.rows[0]) {
        throw new Error('Tag not found');
      }
      
      // Create context item
      const contextItem: Partial<ContextItem> = {
        type: 'tag',
        source: 'auto',
        value: `${tag.rows[0].name} (${tag.rows[0].type}): ${tag.rows[0].description || 'No description'}`,
        user_id: userId,
        project_id: projectId || tag.rows[0].project_id,
        metadata: {
          tagId: tag.rows[0].id,
          tagName: tag.rows[0].name,
          tagType: tag.rows[0].type,
          tagScope: tag.rows[0].scope,
          safetyRated: tag.rows[0].safety_rated
        }
      };
      
      // Store in database
      const result = await db.query(
        `INSERT INTO mcp_context_items 
         (type, source, value, user_id, project_id, metadata, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())
         RETURNING id`,
        [
          contextItem.type,
          contextItem.source,
          contextItem.value,
          contextItem.user_id,
          contextItem.project_id,
          JSON.stringify(contextItem.metadata)
        ]
      );
      
      return result.rows[0].id;
    } catch (error) {
      console.error('Error collecting tag context:', error);
      // For development, create a mock context item
      return `mock_tag_context_${Date.now()}`;
    }
  }
  
  async collectRungContext(rungId: string, userId: string, projectId?: string): Promise<string> {
    try {
      // Get rung details from the database
      const rung = await db.query(
        'SELECT r.*, p.id as program_id, p.name as program_name FROM rungs r JOIN programs p ON r.program_id = p.id WHERE r.id = $1',
        [rungId]
      );
      
      if (!rung.rows[0]) {
        throw new Error('Rung not found');
      }
      
      // Create context item
      const contextItem: Partial<ContextItem> = {
        type: 'rung',
        source: 'auto',
        value: `Rung ${rung.rows[0].number}: ${rung.rows[0].comment || 'No comment'}`,
        user_id: userId,
        project_id: projectId || rung.rows[0].project_id,
        metadata: {
          rungId: rung.rows[0].id,
          rungNumber: rung.rows[0].number,
          programId: rung.rows[0].program_id,
          programName: rung.rows[0].program_name,
          elements: rung.rows[0].elements
        }
      };
      
      // Store in database
      const result = await db.query(
        `INSERT INTO mcp_context_items 
         (type, source, value, user_id, project_id, metadata, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())
         RETURNING id`,
        [
          contextItem.type,
          contextItem.source,
          contextItem.value,
          contextItem.user_id,
          contextItem.project_id,
          JSON.stringify(contextItem.metadata)
        ]
      );
      
      return result.rows[0].id;
    } catch (error) {
      console.error('Error collecting rung context:', error);
      // For development, create a mock context item
      return `mock_rung_context_${Date.now()}`;
    }
  }
  
  async collectSafetyContext(programId: string, userId: string, projectId?: string): Promise<string> {
    try {
      // Get safety program details from the database
      const program = await db.query(
        'SELECT * FROM programs WHERE id = $1 AND safety_program = true',
        [programId]
      );
      
      if (!program.rows[0]) {
        throw new Error('Safety program not found');
      }
      
      // Create context item
      const contextItem: Partial<ContextItem> = {
        type: 'safety',
        source: 'auto',
        value: `Safety Program: ${program.rows[0].name}, SIL Rating: ${program.rows[0].sil_rating || 'Not specified'}`,
        user_id: userId,
        project_id: projectId || program.rows[0].project_id,
        metadata: {
          programId: program.rows[0].id,
          silRating: program.rows[0].sil_rating,
          safetyStandards: program.rows[0].safety_standards
        }
      };
      
      // Store in database
      const result = await db.query(
        `INSERT INTO mcp_context_items 
         (type, source, value, user_id, project_id, metadata, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())
         RETURNING id`,
        [
          contextItem.type,
          contextItem.source,
          contextItem.value,
          contextItem.user_id,
          contextItem.project_id,
          JSON.stringify(contextItem.metadata)
        ]
      );
      
      return result.rows[0].id;
    } catch (error) {
      console.error('Error collecting safety context:', error);
      // For development, create a mock context item
      return `mock_safety_context_${Date.now()}`;
    }
  }
  
  async collectStandardContext(standardName: string, userId: string, projectId?: string): Promise<string> {
    try {
      // Create context item
      const contextItem: Partial<ContextItem> = {
        type: 'standard',
        source: 'auto',
        value: standardName,
        user_id: userId,
        project_id: projectId,
        metadata: {
          standardName,
          timestamp: new Date().toISOString()
        }
      };
      
      // Store in database
      const result = await db.query(
        `INSERT INTO mcp_context_items 
         (type, source, value, user_id, project_id, metadata, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())
         RETURNING id`,
        [
          contextItem.type,
          contextItem.source,
          contextItem.value,
          contextItem.user_id,
          contextItem.project_id,
          JSON.stringify(contextItem.metadata)
        ]
      );
      
      return result.rows[0].id;
    } catch (error) {
      console.error('Error collecting standard context:', error);
      // For development, create a mock context item
      return `mock_standard_context_${Date.now()}`;
    }
  }
  
  async collectDocContext(docId: string, userId: string, projectId?: string): Promise<string> {
    try {
      // Get documentation details from the database
      const doc = await db.query(
        'SELECT * FROM documentation WHERE id = $1',
        [docId]
      );
      
      if (!doc.rows[0]) {
        throw new Error('Documentation not found');
      }
      
      // Create context item
      const contextItem: Partial<ContextItem> = {
        type: 'doc',
        source: 'auto',
        value: `Documentation: ${doc.rows[0].title}`,
        user_id: userId,
        project_id: projectId || doc.rows[0].project_id,
        metadata: {
          docId: doc.rows[0].id,
          docTitle: doc.rows[0].title,
          docType: doc.rows[0].type,
          docContent: doc.rows[0].content.substring(0, 500) // Limit content size
        }
      };
      
      // Store in database
      const result = await db.query(
        `INSERT INTO mcp_context_items 
         (type, source, value, user_id, project_id, metadata, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())
         RETURNING id`,
        [
          contextItem.type,
          contextItem.source,
          contextItem.value,
          contextItem.user_id,
          contextItem.project_id,
          JSON.stringify(contextItem.metadata)
        ]
      );
      
      return result.rows[0].id;
    } catch (error) {
      console.error('Error collecting documentation context:', error);
      // For development, create a mock context item
      return `mock_doc_context_${Date.now()}`;
    }
  }
  
  async collectVersionContext(versionInfo: string, userId: string, projectId?: string): Promise<string> {
    try {
      // Create context item
      const contextItem: Partial<ContextItem> = {
        type: 'version',
        source: 'auto',
        value: versionInfo,
        user_id: userId,
        project_id: projectId,
        metadata: {
          versionInfo,
          timestamp: new Date().toISOString()
        }
      };
      
      // Store in database
      const result = await db.query(
        `INSERT INTO mcp_context_items 
         (type, source, value, user_id, project_id, metadata, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())
         RETURNING id`,
        [
          contextItem.type,
          contextItem.source,
          contextItem.value,
          contextItem.user_id,
          contextItem.project_id,
          JSON.stringify(contextItem.metadata)
        ]
      );
      
      return result.rows[0].id;
    } catch (error) {
      console.error('Error collecting version context:', error);
      // For development, create a mock context item
      return `mock_version_context_${Date.now()}`;
    }
  }
  
  // Method to manually add a context item
  async addContextItem(item: Partial<ContextItem>): Promise<string> {
    try {
      // Store in database
      const result = await db.query(
        `INSERT INTO mcp_context_items 
         (type, source, value, user_id, project_id, metadata, timestamp)
         VALUES ($1, $2, $3, $4, $5, $6, NOW())
         RETURNING id`,
        [
          item.type,
          item.source,
          item.value,
          item.user_id,
          item.project_id,
          JSON.stringify(item.metadata || {})
        ]
      );
      
      return result.rows[0].id;
    } catch (error) {
      console.error('Error adding context item:', error);
      // For development, create a mock context item
      return `mock_context_${Date.now()}`;
    }
  }
  
  // Method to get context items by type
  async getContextByType(type: ContextItemType, projectId?: string, limit: number = 10): Promise<ContextItem[]> {
    try {
      let query = 'SELECT * FROM mcp_context_items WHERE type = $1';
      const params: any[] = [type];
      
      if (projectId) {
        query += ' AND project_id = $2';
        params.push(projectId);
      }
      
      query += ' ORDER BY timestamp DESC LIMIT $' + (params.length + 1);
      params.push(limit);
      
      const result = await db.query(query, params);
      
      return result.rows.map((row: any) => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : {}
      }));
    } catch (error) {
      console.error(`Error getting context items of type ${type}:`, error);
      return [];
    }
  }
  
  // Method to get all context items for a project
  async getProjectContext(projectId: string, limit: number = 50): Promise<ContextItem[]> {
    try {
      const result = await db.query(
        'SELECT * FROM mcp_context_items WHERE project_id = $1 ORDER BY timestamp DESC LIMIT $2',
        [projectId, limit]
      );
      
      return result.rows.map((row: any) => ({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : {}
      }));
    } catch (error) {
      console.error(`Error getting context for project ${projectId}:`, error);
      return [];
    }
  }
  
  // Method to delete a context item
  async deleteContextItem(id: string): Promise<boolean> {
    try {
      const result = await db.query(
        'DELETE FROM mcp_context_items WHERE id = $1 RETURNING id',
        [id]
      );
      
      return result.rowCount > 0;
    } catch (error) {
      console.error(`Error deleting context item ${id}:`, error);
      return false;
    }
  }
}

export const contextCollectionService = new ContextCollectionService();