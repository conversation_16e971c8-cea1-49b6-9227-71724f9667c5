// Real-time Collaboration Engine for PLC IDE

import { io, Socket } from 'socket.io-client';

export interface CollaborationUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  cursor?: CursorPosition;
  selection?: SelectionRange;
  color: string;
}

export interface CursorPosition {
  resourceType: 'program' | 'tag' | 'rung';
  resourceId: string;
  position: { x: number; y: number };
  timestamp: Date;
}

export interface SelectionRange {
  resourceType: 'program' | 'tag' | 'rung';
  resourceId: string;
  start: { line: number; column: number };
  end: { line: number; column: number };
  timestamp: Date;
}

export interface CollaborationEvent {
  type: 'cursor_move' | 'selection_change' | 'edit' | 'comment' | 'user_join' | 'user_leave';
  userId: string;
  data: any;
  timestamp: Date;
}

export interface Comment {
  id: string;
  author: string;
  content: string;
  resourceType: 'program' | 'tag' | 'rung';
  resourceId: string;
  position?: { x: number; y: number };
  lineNumber?: number;
  timestamp: Date;
  resolved: boolean;
  replies: Comment[];
}

export class CollaborationEngine {
  private socket: Socket | null = null;
  private currentUser: CollaborationUser | null = null;
  private activeUsers: Map<string, CollaborationUser> = new Map();
  private comments: Map<string, Comment> = new Map();
  private eventHandlers: Map<string, Function[]> = new Map();

  constructor() {
    this.setupEventHandlers();
  }

  async connect(projectId: string, user: CollaborationUser): Promise<void> {
    this.currentUser = user;
    
    // In production, this would connect to actual WebSocket server
    this.socket = io('ws://localhost:3001', {
      query: { projectId, userId: user.id }
    });

    this.socket.on('connect', () => {
      console.log('Connected to collaboration server');
      this.emit('user_join', user);
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from collaboration server');
    });

    this.socket.on('collaboration_event', (event: CollaborationEvent) => {
      this.handleCollaborationEvent(event);
    });

    this.socket.on('user_joined', (user: CollaborationUser) => {
      this.activeUsers.set(user.id, user);
      this.triggerEvent('user_joined', user);
    });

    this.socket.on('user_left', (userId: string) => {
      this.activeUsers.delete(userId);
      this.triggerEvent('user_left', userId);
    });

    this.socket.on('cursor_update', (data: { userId: string; cursor: CursorPosition }) => {
      const user = this.activeUsers.get(data.userId);
      if (user) {
        user.cursor = data.cursor;
        this.triggerEvent('cursor_update', data);
      }
    });

    this.socket.on('selection_update', (data: { userId: string; selection: SelectionRange }) => {
      const user = this.activeUsers.get(data.userId);
      if (user) {
        user.selection = data.selection;
        this.triggerEvent('selection_update', data);
      }
    });

    this.socket.on('comment_added', (comment: Comment) => {
      this.comments.set(comment.id, comment);
      this.triggerEvent('comment_added', comment);
    });

    this.socket.on('comment_resolved', (commentId: string) => {
      const comment = this.comments.get(commentId);
      if (comment) {
        comment.resolved = true;
        this.triggerEvent('comment_resolved', comment);
      }
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.activeUsers.clear();
    this.currentUser = null;
  }

  // Cursor and Selection Tracking
  updateCursor(position: CursorPosition): void {
    if (!this.socket || !this.currentUser) return;

    this.currentUser.cursor = position;
    this.socket.emit('cursor_move', {
      userId: this.currentUser.id,
      cursor: position
    });
  }

  updateSelection(selection: SelectionRange): void {
    if (!this.socket || !this.currentUser) return;

    this.currentUser.selection = selection;
    this.socket.emit('selection_change', {
      userId: this.currentUser.id,
      selection: selection
    });
  }

  // Real-time Editing
  broadcastEdit(
    resourceType: string,
    resourceId: string,
    operation: any
  ): void {
    if (!this.socket || !this.currentUser) return;

    const event: CollaborationEvent = {
      type: 'edit',
      userId: this.currentUser.id,
      data: {
        resourceType,
        resourceId,
        operation
      },
      timestamp: new Date()
    };

    this.socket.emit('collaboration_event', event);
  }

  // Comments and Annotations
  addComment(
    content: string,
    resourceType: 'program' | 'tag' | 'rung',
    resourceId: string,
    position?: { x: number; y: number },
    lineNumber?: number
  ): void {
    if (!this.socket || !this.currentUser) return;

    const comment: Comment = {
      id: `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      author: this.currentUser.id,
      content,
      resourceType,
      resourceId,
      position,
      lineNumber,
      timestamp: new Date(),
      resolved: false,
      replies: []
    };

    this.comments.set(comment.id, comment);
    this.socket.emit('add_comment', comment);
  }

  replyToComment(commentId: string, content: string): void {
    if (!this.socket || !this.currentUser) return;

    const reply: Comment = {
      id: `reply_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      author: this.currentUser.id,
      content,
      resourceType: 'program', // Will be set by parent comment
      resourceId: '',
      timestamp: new Date(),
      resolved: false,
      replies: []
    };

    this.socket.emit('reply_comment', { commentId, reply });
  }

  resolveComment(commentId: string): void {
    if (!this.socket) return;

    const comment = this.comments.get(commentId);
    if (comment) {
      comment.resolved = true;
      this.socket.emit('resolve_comment', commentId);
    }
  }

  // Event System
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  off(event: string, handler: Function): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emit(eventType: string, data: any): void {
    if (!this.socket) return;

    const event: CollaborationEvent = {
      type: eventType as any,
      userId: this.currentUser?.id || '',
      data,
      timestamp: new Date()
    };

    this.socket.emit('collaboration_event', event);
  }

  private triggerEvent(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }

  private handleCollaborationEvent(event: CollaborationEvent): void {
    switch (event.type) {
      case 'edit':
        this.handleRemoteEdit(event);
        break;
      case 'comment':
        this.handleRemoteComment(event);
        break;
      default:
        this.triggerEvent(event.type, event.data);
    }
  }

  private handleRemoteEdit(event: CollaborationEvent): void {
    // Apply remote edit to local state
    this.triggerEvent('remote_edit', {
      userId: event.userId,
      resourceType: event.data.resourceType,
      resourceId: event.data.resourceId,
      operation: event.data.operation,
      timestamp: event.timestamp
    });
  }

  private handleRemoteComment(event: CollaborationEvent): void {
    const comment = event.data as Comment;
    this.comments.set(comment.id, comment);
    this.triggerEvent('comment_added', comment);
  }

  private setupEventHandlers(): void {
    // Handle page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // User switched away, reduce update frequency
        this.throttleUpdates(true);
      } else {
        // User returned, resume normal updates
        this.throttleUpdates(false);
      }
    });
  }

  private throttleUpdates(throttle: boolean): void {
    // Implement update throttling for performance
    if (this.socket) {
      this.socket.emit('throttle_updates', throttle);
    }
  }

  // Public API
  getActiveUsers(): CollaborationUser[] {
    return Array.from(this.activeUsers.values());
  }

  getComments(resourceType?: string, resourceId?: string): Comment[] {
    const allComments = Array.from(this.comments.values());
    
    if (resourceType && resourceId) {
      return allComments.filter(c => 
        c.resourceType === resourceType && c.resourceId === resourceId
      );
    }
    
    return allComments;
  }

  getCurrentUser(): CollaborationUser | null {
    return this.currentUser;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }
}

export const collaborationEngine = new CollaborationEngine();