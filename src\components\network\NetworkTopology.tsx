import React, { useState, useRef, useEffect } from 'react';
import { usePLCStore } from '../../store/plcStore';
import { NetworkNode } from '../../types/plc';
import { 
  Network, 
  Cpu, 
  Monitor, 
  Zap, 
  HardDrive,
  Shield,
  Wifi,
  WifiOff,
  AlertTriangle,
  CheckCircle2,
  Settings,
  Plus,
  Trash2,
  Edit,
  Save,
  RefreshCw,
  Search,
  Download,
  Upload,
  Move,
  X,
  Eye,
  EyeOff
} from 'lucide-react';

const NetworkTopology: React.FC = () => {
  const { currentProject } = usePLCStore();
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [draggedNode, setDraggedNode] = useState<string | null>(null);
  const [nodes, setNodes] = useState<NetworkNode[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [showAddNodeModal, setShowAddNodeModal] = useState(false);
  const [newNode, setNewNode] = useState<Partial<NetworkNode>>({
    name: '',
    type: 'PLC',
    ipAddress: '*************',
    status: 'online',
    protocol: 'EtherNet/IP',
    position: { x: 100, y: 100 },
    connections: []
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showConnectionMode, setShowConnectionMode] = useState(false);
  const [connectionSource, setConnectionSource] = useState<string | null>(null);
  const [showNodeDetails, setShowNodeDetails] = useState(false);
  const [editingNode, setEditingNode] = useState<NetworkNode | null>(null);
  const [showPropertiesPanel, setShowPropertiesPanel] = useState(true);
  
  const canvasRef = useRef<HTMLDivElement>(null);

  // Default network topology if none exists
  const defaultNodes: NetworkNode[] = [
    {
      id: 'plc-main',
      name: 'Main PLC',
      type: 'PLC',
      ipAddress: '*************',
      status: 'online',
      protocol: 'EtherNet/IP',
      position: { x: 200, y: 150 },
      connections: ['hmi-station', 'io-block-1', 'safety-plc']
    },
    {
      id: 'hmi-station',
      name: 'Operator Station',
      type: 'HMI',
      ipAddress: '*************',
      status: 'online',
      protocol: 'EtherNet/IP',
      position: { x: 50, y: 50 },
      connections: ['plc-main']
    },
    {
      id: 'io-block-1',
      name: 'Remote I/O Block',
      type: 'IO_Block',
      ipAddress: '*************',
      status: 'online',
      protocol: 'EtherNet/IP',
      position: { x: 400, y: 100 },
      connections: ['plc-main', 'drive-1']
    },
    {
      id: 'drive-1',
      name: 'Motor Drive #1',
      type: 'Drive',
      ipAddress: '*************',
      status: 'online',
      protocol: 'EtherNet/IP',
      position: { x: 600, y: 200 },
      connections: ['io-block-1']
    },
    {
      id: 'safety-plc',
      name: 'Safety Controller',
      type: 'Safety_Controller',
      ipAddress: '*************',
      status: 'online',
      protocol: 'Profinet',
      position: { x: 200, y: 300 },
      connections: ['plc-main']
    }
  ];

  // Initialize nodes from project or defaults
  useEffect(() => {
    setNodes(currentProject?.networkTopology || defaultNodes);
  }, [currentProject]);

  // Handle node dragging
  const handleDragStart = (nodeId: string, e: React.DragEvent) => {
    if (!isEditing) return;
    
    setDraggedNode(nodeId);
    // Set drag image
    const dragImage = new Image();
    dragImage.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'; // Transparent 1x1 pixel
    e.dataTransfer.setDragImage(dragImage, 0, 0);
  };

  const handleDrag = (e: React.DragEvent) => {
    if (!draggedNode || !isEditing || !canvasRef.current) return;
    
    e.preventDefault();
    
    // Get canvas position
    const rect = canvasRef.current.getBoundingClientRect();
    
    // Calculate new position
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Update node position
    setNodes(prev => prev.map(node => 
      node.id === draggedNode 
        ? { ...node, position: { x, y } } 
        : node
    ));
  };

  const handleDragEnd = () => {
    setDraggedNode(null);
  };

  const handleNodeClick = (nodeId: string) => {
    if (showConnectionMode) {
      if (connectionSource) {
        // Complete connection
        if (connectionSource !== nodeId) {
          // Add connection between nodes
          setNodes(prev => {
            const updatedNodes = [...prev];
            const sourceNode = updatedNodes.find(n => n.id === connectionSource);
            if (sourceNode) {
              // Check if connection already exists
              if (!sourceNode.connections.includes(nodeId)) {
                sourceNode.connections = [...sourceNode.connections, nodeId];
              }
            }
            return updatedNodes;
          });
        }
        setConnectionSource(null);
      } else {
        // Start connection
        setConnectionSource(nodeId);
      }
    } else {
      setSelectedNode(nodeId);
    }
  };

  const handleEditNode = (node: NetworkNode) => {
    setEditingNode({...node});
    setShowNodeDetails(true);
  };

  const handleSaveNodeDetails = () => {
    if (!editingNode) return;
    
    setNodes(prev => prev.map(node => 
      node.id === editingNode.id ? editingNode : node
    ));
    
    setShowNodeDetails(false);
    setEditingNode(null);
  };

  const handleDeleteNode = (nodeId: string) => {
    if (confirm('Are you sure you want to delete this node?')) {
      // Remove node
      setNodes(prev => prev.filter(node => node.id !== nodeId));
      
      // Remove connections to this node
      setNodes(prev => prev.map(node => ({
        ...node,
        connections: node.connections.filter(conn => conn !== nodeId)
      })));
      
      if (selectedNode === nodeId) {
        setSelectedNode(null);
      }
    }
  };

  const handleAddNode = () => {
    const id = `node-${Date.now()}`;
    const newNodeData: NetworkNode = {
      id,
      name: newNode.name || `New Device`,
      type: newNode.type as NetworkNode['type'] || 'PLC',
      ipAddress: newNode.ipAddress || '*************',
      status: newNode.status as NetworkNode['status'] || 'online',
      protocol: newNode.protocol as NetworkNode['protocol'] || 'EtherNet/IP',
      position: newNode.position || { x: 300, y: 200 },
      connections: []
    };
    
    setNodes(prev => [...prev, newNodeData]);
    setShowAddNodeModal(false);
    setNewNode({
      name: '',
      type: 'PLC',
      ipAddress: '*************',
      status: 'online',
      protocol: 'EtherNet/IP',
      position: { x: 100, y: 100 },
      connections: []
    });
  };

  const handleRemoveConnection = (sourceId: string, targetId: string) => {
    setNodes(prev => prev.map(node => {
      if (node.id === sourceId) {
        return {
          ...node,
          connections: node.connections.filter(conn => conn !== targetId)
        };
      }
      return node;
    }));
  };

  const handleRefreshNetwork = () => {
    setIsRefreshing(true);
    
    // Simulate network refresh
    setTimeout(() => {
      // Randomly update some node statuses
      setNodes(prev => prev.map(node => {
        if (Math.random() > 0.7) {
          return {
            ...node,
            status: Math.random() > 0.8 ? 'fault' : 'online'
          };
        }
        return node;
      }));
      
      setIsRefreshing(false);
    }, 2000);
  };

  const getNodeIcon = (type: string) => {
    switch (type) {
      case 'PLC': return <Cpu className="w-6 h-6" />;
      case 'HMI': return <Monitor className="w-6 h-6" />;
      case 'Drive': return <Zap className="w-6 h-6" />;
      case 'IO_Block': return <HardDrive className="w-6 h-6" />;
      case 'Safety_Controller': return <Shield className="w-6 h-6" />;
      default: return <Network className="w-6 h-6" />;
    }
  };

  const getNodeColor = (type: string, status: string) => {
    const baseColors = {
      'PLC': 'blue',
      'HMI': 'purple',
      'Drive': 'yellow',
      'IO_Block': 'green',
      'Safety_Controller': 'red'
    };
    
    const color = baseColors[type as keyof typeof baseColors] || 'gray';
    
    if (status === 'offline' || status === 'fault') {
      return 'gray';
    }
    
    return color;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle2 className="w-4 h-4 text-green-400" />;
      case 'offline': return <WifiOff className="w-4 h-4 text-red-400" />;
      case 'fault': return <AlertTriangle className="w-4 h-4 text-amber-400" />;
      default: return <Wifi className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderConnection = (fromNode: NetworkNode, toNodeId: string) => {
    const toNode = nodes.find(n => n.id === toNodeId);
    if (!toNode) return null;

    const fromX = fromNode.position.x + 60; // Node width/2
    const fromY = fromNode.position.y + 40; // Node height/2
    const toX = toNode.position.x + 60;
    const toY = toNode.position.y + 40;

    const isSelected = selectedNode === fromNode.id || selectedNode === toNodeId;
    const isConnectionSelected = connectionSource === fromNode.id && selectedNode === toNodeId;
    const strokeColor = isSelected || isConnectionSelected ? '#3b82f6' : '#6b7280';
    const strokeWidth = isSelected || isConnectionSelected ? 3 : 2;

    return (
      <g key={`${fromNode.id}-${toNodeId}`}>
        <line
          x1={fromX}
          y1={fromY}
          x2={toX}
          y2={toY}
          stroke={strokeColor}
          strokeWidth={strokeWidth}
          strokeDasharray={fromNode.status === 'online' && toNode.status === 'online' ? 'none' : '5,5'}
          className="transition-all duration-200"
        />
        
        {isEditing && (
          <g>
            {/* Connection midpoint for delete button */}
            <circle
              cx={(fromX + toX) / 2}
              cy={(fromY + toY) / 2}
              r={10}
              fill="#ef4444"
              opacity={0}
              className="hover:opacity-100 transition-opacity cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveConnection(fromNode.id, toNodeId);
              }}
            />
            <text
              x={(fromX + toX) / 2}
              y={(fromY + toY) / 2}
              textAnchor="middle"
              dominantBaseline="middle"
              fill="white"
              fontSize={12}
              opacity={0}
              className="hover:opacity-100 transition-opacity cursor-pointer pointer-events-none"
            >
              ×
            </text>
          </g>
        )}
      </g>
    );
  };

  const renderNode = (node: NetworkNode) => {
    const isSelected = selectedNode === node.id;
    const isConnectionSource = connectionSource === node.id;
    const color = getNodeColor(node.type, node.status);
    
    return (
      <div
        key={node.id}
        className={`absolute cursor-pointer transition-all duration-200 ${
          isSelected ? 'scale-110 z-10' : 'hover:scale-105'
        } ${isConnectionSource ? 'ring-2 ring-blue-500' : ''}`}
        style={{
          left: node.position.x,
          top: node.position.y,
          width: 120,
          height: 80
        }}
        onClick={() => handleNodeClick(node.id)}
        draggable={isEditing}
        onDragStart={(e) => handleDragStart(node.id, e)}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
      >
        <div className={`w-full h-full rounded-lg border-2 p-3 ${
          isSelected 
            ? `border-${color}-400 bg-${color}-500/20` 
            : `border-${color}-600 bg-${color}-500/10 hover:bg-${color}-500/20`
        } backdrop-blur-sm`}>
          <div className="flex items-center justify-between mb-2">
            <span className={`text-${color}-400`}>
              {getNodeIcon(node.type)}
            </span>
            {getStatusIcon(node.status)}
          </div>
          
          <div className="text-white text-sm font-semibold truncate">
            {node.name}
          </div>
          
          <div className="text-xs text-gray-400 truncate">
            {node.ipAddress}
          </div>
          
          <div className="text-xs text-gray-500 mt-1">
            {node.protocol}
          </div>
        </div>
        
        {isEditing && isSelected && (
          <div className="absolute -top-4 -right-4 flex space-x-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleEditNode(node);
              }}
              className="w-8 h-8 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center text-white shadow-lg"
              title="Edit Node"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteNode(node.id);
              }}
              className="w-8 h-8 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center text-white shadow-lg"
              title="Delete Node"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    );
  };

  const renderAddNodeModal = () => {
    if (!showAddNodeModal) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md">
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Add Network Device</h3>
            <button
              onClick={() => setShowAddNodeModal(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Device Name</label>
              <input
                type="text"
                value={newNode.name || ''}
                onChange={(e) => setNewNode({...newNode, name: e.target.value})}
                placeholder="Enter device name"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Device Type</label>
              <select
                value={newNode.type || 'PLC'}
                onChange={(e) => setNewNode({...newNode, type: e.target.value as NetworkNode['type']})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="PLC">PLC Controller</option>
                <option value="HMI">HMI Station</option>
                <option value="Drive">Motor Drive</option>
                <option value="IO_Block">I/O Block</option>
                <option value="Safety_Controller">Safety Controller</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">IP Address</label>
              <input
                type="text"
                value={newNode.ipAddress || ''}
                onChange={(e) => setNewNode({...newNode, ipAddress: e.target.value})}
                placeholder="*************"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Protocol</label>
              <select
                value={newNode.protocol || 'EtherNet/IP'}
                onChange={(e) => setNewNode({...newNode, protocol: e.target.value as NetworkNode['protocol']})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="EtherNet/IP">EtherNet/IP</option>
                <option value="Profinet">Profinet</option>
                <option value="Modbus TCP">Modbus TCP</option>
                <option value="DeviceNet">DeviceNet</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Status</label>
              <select
                value={newNode.status || 'online'}
                onChange={(e) => setNewNode({...newNode, status: e.target.value as NetworkNode['status']})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="online">Online</option>
                <option value="offline">Offline</option>
                <option value="fault">Fault</option>
              </select>
            </div>
            
            <div className="pt-4 border-t border-gray-700 flex justify-end">
              <button
                onClick={() => setShowAddNodeModal(false)}
                className="text-gray-400 hover:text-white px-4 py-2 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={handleAddNode}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
                disabled={!newNode.name}
              >
                Add Device
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderNodeDetailsModal = () => {
    if (!showNodeDetails || !editingNode) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md">
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Edit Device</h3>
            <button
              onClick={() => setShowNodeDetails(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Device Name</label>
              <input
                type="text"
                value={editingNode.name}
                onChange={(e) => setEditingNode({...editingNode, name: e.target.value})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Device Type</label>
              <select
                value={editingNode.type}
                onChange={(e) => setEditingNode({...editingNode, type: e.target.value as NetworkNode['type']})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="PLC">PLC Controller</option>
                <option value="HMI">HMI Station</option>
                <option value="Drive">Motor Drive</option>
                <option value="IO_Block">I/O Block</option>
                <option value="Safety_Controller">Safety Controller</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">IP Address</label>
              <input
                type="text"
                value={editingNode.ipAddress}
                onChange={(e) => setEditingNode({...editingNode, ipAddress: e.target.value})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Protocol</label>
              <select
                value={editingNode.protocol}
                onChange={(e) => setEditingNode({...editingNode, protocol: e.target.value as NetworkNode['protocol']})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="EtherNet/IP">EtherNet/IP</option>
                <option value="Profinet">Profinet</option>
                <option value="Modbus TCP">Modbus TCP</option>
                <option value="DeviceNet">DeviceNet</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Status</label>
              <select
                value={editingNode.status}
                onChange={(e) => setEditingNode({...editingNode, status: e.target.value as NetworkNode['status']})}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="online">Online</option>
                <option value="offline">Offline</option>
                <option value="fault">Fault</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-400 mb-2">Connections</label>
              <div className="bg-gray-700 border border-gray-600 rounded p-2 max-h-32 overflow-y-auto">
                {editingNode.connections.length === 0 ? (
                  <div className="text-gray-400 text-sm text-center py-2">
                    No connections
                  </div>
                ) : (
                  <div className="space-y-1">
                    {editingNode.connections.map(connId => {
                      const connectedNode = nodes.find(n => n.id === connId);
                      return connectedNode ? (
                        <div key={connId} className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-2">
                            <span className={`text-${getNodeColor(connectedNode.type, connectedNode.status)}-400`}>
                              {getNodeIcon(connectedNode.type)}
                            </span>
                            <span className="text-white">{connectedNode.name}</span>
                          </div>
                          <button
                            onClick={() => {
                              setEditingNode({
                                ...editingNode,
                                connections: editingNode.connections.filter(c => c !== connId)
                              });
                            }}
                            className="text-red-400 hover:text-red-300"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ) : null;
                    })}
                  </div>
                )}
              </div>
            </div>
            
            <div className="pt-4 border-t border-gray-700 flex justify-between">
              <button
                onClick={() => {
                  handleDeleteNode(editingNode.id);
                  setShowNodeDetails(false);
                }}
                className="flex items-center space-x-2 text-red-400 hover:text-red-300"
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete</span>
              </button>
              
              <div>
                <button
                  onClick={() => setShowNodeDetails(false)}
                  className="text-gray-400 hover:text-white px-4 py-2 mr-2"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveNodeDetails}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const selectedNodeData = nodes.find(n => n.id === selectedNode);

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Toolbar */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Network className="w-5 h-5 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Network Topology</h2>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={handleRefreshNetwork}
              className={`p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors ${isRefreshing ? 'animate-spin text-blue-400' : ''}`}
              title="Refresh Network Status"
              disabled={isRefreshing}
            >
              <RefreshCw className="w-4 h-4" />
            </button>
            <button
              onClick={() => setIsEditing(!isEditing)}
              className={`p-2 rounded transition-colors ${
                isEditing ? 'bg-blue-600/20 text-blue-400' : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
              title={isEditing ? 'Exit Edit Mode' : 'Edit Network'}
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowConnectionMode(!showConnectionMode)}
              className={`p-2 rounded transition-colors ${
                showConnectionMode ? 'bg-green-600/20 text-green-400' : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
              title={showConnectionMode ? 'Exit Connection Mode' : 'Add Connections'}
              disabled={!isEditing}
            >
              <Network className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowAddNodeModal(true)}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors"
              disabled={!isEditing}
            >
              <Plus className="w-4 h-4" />
              <span>Add Device</span>
            </button>
            <button
              onClick={() => setShowPropertiesPanel(!showPropertiesPanel)}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title={showPropertiesPanel ? "Hide Properties" : "Show Properties"}
            >
              {showPropertiesPanel ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            </button>
          </div>
        </div>
        
        {isEditing && (
          <div className="mt-3 p-3 bg-blue-600/20 border border-blue-600/30 rounded">
            <div className="flex items-center justify-between">
              <p className="text-blue-400 text-sm">
                Edit Mode: Drag devices to reposition them. Click devices to select and edit.
              </p>
              <button
                onClick={() => setIsEditing(false)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
              >
                Exit Edit Mode
              </button>
            </div>
          </div>
        )}
        
        {showConnectionMode && (
          <div className="mt-3 p-3 bg-green-600/20 border border-green-600/30 rounded">
            <div className="flex items-center justify-between">
              <p className="text-green-400 text-sm">
                {connectionSource 
                  ? 'Now click on another device to create a connection' 
                  : 'Click on a device to start creating a connection'}
              </p>
              <button
                onClick={() => {
                  setShowConnectionMode(false);
                  setConnectionSource(null);
                }}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
              >
                Exit Connection Mode
              </button>
            </div>
          </div>
        )}
      </div>
      
      {/* Main Canvas */}
      <div className="flex-1 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
          {/* Grid Background */}
          <div 
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: 'radial-gradient(circle, #374151 1px, transparent 1px)',
              backgroundSize: '20px 20px'
            }}
          />
          
          {/* SVG for connections */}
          <svg className="absolute inset-0 w-full h-full pointer-events-none">
            {nodes.map(node => 
              node.connections.map(connId => renderConnection(node, connId))
            )}
          </svg>
          
          {/* Network Nodes */}
          <div ref={canvasRef} className="relative w-full h-full">
            {nodes.map(renderNode)}
          </div>
        </div>
        
        {/* Canvas Controls */}
        <div className="absolute top-4 left-4 bg-gray-800/90 backdrop-blur-sm rounded-lg p-3">
          <div className="flex items-center space-x-3">
            <Network className="w-5 h-5 text-blue-400" />
            <div>
              <h3 className="text-white font-semibold">Network Topology</h3>
              <p className="text-xs text-gray-400">{nodes.length} devices</p>
            </div>
          </div>
        </div>
        
        {/* Legend */}
        <div className="absolute bottom-4 left-4 bg-gray-800/90 backdrop-blur-sm rounded-lg p-3">
          <h4 className="text-white text-sm font-semibold mb-2">Device Types</h4>
          <div className="space-y-1">
            {[
              { type: 'PLC', color: 'blue', label: 'PLC Controller' },
              { type: 'HMI', color: 'purple', label: 'HMI Station' },
              { type: 'Drive', color: 'yellow', label: 'Motor Drive' },
              { type: 'IO_Block', color: 'green', label: 'I/O Block' },
              { type: 'Safety_Controller', color: 'red', label: 'Safety PLC' }
            ].map(item => (
              <div key={item.type} className="flex items-center space-x-2 text-xs">
                <div className={`w-3 h-3 rounded bg-${item.color}-500`}></div>
                <span className="text-gray-300">{item.label}</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Status Indicators */}
        <div className="absolute bottom-4 right-4 bg-gray-800/90 backdrop-blur-sm rounded-lg p-3">
          <h4 className="text-white text-sm font-semibold mb-2">Status</h4>
          <div className="space-y-1">
            <div className="flex items-center space-x-2 text-xs">
              <CheckCircle2 className="w-3 h-3 text-green-400" />
              <span className="text-gray-300">Online</span>
            </div>
            <div className="flex items-center space-x-2 text-xs">
              <WifiOff className="w-3 h-3 text-red-400" />
              <span className="text-gray-300">Offline</span>
            </div>
            <div className="flex items-center space-x-2 text-xs">
              <AlertTriangle className="w-3 h-3 text-amber-400" />
              <span className="text-gray-300">Fault</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Properties Panel */}
      {showPropertiesPanel && (
        <div className="w-80 bg-gray-800 border-l border-gray-700 absolute right-0 top-[57px] bottom-0 flex flex-col">
          <div className="p-4 border-b border-gray-700 flex items-center justify-between">
            <h3 className="text-white font-semibold">Device Properties</h3>
            <button 
              onClick={() => setShowPropertiesPanel(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            {selectedNodeData ? (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm text-gray-400 mb-2">Device Name</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={selectedNodeData.name}
                      readOnly={!isEditing}
                      onChange={(e) => {
                        if (isEditing) {
                          setNodes(prev => prev.map(n => 
                            n.id === selectedNodeData.id ? { ...n, name: e.target.value } : n
                          ));
                        }
                      }}
                      className={`w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm ${!isEditing ? 'opacity-80' : ''}`}
                    />
                    {isEditing && (
                      <button
                        onClick={() => handleEditNode(selectedNodeData)}
                        className="p-1 text-gray-400 hover:text-white hover:bg-gray-600 rounded transition-colors"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm text-gray-400 mb-2">Device Type</label>
                  <div className="flex items-center space-x-2">
                    <span className={`text-${getNodeColor(selectedNodeData.type, selectedNodeData.status)}-400`}>
                      {getNodeIcon(selectedNodeData.type)}
                    </span>
                    <span className="text-white">{selectedNodeData.type.replace('_', ' ')}</span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm text-gray-400 mb-2">IP Address</label>
                  <input
                    type="text"
                    value={selectedNodeData.ipAddress}
                    readOnly={!isEditing}
                    onChange={(e) => {
                      if (isEditing) {
                        setNodes(prev => prev.map(n => 
                          n.id === selectedNodeData.id ? { ...n, ipAddress: e.target.value } : n
                        ));
                      }
                    }}
                    className={`w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm ${!isEditing ? 'opacity-80' : ''}`}
                  />
                </div>
                
                <div>
                  <label className="block text-sm text-gray-400 mb-2">Protocol</label>
                  <select
                    value={selectedNodeData.protocol}
                    disabled={!isEditing}
                    onChange={(e) => {
                      if (isEditing) {
                        setNodes(prev => prev.map(n => 
                          n.id === selectedNodeData.id ? { ...n, protocol: e.target.value as NetworkNode['protocol'] } : n
                        ));
                      }
                    }}
                    className={`w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm ${!isEditing ? 'opacity-80' : ''}`}
                  >
                    <option value="EtherNet/IP">EtherNet/IP</option>
                    <option value="Profinet">Profinet</option>
                    <option value="Modbus TCP">Modbus TCP</option>
                    <option value="DeviceNet">DeviceNet</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm text-gray-400 mb-2">Status</label>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(selectedNodeData.status)}
                    <span className={`capitalize ${
                      selectedNodeData.status === 'online' ? 'text-green-400' :
                      selectedNodeData.status === 'offline' ? 'text-red-400' :
                      'text-amber-400'
                    }`}>
                      {selectedNodeData.status}
                    </span>
                    {isEditing && (
                      <select
                        value={selectedNodeData.status}
                        onChange={(e) => {
                          setNodes(prev => prev.map(n => 
                            n.id === selectedNodeData.id ? { ...n, status: e.target.value as NetworkNode['status'] } : n
                          ));
                        }}
                        className="ml-auto bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-xs"
                      >
                        <option value="online">Online</option>
                        <option value="offline">Offline</option>
                        <option value="fault">Fault</option>
                      </select>
                    )}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm text-gray-400 mb-2">Connections</label>
                  <div className="space-y-1 max-h-40 overflow-y-auto bg-gray-700/50 rounded-lg p-2">
                    {selectedNodeData.connections.length === 0 ? (
                      <div className="text-center py-2 text-gray-400 text-sm">
                        No connections
                      </div>
                    ) : (
                      selectedNodeData.connections.map(connId => {
                        const connectedNode = nodes.find(n => n.id === connId);
                        return connectedNode ? (
                          <div key={connId} className="flex items-center justify-between text-sm">
                            <div className="flex items-center space-x-2">
                              <span className={`text-${getNodeColor(connectedNode.type, connectedNode.status)}-400`}>
                                {getNodeIcon(connectedNode.type)}
                              </span>
                              <span className="text-white">{connectedNode.name}</span>
                              {getStatusIcon(connectedNode.status)}
                            </div>
                            {isEditing && (
                              <button
                                onClick={() => handleRemoveConnection(selectedNodeData.id, connId)}
                                className="text-red-400 hover:text-red-300"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        ) : null;
                      })
                    )}
                  </div>
                </div>
                
                <div className="pt-4 border-t border-gray-700">
                  <button 
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors"
                    onClick={() => {
                      if (isEditing) {
                        handleEditNode(selectedNodeData);
                      } else {
                        alert(`Connecting to ${selectedNodeData.name}...`);
                      }
                    }}
                  >
                    {isEditing ? 'Edit Device' : 'Connect to Device'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-400 py-12">
                <Network className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Select a device to view properties</p>
              </div>
            )}
          </div>
        </div>
      )}
      
      {renderAddNodeModal()}
      {renderNodeDetailsModal()}
    </div>
  );
};

export default NetworkTopology;