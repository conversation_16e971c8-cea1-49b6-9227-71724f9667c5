import React, { useState } from 'react';
import { 
  Settings, 
  Tag, 
  Clock, 
  Shield, 
  Zap,
  Info,
  Sparkles,
  ToggleLeft,
  ToggleRight,
  Save,
  Trash2,
  Copy,
  Edit,
  Check,
  X,
  Plus,
  RefreshCw,
  Download,
  Upload,
  HelpCircle,
  AlertTriangle
} from 'lucide-react';
import { useAI } from '../ai/hooks/useAI';
import AiConfidenceBadge from '../ai/components/AiConfidenceBadge';
import AiImpactBadge from '../ai/components/AiImpactBadge';
import AiSuggestionModal from '../ai/components/AiSuggestionModal';
import { usePLCStore } from '../store/plcStore';
import ContextMemoryPanel from '../ai/components/ContextMemoryPanel';
import MCPInspector from '../ai/components/MCPInspector';

const PropertiesPanel: React.FC = () => {
  const [activeSection, setActiveSection] = useState('properties');
  const [isEditing, setIsEditing] = useState(false);
  const [editedValues, setEditedValues] = useState({
    name: 'PumpControl_FB',
    description: 'Main pump control logic with safety interlocks',
    version: '1.2.3'
  });
  
  const { 
    explainCode, 
    suggestRefactor, 
    suggestTags, 
    isLoading, 
    metrics,
    requireReview,
    setRequireReview 
  } = useAI();
  
  const { currentProject, activeProgram } = usePLCStore();
  const program = currentProject?.programs.find(p => p.id === activeProgram);
  
  const [aiResponse, setAiResponse] = useState<string>('');
  const [currentAiResponse, setCurrentAiResponse] = useState<any>(null);
  const [showSuggestionModal, setShowSuggestionModal] = useState(false);
  const [currentSuggestion, setCurrentSuggestion] = useState<any>(null);

  const sections = [
    { id: 'properties', name: 'Properties', icon: Settings },
    { id: 'tags', name: 'Tags', icon: Tag },
    { id: 'timing', name: 'Timing', icon: Clock },
    { id: 'safety', name: 'Safety', icon: Shield },
    { id: 'context', name: 'Context', icon: Info },
    { id: 'ai', name: 'AI Assistant', icon: Sparkles }
  ];

  const handleAiAction = async (action: string) => {
    try {
      let response;
      switch (action) {
        case 'explain':
          response = await explainCode('current selected code', 'ladder');
          break;
        case 'optimize':
          response = await suggestRefactor('current selected code', 'ladder');
          break;
        case 'suggest-tags':
          response = await suggestTags('motor control system');
          break;
        default:
          return;
      }
      
      setCurrentAiResponse(response);
      
      if (requireReview && action !== 'explain') {
        setCurrentSuggestion({
          id: response.id,
          content: response.content,
          confidence: response.confidence,
          prompt: response.prompt || '',
          type: action === 'optimize' ? 'refactor' : 'code'
        });
        setShowSuggestionModal(true);
      } else {
        setAiResponse(response.content);
      }
    } catch (error) {
      console.error('AI action failed:', error);
    }
  };

  const handleSaveProperties = () => {
    // In a real implementation, this would update the program properties
    setIsEditing(false);
    alert('Properties saved');
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedValues({
      name: 'PumpControl_FB',
      description: 'Main pump control logic with safety interlocks',
      version: '1.2.3'
    });
  };

  const renderProperties = () => (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-semibold text-neutral">Block Properties</h3>
        {isEditing ? (
          <div className="flex items-center space-x-2">
            <button
              onClick={handleSaveProperties}
              className="p-1 text-success hover:bg-success/20 rounded transition-colors"
              title="Save"
            >
              <Check className="w-4 h-4" />
            </button>
            <button
              onClick={handleCancelEdit}
              className="p-1 text-error hover:bg-error/20 rounded transition-colors"
              title="Cancel"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <button
            onClick={() => setIsEditing(true)}
            className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            title="Edit Properties"
          >
            <Edit className="w-4 h-4" />
          </button>
        )}
      </div>
      
      <div className="space-y-3">
        <div>
          <label className="block text-xs text-control-400 mb-1">Name</label>
          {isEditing ? (
            <input 
              type="text" 
              value={editedValues.name}
              onChange={(e) => setEditedValues({...editedValues, name: e.target.value})}
              className="w-full bg-control-800 border border-control-600 rounded px-3 py-2 text-sm text-neutral focus:border-accent focus:outline-none"
            />
          ) : (
            <div className="bg-control-800/50 rounded px-3 py-2 text-sm text-neutral">
              {program?.name || editedValues.name}
            </div>
          )}
        </div>
        <div>
          <label className="block text-xs text-control-400 mb-1">Description</label>
          {isEditing ? (
            <textarea 
              value={editedValues.description}
              onChange={(e) => setEditedValues({...editedValues, description: e.target.value})}
              className="w-full bg-control-800 border border-control-600 rounded px-3 py-2 text-sm text-neutral focus:border-accent focus:outline-none h-20 resize-none"
            />
          ) : (
            <div className="bg-control-800/50 rounded px-3 py-2 text-sm text-neutral min-h-[5rem]">
              {program?.description || editedValues.description}
            </div>
          )}
        </div>
        <div>
          <label className="block text-xs text-control-400 mb-1">Version</label>
          {isEditing ? (
            <input 
              type="text" 
              value={editedValues.version}
              onChange={(e) => setEditedValues({...editedValues, version: e.target.value})}
              className="w-full bg-control-800 border border-control-600 rounded px-3 py-2 text-sm text-neutral focus:border-accent focus:outline-none"
            />
          ) : (
            <div className="bg-control-800/50 rounded px-3 py-2 text-sm text-neutral">
              {program?.version || editedValues.version}
            </div>
          )}
        </div>
      </div>

      <div className="border-t border-control-600 pt-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-neutral">Execution</h3>
          <button
            className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            title="Configure Execution"
          >
            <Settings className="w-4 h-4" />
          </button>
        </div>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-xs text-control-400">Scan Time</span>
            <span className="text-xs text-success font-mono">2.3 ms</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-control-400">Priority</span>
            <select className="bg-control-800 border border-control-600 rounded px-2 py-1 text-xs text-neutral">
              <option>Normal</option>
              <option>High</option>
              <option>Critical</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-control-400">Watchdog</span>
            <div className="flex items-center">
              <input type="checkbox" className="mr-2" defaultChecked />
              <span className="text-xs text-neutral">Enabled</span>
            </div>
          </div>
        </div>
      </div>

      <div className="border-t border-control-600 pt-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-neutral">Actions</h3>
        </div>
        <div className="grid grid-cols-2 gap-2">
          <button className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-xs transition-colors">
            <Save className="w-3 h-3" />
            <span>Save Program</span>
          </button>
          <button className="flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-xs transition-colors">
            <Download className="w-3 h-3" />
            <span>Download to PLC</span>
          </button>
          <button className="flex items-center justify-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-xs transition-colors">
            <Copy className="w-3 h-3" />
            <span>Duplicate</span>
          </button>
          <button className="flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-xs transition-colors">
            <Trash2 className="w-3 h-3" />
            <span>Delete</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderTags = () => (
    <div className="p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-neutral">Variable Tags</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => handleAiAction('suggest-tags')}
            disabled={isLoading}
            className="flex items-center space-x-1 text-xs bg-purple-600/20 hover:bg-purple-600/30 text-purple-400 px-2 py-1 rounded transition-colors disabled:opacity-50"
          >
            <Sparkles className="w-3 h-3" />
            <span>AI Suggest</span>
          </button>
          <button
            className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            title="Add Tag"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div className="space-y-2">
        {[
          { name: 'Start_Command', type: 'BOOL', value: 'FALSE', access: 'INPUT' },
          { name: 'Emergency_Stop', type: 'BOOL', value: 'TRUE', access: 'INPUT' },
          { name: 'Motor_Output', type: 'BOOL', value: 'FALSE', access: 'OUTPUT' },
          { name: 'Runtime_Timer', type: 'TON', value: 'T#0ms', access: 'LOCAL' }
        ].map((tag, index) => (
          <div key={index} className="bg-control-800/50 rounded p-3 space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-neutral font-medium">{tag.name}</span>
              <div className="flex items-center space-x-2">
                <span className={`text-xs px-2 py-1 rounded ${
                  tag.access === 'INPUT' ? 'bg-accent/20 text-accent' :
                  tag.access === 'OUTPUT' ? 'bg-success/20 text-success' :
                  'bg-control-600/20 text-control-400'
                }`}>
                  {tag.access}
                </span>
                <button
                  className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
                  title="Edit Tag"
                >
                  <Edit className="w-3 h-3" />
                </button>
              </div>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-control-400">{tag.type}</span>
              <span className="text-primary font-mono">{tag.value}</span>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-control-600">
        <button className="w-full flex items-center justify-center space-x-2 bg-accent hover:bg-accent/80 text-white px-3 py-2 rounded text-sm transition-colors">
          <Plus className="w-4 h-4" />
          <span>Add New Tag</span>
        </button>
      </div>
    </div>
  );

  const renderTiming = () => (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-semibold text-neutral">Timing Analysis</h3>
        <button
          className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
          title="Refresh Timing"
        >
          <RefreshCw className="w-4 h-4" />
        </button>
      </div>
      
      <div className="bg-control-800/50 rounded-lg p-4">
        <h4 className="text-white text-sm font-medium mb-3">Execution Time</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-control-400">Current Scan</span>
            <span className="text-white font-mono">2.3 ms</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-control-400">Average</span>
            <span className="text-white font-mono">2.1 ms</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-control-400">Maximum</span>
            <span className="text-white font-mono">3.5 ms</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-control-400">Minimum</span>
            <span className="text-white font-mono">1.8 ms</span>
          </div>
        </div>
      </div>
      
      <div className="bg-control-800/50 rounded-lg p-4">
        <h4 className="text-white text-sm font-medium mb-3">Timing Configuration</h4>
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-control-400 mb-1">Watchdog Time</label>
            <input 
              type="number" 
              value="100"
              className="w-full bg-control-800 border border-control-600 rounded px-3 py-2 text-sm text-neutral focus:border-accent focus:outline-none"
            />
            <div className="text-xs text-control-500 mt-1">milliseconds</div>
          </div>
          <div>
            <label className="block text-xs text-control-400 mb-1">Task Priority</label>
            <select className="w-full bg-control-800 border border-control-600 rounded px-3 py-2 text-sm text-neutral focus:border-accent focus:outline-none">
              <option>Normal (8)</option>
              <option>High (4)</option>
              <option>Critical (1)</option>
              <option>Low (16)</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-control-400">Continuous Task</span>
            <div className="flex items-center">
              <input type="checkbox" className="mr-2" defaultChecked />
              <span className="text-xs text-neutral">Enabled</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-control-800/50 rounded-lg p-4">
        <h4 className="text-white text-sm font-medium mb-3">Performance Alerts</h4>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-xs text-control-400">Alert if scan time exceeds</span>
            <div className="flex items-center space-x-2">
              <input 
                type="number" 
                value="10"
                className="w-16 bg-control-800 border border-control-600 rounded px-2 py-1 text-xs text-neutral focus:border-accent focus:outline-none"
              />
              <span className="text-xs text-control-400">ms</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-control-400">Log performance data</span>
            <div className="flex items-center">
              <input type="checkbox" className="mr-2" defaultChecked />
              <span className="text-xs text-neutral">Enabled</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSafety = () => (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-semibold text-neutral">Safety Configuration</h3>
        <div className="px-2 py-1 rounded text-xs bg-error/20 text-error border border-error/30">
          SIL2
        </div>
      </div>
      
      <div className="bg-control-800/50 rounded-lg p-4">
        <h4 className="text-white text-sm font-medium mb-3">Safety Classification</h4>
        <div className="space-y-3">
          <div>
            <label className="block text-xs text-control-400 mb-1">Safety Integrity Level</label>
            <select className="w-full bg-control-800 border border-control-600 rounded px-3 py-2 text-sm text-neutral focus:border-accent focus:outline-none">
              <option>SIL2</option>
              <option>SIL3</option>
              <option>SIL1</option>
              <option>Not Safety Rated</option>
            </select>
          </div>
          <div>
            <label className="block text-xs text-control-400 mb-1">Safety Standard</label>
            <select className="w-full bg-control-800 border border-control-600 rounded px-3 py-2 text-sm text-neutral focus:border-accent focus:outline-none">
              <option>IEC 61508</option>
              <option>ISO 13849</option>
              <option>IEC 62061</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-control-400">Safety Signature Required</span>
            <div className="flex items-center">
              <input type="checkbox" className="mr-2" defaultChecked />
              <span className="text-xs text-neutral">Enabled</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-control-800/50 rounded-lg p-4">
        <h4 className="text-white text-sm font-medium mb-3">Safety Validation</h4>
        <div className="space-y-2">
          <div className="flex items-center space-x-2 text-green-400 text-sm">
            <CheckCircle2 className="w-4 h-4" />
            <span>Safety signature valid</span>
          </div>
          <div className="flex items-center space-x-2 text-green-400 text-sm">
            <CheckCircle2 className="w-4 h-4" />
            <span>Safety blocks validated</span>
          </div>
          <div className="flex items-center space-x-2 text-amber-400 text-sm">
            <AlertTriangle className="w-4 h-4" />
            <span>Safety test documentation required</span>
          </div>
        </div>
      </div>
      
      <div className="bg-control-800/50 rounded-lg p-4">
        <h4 className="text-white text-sm font-medium mb-3">Safety Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          <button className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-xs transition-colors">
            <Shield className="w-3 h-3" />
            <span>Validate Safety Logic</span>
          </button>
          <button className="flex items-center justify-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-xs transition-colors">
            <Download className="w-3 h-3" />
            <span>Generate Safety Report</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderContext = () => (
    <ContextMemoryPanel />
  );

  const renderAIAssistant = () => (
    <div className="p-4 space-y-4">
      {/* AI Settings */}
      <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
        <h4 className="text-white font-semibold mb-3">AI Settings</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm text-white">Review Before Apply</span>
              <p className="text-xs text-gray-400">Require manual review for AI suggestions</p>
            </div>
            <button
              onClick={() => setRequireReview(!requireReview)}
              className="flex items-center"
            >
              {requireReview ? (
                <ToggleRight className="w-6 h-6 text-blue-400" />
              ) : (
                <ToggleLeft className="w-6 h-6 text-gray-400" />
              )}
            </button>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm text-white">Show AI Prompts</span>
              <p className="text-xs text-gray-400">Display prompts sent to AI</p>
            </div>
            <button
              onClick={() => alert('Toggle show prompts')}
              className="flex items-center"
            >
              <ToggleRight className="w-6 h-6 text-blue-400" />
            </button>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm text-white">Safety Validation</span>
              <p className="text-xs text-gray-400">Extra validation for safety-critical code</p>
            </div>
            <button
              onClick={() => alert('Toggle safety validation')}
              className="flex items-center"
            >
              <ToggleRight className="w-6 h-6 text-blue-400" />
            </button>
          </div>
        </div>
      </div>

      {/* AI Impact Metrics */}
      <AiImpactBadge metrics={metrics} showDetails={true} />

      {/* AI Actions */}
      <div className="bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-lg p-4 border border-purple-500/30">
        <div className="flex items-center mb-3">
          <Sparkles className="w-5 h-5 text-purple-400 mr-2" />
          <h3 className="text-sm font-semibold text-neutral">AI Assistant</h3>
        </div>
        <p className="text-xs text-control-300 mb-3">
          Get intelligent suggestions and explanations for your PLC logic.
        </p>
        <div className="space-y-2">
          <button 
            onClick={() => handleAiAction('explain')}
            disabled={isLoading}
            className="w-full bg-blue-600/20 hover:bg-blue-600/30 border border-blue-500/30 rounded px-3 py-2 text-xs text-blue-400 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <div className="w-3 h-3 border border-blue-400 border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <Info className="w-3 h-3" />
            )}
            <span>Explain this logic</span>
          </button>
          
          <button 
            onClick={() => handleAiAction('optimize')}
            disabled={isLoading}
            className="w-full bg-green-600/20 hover:bg-green-600/30 border border-green-500/30 rounded px-3 py-2 text-xs text-green-400 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <div className="w-3 h-3 border border-green-400 border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <Zap className="w-3 h-3" />
            )}
            <span>Suggest optimizations</span>
          </button>
          
          <button 
            onClick={() => handleAiAction('suggest-tags')}
            disabled={isLoading}
            className="w-full bg-purple-600/20 hover:bg-purple-600/30 border border-purple-500/30 rounded px-3 py-2 text-xs text-purple-400 transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <div className="w-3 h-3 border border-purple-400 border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <Tag className="w-3 h-3" />
            )}
            <span>Generate tags</span>
          </button>
          
          <button 
            onClick={() => alert('Opening AI command palette')}
            className="w-full bg-gray-600/20 hover:bg-gray-600/30 border border-gray-500/30 rounded px-3 py-2 text-xs text-gray-300 transition-colors flex items-center justify-center space-x-2"
          >
            <HelpCircle className="w-3 h-3" />
            <span>More AI options...</span>
          </button>
        </div>
      </div>

      {aiResponse && (
        <div className="bg-control-800/50 rounded-lg p-4 border border-control-600">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-xs font-semibold text-neutral">AI Response</h4>
            <div className="flex items-center space-x-2">
              <AiConfidenceBadge 
                confidence={currentAiResponse?.confidence || 0.85} 
                size="sm" 
                showBreakdown={true}
                breakdown={{
                  contextQuality: 0.9,
                  promptClarity: 0.8,
                  domainMatch: 0.85,
                  safetyCheck: 0.9
                }}
              />
              <button
                onClick={() => setAiResponse('')}
                className="p-1 text-control-400 hover:text-error hover:bg-control-800/50 rounded transition-colors"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          </div>
          <div className="text-xs text-control-300 whitespace-pre-wrap max-h-32 overflow-y-auto">
            {aiResponse}
          </div>
          <div className="flex items-center justify-between mt-2 pt-2 border-t border-control-700">
            <div className="flex items-center space-x-2">
              <button className="p-1 text-control-400 hover:text-neutral hover:bg-control-800/50 rounded transition-colors">
                <Copy className="w-3 h-3" />
              </button>
              <button className="p-1 text-control-400 hover:text-neutral hover:bg-control-800/50 rounded transition-colors">
                <Download className="w-3 h-3" />
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-1 text-green-400 hover:bg-green-400/20 rounded transition-colors">
                <Check className="w-3 h-3" />
              </button>
              <button className="p-1 text-red-400 hover:bg-red-400/20 rounded transition-colors">
                <X className="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>
      )}

      {currentAiResponse && (
        <MCPInspector response={currentAiResponse} />
      )}

      <div className="bg-control-800/50 rounded-lg p-4">
        <h4 className="text-xs font-semibold text-neutral mb-2">Recent Suggestions</h4>
        <div className="space-y-2">
          <div className="text-xs text-control-300 p-2 bg-base-900/50 rounded flex items-start space-x-2">
            <span className="text-blue-400">💡</span>
            <span>Consider adding a delay timer before pump restart for motor protection</span>
          </div>
          <div className="text-xs text-control-300 p-2 bg-base-900/50 rounded flex items-start space-x-2">
            <span className="text-amber-400">⚠️</span>
            <span>Emergency stop logic should use hardware safety circuits</span>
          </div>
          <div className="text-xs text-control-300 p-2 bg-base-900/50 rounded flex items-start space-x-2">
            <span className="text-green-400">✅</span>
            <span>Good use of seal-in circuit for motor control</span>
          </div>
          <button className="w-full text-center text-xs text-blue-400 hover:text-blue-300 mt-1">
            View all suggestions
          </button>
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'properties': return renderProperties();
      case 'tags': return renderTags();
      case 'timing': return renderTiming();
      case 'safety': return renderSafety();
      case 'context': return renderContext();
      case 'ai': return renderAIAssistant();
      default: return renderProperties();
    }
  };

  return (
    <>
      <div className="w-80 bg-base-dark border-l border-control-600 flex flex-col">
        <div className="border-b border-control-600">
          <div className="flex">
            {sections.map(section => {
              const IconComponent = section.icon;
              return (
                <button
                  key={section.id}
                  className={`flex-1 flex items-center justify-center px-3 py-3 text-xs transition-colors ${
                    activeSection === section.id
                      ? 'bg-control-800 text-neutral border-b-2 border-accent'
                      : 'text-control-400 hover:text-neutral hover:bg-control-800/50'
                  }`}
                  onClick={() => setActiveSection(section.id)}
                >
                  <IconComponent className={`w-4 h-4 ${section.id === 'ai' ? 'text-purple-400' : ''}`} />
                </button>
              );
            })}
          </div>
        </div>
        
        <div className="flex-1 overflow-y-auto">
          {renderContent()}
        </div>
      </div>

      {/* AI Suggestion Modal */}
      {showSuggestionModal && currentSuggestion && (
        <AiSuggestionModal
          isOpen={showSuggestionModal}
          onClose={() => setShowSuggestionModal(false)}
          suggestion={currentSuggestion}
          onApply={() => {
            setAiResponse(currentSuggestion.content);
            setShowSuggestionModal(false);
          }}
          onEdit={(newPrompt) => {
            alert(`Would re-run with prompt: ${newPrompt}`);
            setShowSuggestionModal(false);
          }}
          requireReview={requireReview}
        />
      )}
    </>
  );
};

export default PropertiesPanel;