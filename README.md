# LUREON Industrial Automation IDE

![LUREON Logo](https://img.shields.io/badge/LUREON-Industrial%20Automation%20IDE-00C2A8?style=for-the-badge&logo=data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBmaWxsPSJub25lIj4KICA8cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHJ4PSIxMCIgZmlsbD0iIzAwQzJBOCIvPgogIDxyZWN0IHg9IjEyIiB5PSIxMCIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEyIiByeD0iMiIgZmlsbD0iIzExMTIxMyIvPgogIDxyZWN0IHg9IjYiIHk9IjMwIiB3aWR0aD0iNCIgaGVpZ2h0PSIxMCIgcng9IjEiIGZpbGw9IiMwMEMyQTgiLz4KICA8cmVjdCB4PSIxNiIgeT0iMzAiIHdpZHRoPSI0IiBoZWlnaHQ9IjEwIiByeD0iMSIgZmlsbD0iIzAwQzJBOCIvPgogIDxyZWN0IHg9IjI2IiB5PSIzMCIgd2lkdGg9IjQiIGhlaWdodD0iMTAiIHJ4PSIxIiBmaWxsPSIjMDBDMkE4Ii8+Cjwvc3ZnPg==)

A modern, web-based Industrial Automation IDE for PLC programming with real-time collaboration, offline capabilities, and enterprise-grade features. Built with React, TypeScript, and modern web technologies.

## 🚀 Quick Start for Developers

### Local Development

```bash
# Clone the repository
git clone https://github.com/lureon/ide.git
cd ide

# Install dependencies
npm install

# Start development server
npm run dev

# Start full stack (frontend + AI backend)
npm run dev:full

# Build for production
npm run build
```

### Docker Development Environment

```bash
# Build and start the development environment
docker-compose -f docker-compose.dev.yml up -d

# Access the application at http://localhost:5173
```

## 🧠 AI Backend Setup

The LUREON IDE includes a powerful AI assistant that requires a backend service. You can run this locally during development:

```bash
# Start the AI backend server
npm run ai-server

# Or run both frontend and AI backend together
npm run dev:full
```

### AI Backend Docker Setup

For local development with Docker:

```bash
# Navigate to the AI backend directory
cd src/ai/backend

# Build and start the AI backend services
docker-compose up -d

# This will start:
# - AI backend service
# - Redis for caching
# - PostgreSQL for data storage
# - Nginx as reverse proxy
```

### Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# AI Provider API Keys
VITE_OPENAI_API_KEY=your_openai_key
VITE_CLAUDE_API_KEY=your_claude_key

# Database (if using Supabase)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Server Configuration
PORT=3001
FRONTEND_URL=http://localhost:5173
```

## 🏗️ Project Structure

```
src/
├── ai/                    # AI Assistant features
│   ├── backend/           # AI backend service
│   ├── components/        # AI UI components
│   ├── hooks/             # AI React hooks
│   └── services/          # AI client services
├── components/            # React components
│   ├── collaboration/     # Real-time collaboration
│   ├── debugging/         # Signal tracing and debugging
│   ├── editors/           # Programming language editors
│   ├── enterprise/        # Enterprise features (audit, users)
│   ├── hmi/               # HMI design and preview
│   ├── io/                # I/O mapping and configuration
│   ├── network/           # Network topology visualization
│   ├── simulation/        # PLC simulation engine
│   ├── templates/         # Code template library
│   └── workspace/         # Workspace and sync management
├── services/              # Business logic and APIs
│   ├── collaborationEngine.ts  # Real-time collaboration
│   ├── enterpriseServices.ts   # Enterprise features
│   ├── projectPackager.ts      # Project import/export
│   └── syncEngine.ts           # Offline/online sync
├── store/                 # State management (Zustand)
├── types/                 # TypeScript type definitions
└── main.tsx              # Application entry point
```

## 🔧 Technology Stack

- **Frontend**: React, TypeScript, Tailwind CSS
- **State Management**: Zustand
- **Code Editor**: Monaco Editor
- **Real-time Collaboration**: Socket.IO
- **AI Integration**: OpenAI, Claude, Ollama
- **Offline Support**: IndexedDB, custom sync engine
- **Backend**: Node.js, Express
- **Database**: PostgreSQL (via Supabase)
- **Caching**: Redis
- **Containerization**: Docker, Docker Compose

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run tests with UI
npm run test:ui

# Generate coverage report
npm run test:coverage
```

## 🚢 Deployment

### Production Build

```bash
# Build the application
npm run build

# Preview the production build locally
npm run preview
```

### Docker Production Deployment

```bash
# Build and deploy with Docker Compose
docker-compose -f docker-compose.prod.yml up -d
```

### Kubernetes Deployment

Sample Kubernetes deployment files are available in the `k8s/` directory.

## 🔒 Security Considerations

- All projects are encrypted at rest
- TLS 1.3 for data in transit
- Role-based access control
- Audit logging for compliance
- Digital signatures for AI-generated code

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## 📄 License

Copyright © 2025 LUREON Industrial Automation IDE  
Licensed under the MIT License

## 🆘 Support

- [Documentation](docs/user-guide.md)
- [API Reference](docs/api-reference.md)
- [GitHub Issues](https://github.com/lureon/ide/issues)
- [Discord Community](https://discord.gg/lureon)

---

**Built with ❤️ for Industrial Automation Engineers**

*LUREON - Where Industrial Control Meets Modern Software Development*