import React from 'react';
import { Clock, Zap, TrendingUp, CheckCircle2 } from 'lucide-react';

interface AiImpactMetrics {
  timeSaved: number; // in seconds
  linesGenerated: number;
  suggestionsAccepted: number;
  errorsAvoided: number;
}

interface AiImpactBadgeProps {
  metrics: AiImpactMetrics;
  size?: 'sm' | 'md' | 'lg';
  showDetails?: boolean;
  className?: string;
}

const AiImpactBadge: React.FC<AiImpactBadgeProps> = ({
  metrics,
  size = 'md',
  showDetails = false,
  className = ''
}) => {
  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    return `${Math.round(seconds / 3600)}h`;
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'px-2 py-1 text-xs';
      case 'lg': return 'px-4 py-2 text-base';
      default: return 'px-3 py-1 text-sm';
    }
  };

  const primaryMetric = metrics.timeSaved > 0 ? formatTime(metrics.timeSaved) : `${metrics.linesGenerated} LOC`;

  if (!showDetails) {
    return (
      <div className={`inline-flex items-center space-x-1 bg-green-600/20 text-green-400 border border-green-600/30 rounded-full ${getSizeClasses()} ${className}`}>
        <Clock className="w-3 h-3" />
        <span className="font-medium">⏱️ Saved {primaryMetric}</span>
      </div>
    );
  }

  return (
    <div className={`bg-gradient-to-r from-green-600/20 to-blue-600/20 border border-green-600/30 rounded-lg p-3 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-semibold text-white">AI Impact</h4>
        <TrendingUp className="w-4 h-4 text-green-400" />
      </div>
      
      <div className="grid grid-cols-2 gap-3">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-green-400 mb-1">
            <Clock className="w-3 h-3" />
            <span className="text-lg font-bold">{formatTime(metrics.timeSaved)}</span>
          </div>
          <div className="text-xs text-gray-400">Time Saved</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-blue-400 mb-1">
            <Zap className="w-3 h-3" />
            <span className="text-lg font-bold">{metrics.linesGenerated}</span>
          </div>
          <div className="text-xs text-gray-400">Lines Generated</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-purple-400 mb-1">
            <CheckCircle2 className="w-3 h-3" />
            <span className="text-lg font-bold">{metrics.suggestionsAccepted}</span>
          </div>
          <div className="text-xs text-gray-400">Suggestions Used</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 text-orange-400 mb-1">
            <span className="text-lg font-bold">🛡️</span>
            <span className="text-lg font-bold">{metrics.errorsAvoided}</span>
          </div>
          <div className="text-xs text-gray-400">Errors Avoided</div>
        </div>
      </div>
      
      <div className="mt-3 pt-2 border-t border-gray-700">
        <div className="text-xs text-gray-400 text-center">
          Estimated productivity boost: <span className="text-green-400 font-semibold">
            {Math.round((metrics.timeSaved / 3600) * 100)}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default AiImpactBadge;