import React, { useState } from 'react';
import { AIResponse } from '../hooks/useAI';
import { useMCPContext, ContextItem } from '../context/MCPStore';
import { 
  Info, 
  ChevronDown, 
  ChevronUp, 
  Tag, 
  FileText, 
  Code, 
  Shield, 
  Clock, 
  Eye, 
  EyeOff,
  Fingerprint,
  Sparkles
} from 'lucide-react';

interface MCPInspectorProps {
  response: AIResponse;
  className?: string;
}

const MCPInspector: React.FC<MCPInspectorProps> = ({ response, className = '' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showRawPrompt, setShowRawPrompt] = useState(false);
  const { contextItems, getContextConfidence } = useMCPContext();
  
  // Generate a deterministic "fingerprint" for this response based on context
  const generateFingerprint = () => {
    const contextTypes = contextItems.map(item => item.type).sort().join('-');
    const contextCount = contextItems.length;
    const timestamp = response.timestamp.getTime();
    
    return `${contextTypes}-${contextCount}-${timestamp.toString(36)}`.substring(0, 8);
  };
  
  const confidence = getContextConfidence();
  const fingerprint = generateFingerprint();
  
  // Count context items by type
  const contextCounts = contextItems.reduce((acc, item) => {
    acc[item.type] = (acc[item.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  // Get the raw prompt that would be sent to the AI
  const getRawPrompt = () => {
    const { serializeForPrompt } = useMCPContext();
    const contextPrompt = serializeForPrompt();
    
    return `
# Context Information
${contextPrompt}

# User Request
${response.prompt || 'No prompt available'}
`;
  };
  
  return (
    <div className={`bg-gray-800 rounded-lg border border-gray-700 overflow-hidden ${className}`}>
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-700/50 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <Info className="w-4 h-4 text-blue-400" />
          <span className="text-white text-sm font-medium">Context & Metadata</span>
        </div>
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-1 text-xs text-gray-400">
            <Fingerprint className="w-3 h-3" />
            <span>{fingerprint}</span>
          </div>
          {isExpanded ? (
            <ChevronUp className="w-4 h-4 text-gray-400" />
          ) : (
            <ChevronDown className="w-4 h-4 text-gray-400" />
          )}
        </div>
      </button>
      
      {isExpanded && (
        <div className="p-3 border-t border-gray-700">
          <div className="space-y-4">
            {/* Context Summary */}
            <div>
              <h4 className="text-white text-sm font-medium mb-2">Context Summary</h4>
              <div className="grid grid-cols-3 gap-2">
                {Object.entries(contextCounts).map(([type, count]) => (
                  <div key={type} className="bg-gray-700/50 rounded p-2 flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {type === 'tag' && <Tag className="w-3 h-3 text-blue-400" />}
                      {type === 'program' && <FileText className="w-3 h-3 text-green-400" />}
                      {type === 'rung' && <Code className="w-3 h-3 text-yellow-400" />}
                      {type === 'safety' && <Shield className="w-3 h-3 text-red-400" />}
                      {type === 'version' && <Clock className="w-3 h-3 text-gray-400" />}
                      {type === 'standard' && <Info className="w-3 h-3 text-teal-400" />}
                      <span className="text-xs text-gray-300 capitalize">{type}</span>
                    </div>
                    <span className="text-xs text-white">{count}</span>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Confidence Score */}
            <div>
              <div className="flex items-center justify-between mb-1">
                <h4 className="text-white text-sm font-medium">Context Confidence</h4>
                <span className={`text-xs ${
                  confidence >= 0.8 ? 'text-green-400' :
                  confidence >= 0.5 ? 'text-yellow-400' :
                  'text-red-400'
                }`}>
                  {Math.round(confidence * 100)}%
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all ${
                    confidence >= 0.8 ? 'bg-green-500' :
                    confidence >= 0.5 ? 'bg-yellow-500' :
                    'bg-red-500'
                  }`}
                  style={{ width: `${confidence * 100}%` }}
                ></div>
              </div>
            </div>
            
            {/* Raw Prompt Toggle */}
            <div>
              <button
                onClick={() => setShowRawPrompt(!showRawPrompt)}
                className="flex items-center space-x-2 text-sm text-blue-400 hover:text-blue-300"
              >
                {showRawPrompt ? (
                  <>
                    <EyeOff className="w-4 h-4" />
                    <span>Hide Raw Prompt</span>
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4" />
                    <span>Show Raw Prompt</span>
                  </>
                )}
              </button>
              
              {showRawPrompt && (
                <div className="mt-2 bg-gray-900 rounded p-3 max-h-64 overflow-y-auto">
                  <pre className="text-xs text-gray-300 whitespace-pre-wrap">{getRawPrompt()}</pre>
                </div>
              )}
            </div>
            
            {/* Response Metadata */}
            <div>
              <h4 className="text-white text-sm font-medium mb-2">Response Metadata</h4>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-400">Response ID</span>
                  <span className="text-white font-mono">{response.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Generated At</span>
                  <span className="text-white">{response.timestamp.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Processing Time</span>
                  <span className="text-white">{response.timeTaken?.toFixed(2) || '?'} seconds</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">AI Confidence</span>
                  <span className={`${
                    response.confidence >= 0.8 ? 'text-green-400' :
                    response.confidence >= 0.5 ? 'text-yellow-400' :
                    'text-red-400'
                  }`}>
                    {Math.round(response.confidence * 100)}%
                  </span>
                </div>
              </div>
            </div>
            
            <div className="text-xs text-gray-500 flex items-center justify-center space-x-1 pt-2 border-t border-gray-700">
              <Sparkles className="w-3 h-3" />
              <span>Powered by Model Context Protocol</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MCPInspector;