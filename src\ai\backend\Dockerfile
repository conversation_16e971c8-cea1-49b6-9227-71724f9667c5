FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ai/backend ./src/ai/backend
COPY src/ai/services ./src/ai/services
COPY src/ai/hooks ./src/ai/hooks
COPY src/types ./src/types

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S aiserver -u 1001

# Change ownership
RUN chown -R aiserver:nodejs /app
USER aiserver

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Start server
CMD ["node", "--loader", "ts-node/esm", "src/ai/backend/server.ts"]