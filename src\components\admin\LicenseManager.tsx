import React, { useState, useEffect } from 'react';
import { 
  Key, 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  Download, 
  Copy, 
  CheckCircle2, 
  XCircle, 
  AlertTriangle, 
  Clock, 
  User, 
  Users, 
  Building, 
  Mail, 
  Calendar, 
  X, 
  RefreshCw,
  Shield,
  FileText,
  BarChart3
} from 'lucide-react';

interface LicenseData {
  id: string;
  key: string;
  type: 'basic' | 'standard' | 'professional' | 'enterprise' | 'trial';
  status: 'active' | 'expired' | 'revoked' | 'pending';
  organization: string;
  contactName: string;
  contactEmail: string;
  seats: number;
  usedSeats: number;
  features: string[];
  validFrom: Date;
  validUntil: Date;
  createdAt: Date;
  lastModified?: Date;
  notes?: string;
}

const LicenseManager: React.FC = () => {
  const [licenses, setLicenses] = useState<LicenseData[]>([]);
  const [filteredLicenses, setFilteredLicenses] = useState<LicenseData[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedLicense, setSelectedLicense] = useState<LicenseData | null>(null);
  const [showAddLicenseModal, setShowAddLicenseModal] = useState(false);
  const [showRevokeConfirmation, setShowRevokeConfirmation] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [newLicense, setNewLicense] = useState({
    organization: '',
    contactName: '',
    contactEmail: '',
    type: 'professional' as const,
    seats: 5,
    validMonths: 12,
    notes: ''
  });

  // Mock data for demonstration
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const mockLicenses: LicenseData[] = [
        {
          id: '1',
          key: 'LURN-PRO-1234-5678-9ABC-DEF0',
          type: 'professional',
          status: 'active',
          organization: 'Manufacturing Inc.',
          contactName: 'John Smith',
          contactEmail: '<EMAIL>',
          seats: 10,
          usedSeats: 8,
          features: ['ladder_editor', 'st_editor', 'fbd_editor', 'simulation', 'deployment', 'collaboration'],
          validFrom: new Date(2023, 0, 1),
          validUntil: new Date(2023, 11, 31),
          createdAt: new Date(2022, 11, 15),
          lastModified: new Date(2023, 2, 10),
          notes: 'Annual renewal, key account'
        },
        {
          id: '2',
          key: 'LURN-ENT-2345-6789-ABCD-EF01',
          type: 'enterprise',
          status: 'active',
          organization: 'Global Automation Corp',
          contactName: 'Sarah Johnson',
          contactEmail: '<EMAIL>',
          seats: 50,
          usedSeats: 42,
          features: ['ladder_editor', 'st_editor', 'fbd_editor', 'sfc_editor', 'safety_editor', 'simulation', 'deployment', 'collaboration', 'audit_trail', 'enterprise_support'],
          validFrom: new Date(2023, 3, 1),
          validUntil: new Date(2025, 2, 31),
          createdAt: new Date(2023, 2, 15),
          notes: 'Multi-year agreement, premium support'
        },
        {
          id: '3',
          key: 'LURN-STD-3456-789A-BCDE-F012',
          type: 'standard',
          status: 'active',
          organization: 'Acme Controls',
          contactName: 'Robert Lee',
          contactEmail: '<EMAIL>',
          seats: 5,
          usedSeats: 5,
          features: ['ladder_editor', 'st_editor', 'simulation', 'deployment'],
          validFrom: new Date(2023, 6, 1),
          validUntil: new Date(2024, 5, 30),
          createdAt: new Date(2023, 5, 25)
        },
        {
          id: '4',
          key: 'LURN-TRL-4567-89AB-CDEF-0123',
          type: 'trial',
          status: 'expired',
          organization: 'New Client LLC',
          contactName: 'Emma Wilson',
          contactEmail: '<EMAIL>',
          seats: 2,
          usedSeats: 0,
          features: ['ladder_editor', 'st_editor', 'simulation'],
          validFrom: new Date(2023, 8, 1),
          validUntil: new Date(2023, 8, 30),
          createdAt: new Date(2023, 7, 28)
        },
        {
          id: '5',
          key: 'LURN-BAS-5678-9ABC-DEF0-1234',
          type: 'basic',
          status: 'active',
          organization: 'Small Shop Automation',
          contactName: 'David Brown',
          contactEmail: '<EMAIL>',
          seats: 3,
          usedSeats: 2,
          features: ['ladder_editor', 'simulation'],
          validFrom: new Date(2023, 9, 15),
          validUntil: new Date(2024, 9, 14),
          createdAt: new Date(2023, 9, 10)
        },
        {
          id: '6',
          key: 'LURN-PRO-6789-ABCD-EF01-2345',
          type: 'professional',
          status: 'revoked',
          organization: 'Former Client Inc.',
          contactName: 'Michael Taylor',
          contactEmail: '<EMAIL>',
          seats: 8,
          usedSeats: 0,
          features: ['ladder_editor', 'st_editor', 'fbd_editor', 'simulation', 'deployment', 'collaboration'],
          validFrom: new Date(2023, 2, 1),
          validUntil: new Date(2024, 1, 29),
          createdAt: new Date(2023, 1, 20),
          lastModified: new Date(2023, 4, 15),
          notes: 'License revoked due to payment issues'
        },
        {
          id: '7',
          key: 'LURN-ENT-789A-BCDE-F012-3456',
          type: 'enterprise',
          status: 'pending',
          organization: 'New Enterprise Co.',
          contactName: 'Jennifer Adams',
          contactEmail: '<EMAIL>',
          seats: 25,
          usedSeats: 0,
          features: ['ladder_editor', 'st_editor', 'fbd_editor', 'sfc_editor', 'safety_editor', 'simulation', 'deployment', 'collaboration', 'audit_trail', 'enterprise_support'],
          validFrom: new Date(2023, 11, 1),
          validUntil: new Date(2024, 10, 30),
          createdAt: new Date(2023, 10, 25),
          notes: 'Awaiting payment confirmation'
        }
      ];
      
      setLicenses(mockLicenses);
      setFilteredLicenses(mockLicenses);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter licenses based on search and filters
  useEffect(() => {
    let result = licenses;
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(license => 
        license.organization.toLowerCase().includes(query) || 
        license.contactName.toLowerCase().includes(query) ||
        license.contactEmail.toLowerCase().includes(query) ||
        license.key.toLowerCase().includes(query)
      );
    }
    
    if (filterType !== 'all') {
      result = result.filter(license => license.type === filterType);
    }
    
    if (filterStatus !== 'all') {
      result = result.filter(license => license.status === filterStatus);
    }
    
    setFilteredLicenses(result);
  }, [licenses, searchQuery, filterType, filterStatus]);

  const handleAddLicense = () => {
    if (!newLicense.organization || !newLicense.contactName || !newLicense.contactEmail) return;
    
    const validFrom = new Date();
    const validUntil = new Date();
    validUntil.setMonth(validUntil.getMonth() + parseInt(newLicense.validMonths.toString()));
    
    const license: LicenseData = {
      id: (licenses.length + 1).toString(),
      key: generateLicenseKey(newLicense.type),
      type: newLicense.type,
      status: 'active',
      organization: newLicense.organization,
      contactName: newLicense.contactName,
      contactEmail: newLicense.contactEmail,
      seats: newLicense.seats,
      usedSeats: 0,
      features: getLicenseFeatures(newLicense.type),
      validFrom,
      validUntil,
      createdAt: new Date(),
      notes: newLicense.notes
    };
    
    setLicenses([...licenses, license]);
    setShowAddLicenseModal(false);
    setNewLicense({
      organization: '',
      contactName: '',
      contactEmail: '',
      type: 'professional',
      seats: 5,
      validMonths: 12,
      notes: ''
    });
  };

  const handleRevokeLicense = () => {
    if (!selectedLicense) return;
    
    setLicenses(licenses.map(license => 
      license.id === selectedLicense.id 
        ? { ...license, status: 'revoked', lastModified: new Date() } 
        : license
    ));
    setSelectedLicense(null);
    setShowRevokeConfirmation(false);
  };

  const generateLicenseKey = (type: string): string => {
    const prefix = type === 'basic' ? 'LURN-BAS' :
                  type === 'standard' ? 'LURN-STD' :
                  type === 'professional' ? 'LURN-PRO' :
                  type === 'enterprise' ? 'LURN-ENT' : 'LURN-TRL';
    
    const randomPart = () => Math.random().toString(16).substring(2, 6).toUpperCase();
    
    return `${prefix}-${randomPart()}-${randomPart()}-${randomPart()}-${randomPart()}`;
  };

  const getLicenseFeatures = (type: string): string[] => {
    switch (type) {
      case 'basic':
        return ['ladder_editor', 'simulation'];
      case 'standard':
        return ['ladder_editor', 'st_editor', 'simulation', 'deployment'];
      case 'professional':
        return ['ladder_editor', 'st_editor', 'fbd_editor', 'simulation', 'deployment', 'collaboration'];
      case 'enterprise':
        return ['ladder_editor', 'st_editor', 'fbd_editor', 'sfc_editor', 'safety_editor', 'simulation', 'deployment', 'collaboration', 'audit_trail', 'enterprise_support'];
      case 'trial':
        return ['ladder_editor', 'st_editor', 'simulation'];
      default:
        return [];
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'basic':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      case 'standard':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'professional':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'enterprise':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'trial':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'expired':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'revoked':
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle2 className="w-4 h-4 text-green-400" />;
      case 'expired':
        return <Clock className="w-4 h-4 text-red-400" />;
      case 'revoked':
        return <XCircle className="w-4 h-4 text-gray-400" />;
      case 'pending':
        return <AlertTriangle className="w-4 h-4 text-yellow-400" />;
      default:
        return null;
    }
  };

  const getDaysRemaining = (endDate: Date): number => {
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header with search and filters */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 border border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">License Management</h2>
          <button
            onClick={() => setShowAddLicenseModal(true)}
            className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Issue License</span>
          </button>
        </div>
        
        <div className="grid grid-cols-3 gap-4">
          <div className="col-span-3 sm:col-span-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search licenses..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
          
          <div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Types</option>
              <option value="basic">Basic</option>
              <option value="standard">Standard</option>
              <option value="professional">Professional</option>
              <option value="enterprise">Enterprise</option>
              <option value="trial">Trial</option>
            </select>
          </div>
          
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="expired">Expired</option>
              <option value="revoked">Revoked</option>
              <option value="pending">Pending</option>
            </select>
          </div>
        </div>
      </div>

      {/* License List */}
      <div className="flex-1 bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex items-center space-x-2">
              <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />
              <span className="text-white">Loading licenses...</span>
            </div>
          </div>
        ) : filteredLicenses.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full">
            <Key className="w-16 h-16 text-gray-600 mb-4" />
            <h3 className="text-lg font-medium text-white">No licenses found</h3>
            <p className="text-gray-400 mt-2">Try adjusting your search or filters</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Organization</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Seats</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Valid Until</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Contact</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {filteredLicenses.map(license => (
                  <tr 
                    key={license.id} 
                    className="hover:bg-gray-700/50 cursor-pointer"
                    onClick={() => setSelectedLicense(license)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        <Building className="w-5 h-5 text-gray-400" />
                        <span className="text-white">{license.organization}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 rounded-full text-xs border ${getTypeBadgeColor(license.type)}`}>
                        {license.type.charAt(0).toUpperCase() + license.type.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(license.status)}
                        <span className={`capitalize ${
                          license.status === 'active' ? 'text-green-400' :
                          license.status === 'expired' ? 'text-red-400' :
                          license.status === 'revoked' ? 'text-gray-400' :
                          'text-yellow-400'
                        }`}>
                          {license.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-gray-300">
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4 text-gray-400" />
                        <span>{license.usedSeats} / {license.seats}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <span className="text-gray-300">{license.validUntil.toLocaleDateString()}</span>
                        {license.status === 'active' && (
                          <span className={`text-xs ${
                            getDaysRemaining(license.validUntil) < 30 ? 'text-red-400' :
                            getDaysRemaining(license.validUntil) < 90 ? 'text-yellow-400' :
                            'text-green-400'
                          }`}>
                            {getDaysRemaining(license.validUntil)} days left
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-gray-300 text-sm">
                      {license.contactName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            navigator.clipboard.writeText(license.key);
                            alert('License key copied to clipboard');
                          }}
                          className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                          title="Copy License Key"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            // Download license file functionality
                          }}
                          className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                          title="Download License"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* License Details Sidebar - Show when a license is selected */}
      {selectedLicense && (
        <div className="fixed inset-y-0 right-0 w-96 bg-gray-800 border-l border-gray-700 p-6 overflow-y-auto shadow-xl z-10">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">License Details</h3>
            <button
              onClick={() => setSelectedLicense(null)}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="space-y-6">
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <span className={`px-2 py-1 rounded text-xs ${getTypeBadgeColor(selectedLicense.type)}`}>
                  {selectedLicense.type.charAt(0).toUpperCase() + selectedLicense.type.slice(1)}
                </span>
                <span className={`px-2 py-1 rounded text-xs ${getStatusBadgeColor(selectedLicense.status)}`}>
                  {selectedLicense.status.toUpperCase()}
                </span>
              </div>
              <h4 className="text-white font-medium mb-2">{selectedLicense.organization}</h4>
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <Calendar className="w-4 h-4" />
                <span>Created on {selectedLicense.createdAt.toLocaleDateString()}</span>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">License Key</h5>
              <div className="bg-gray-800 p-3 rounded-lg flex items-center justify-between">
                <code className="text-blue-400 font-mono text-sm">{selectedLicense.key}</code>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(selectedLicense.key);
                    alert('License key copied to clipboard');
                  }}
                  className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
                >
                  <Copy className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">License Information</h5>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">Valid From</span>
                  <span className="text-white">{selectedLicense.validFrom.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Valid Until</span>
                  <span className={`text-white ${
                    selectedLicense.status === 'active' && getDaysRemaining(selectedLicense.validUntil) < 30 
                      ? 'text-red-400' 
                      : ''
                  }`}>
                    {selectedLicense.validUntil.toLocaleDateString()}
                    {selectedLicense.status === 'active' && (
                      <span className="text-xs ml-2">
                        ({getDaysRemaining(selectedLicense.validUntil)} days left)
                      </span>
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Seats</span>
                  <span className="text-white">{selectedLicense.usedSeats} / {selectedLicense.seats} used</span>
                </div>
                {selectedLicense.lastModified && (
                  <div className="flex justify-between">
                    <span className="text-gray-400">Last Modified</span>
                    <span className="text-white">{selectedLicense.lastModified.toLocaleDateString()}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">Contact Information</h5>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4 text-gray-400" />
                  <span className="text-white">{selectedLicense.contactName}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4 text-gray-400" />
                  <span className="text-white">{selectedLicense.contactEmail}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Building className="w-4 h-4 text-gray-400" />
                  <span className="text-white">{selectedLicense.organization}</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-700 rounded-lg p-4">
              <h5 className="text-white font-medium mb-3">Features</h5>
              <div className="grid grid-cols-2 gap-2">
                {selectedLicense.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <CheckCircle2 className="w-4 h-4 text-green-400" />
                    <span className="text-white">{feature.replace('_', ' ')}</span>
                  </div>
                ))}
              </div>
            </div>
            
            {selectedLicense.notes && (
              <div className="bg-gray-700 rounded-lg p-4">
                <h5 className="text-white font-medium mb-3">Notes</h5>
                <p className="text-gray-300 text-sm">{selectedLicense.notes}</p>
              </div>
            )}
            
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  // Edit license functionality
                }}
                className="flex-1 flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Edit className="w-4 h-4" />
                <span>Edit</span>
              </button>
              <button
                onClick={() => {
                  // Download license file functionality
                }}
                className="flex-1 flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Download</span>
              </button>
            </div>
            
            {selectedLicense.status === 'active' && (
              <button
                onClick={() => setShowRevokeConfirmation(true)}
                className="w-full flex items-center justify-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <XCircle className="w-4 h-4" />
                <span>Revoke License</span>
              </button>
            )}
          </div>
        </div>
      )}

      {/* Add License Modal */}
      {showAddLicenseModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-white">Issue New License</h3>
              <button
                onClick={() => setShowAddLicenseModal(false)}
                className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-gray-300 mb-2">Organization</label>
                <input
                  type="text"
                  value={newLicense.organization}
                  onChange={(e) => setNewLicense({...newLicense, organization: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter organization name"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Contact Name</label>
                <input
                  type="text"
                  value={newLicense.contactName}
                  onChange={(e) => setNewLicense({...newLicense, contactName: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter contact name"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Contact Email</label>
                <input
                  type="email"
                  value={newLicense.contactEmail}
                  onChange={(e) => setNewLicense({...newLicense, contactEmail: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter contact email"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">License Type</label>
                <select
                  value={newLicense.type}
                  onChange={(e) => setNewLicense({...newLicense, type: e.target.value as any})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="basic">Basic</option>
                  <option value="standard">Standard</option>
                  <option value="professional">Professional</option>
                  <option value="enterprise">Enterprise</option>
                  <option value="trial">Trial</option>
                </select>
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Number of Seats</label>
                <input
                  type="number"
                  min="1"
                  value={newLicense.seats}
                  onChange={(e) => setNewLicense({...newLicense, seats: parseInt(e.target.value)})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Valid for (months)</label>
                <input
                  type="number"
                  min="1"
                  value={newLicense.validMonths}
                  onChange={(e) => setNewLicense({...newLicense, validMonths: parseInt(e.target.value)})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-300 mb-2">Notes (Optional)</label>
                <textarea
                  value={newLicense.notes}
                  onChange={(e) => setNewLicense({...newLicense, notes: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none"
                  placeholder="Enter any additional notes"
                ></textarea>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowAddLicenseModal(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleAddLicense}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                disabled={!newLicense.organization || !newLicense.contactName || !newLicense.contactEmail}
              >
                Issue License
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Revoke Confirmation Modal */}
      {showRevokeConfirmation && selectedLicense && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-20">
          <div className="bg-gray-800 rounded-lg w-full max-w-md p-6 border border-gray-700">
            <div className="flex items-center space-x-3 text-red-400 mb-4">
              <AlertTriangle className="w-6 h-6" />
              <h3 className="text-xl font-semibold">Confirm Revocation</h3>
            </div>
            
            <p className="text-white mb-2">Are you sure you want to revoke this license?</p>
            <p className="text-gray-400 mb-6">This will immediately terminate access for all users of this organization.</p>
            
            <div className="bg-gray-700/50 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-white font-medium">{selectedLicense.organization}</h4>
                <span className={`px-2 py-1 rounded text-xs ${getTypeBadgeColor(selectedLicense.type)}`}>
                  {selectedLicense.type.charAt(0).toUpperCase() + selectedLicense.type.slice(1)}
                </span>
              </div>
              <div className="text-sm text-gray-400">
                <p>License Key: {selectedLicense.key}</p>
                <p>Seats: {selectedLicense.usedSeats} / {selectedLicense.seats} used</p>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowRevokeConfirmation(false)}
                className="px-4 py-2 text-gray-300 hover:text-white"
              >
                Cancel
              </button>
              <button
                onClick={handleRevokeLicense}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Revoke License
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LicenseManager;