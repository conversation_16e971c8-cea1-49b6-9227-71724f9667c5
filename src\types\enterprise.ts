// Enterprise-grade type definitions for commercial PLC platform

export interface User {
  id: string;
  username: string;
  email: string;
  role: UserRole;
  permissions: Permission[];
  lastActive: Date;
  currentSession?: SessionInfo;
  preferences: UserPreferences;
  certifications?: Certification[];
}

export interface UserRole {
  id: string;
  name: 'admin' | 'engineer' | 'operator' | 'viewer' | 'safety_engineer';
  level: number;
  description: string;
  defaultPermissions: Permission[];
}

export interface Permission {
  id: string;
  resource: 'project' | 'program' | 'tag' | 'deployment' | 'safety' | 'system';
  action: 'read' | 'write' | 'delete' | 'deploy' | 'approve' | 'audit';
  scope: 'global' | 'project' | 'program';
}

export interface SessionInfo {
  id: string;
  startTime: Date;
  lastActivity: Date;
  ipAddress: string;
  userAgent: string;
  location?: string;
}

export interface UserPreferences {
  theme: 'dark' | 'light' | 'safety';
  language: string;
  keyboardShortcuts: Record<string, string>;
  autoSave: boolean;
  autoSaveInterval: number;
  defaultPLCBrand: string;
  gridSnap: boolean;
  showMinimap: boolean;
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  level: 'SIL1' | 'SIL2' | 'SIL3' | 'TUV' | 'UL' | 'CSA';
  expiryDate: Date;
  verified: boolean;
}

// Audit and Compliance
export interface AuditLog {
  id: string;
  timestamp: Date;
  userId: string;
  action: AuditAction;
  resource: string;
  resourceId: string;
  oldValue?: any;
  newValue?: any;
  ipAddress: string;
  sessionId: string;
  compliance?: ComplianceInfo;
}

export interface AuditAction {
  type: 'create' | 'read' | 'update' | 'delete' | 'deploy' | 'approve' | 'reject';
  category: 'project' | 'program' | 'tag' | 'user' | 'system' | 'safety';
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface ComplianceInfo {
  standard: 'FDA_CFR_21_Part_11' | 'GAMP_5' | 'IEC_61131_3' | 'IEC_61508' | 'ISO_13849';
  requiresSignature: boolean;
  retentionPeriod: number; // days
  classification: string;
}

// Version Control and Change Management
export interface ChangeRequest {
  id: string;
  title: string;
  description: string;
  author: string;
  reviewers: string[];
  status: 'draft' | 'review' | 'approved' | 'rejected' | 'merged';
  priority: 'low' | 'medium' | 'high' | 'critical';
  targetBranch: string;
  sourceBranch: string;
  changes: ProgramChange[];
  comments: Comment[];
  approvals: Approval[];
  created: Date;
  updated: Date;
}

export interface ProgramChange {
  id: string;
  type: 'add' | 'modify' | 'delete';
  resource: 'program' | 'tag' | 'rung' | 'block';
  resourceId: string;
  diff: string;
  impact: 'low' | 'medium' | 'high';
  safetyImpact: boolean;
}

export interface Comment {
  id: string;
  author: string;
  content: string;
  timestamp: Date;
  lineNumber?: number;
  resolved: boolean;
  replies: Comment[];
}

export interface Approval {
  id: string;
  reviewer: string;
  status: 'pending' | 'approved' | 'rejected';
  timestamp: Date;
  comments?: string;
  digitalSignature?: DigitalSignature;
}

export interface DigitalSignature {
  algorithm: string;
  hash: string;
  certificate: string;
  timestamp: Date;
  valid: boolean;
}

// Hardware Abstraction Layer
export interface HardwareDriver {
  id: string;
  name: string;
  vendor: 'siemens' | 'rockwell' | 'schneider' | 'beckhoff' | 'omron';
  protocol: 'ethernet_ip' | 'profinet' | 'modbus_tcp' | 'opcua' | 'ads';
  version: string;
  capabilities: DriverCapability[];
  configuration: DriverConfig;
}

export interface DriverCapability {
  feature: 'read_tags' | 'write_tags' | 'program_download' | 'online_edit' | 'diagnostics';
  supported: boolean;
  limitations?: string[];
}

export interface DriverConfig {
  connectionString: string;
  timeout: number;
  retryCount: number;
  authentication?: AuthConfig;
  encryption?: EncryptionConfig;
}

export interface AuthConfig {
  type: 'none' | 'basic' | 'certificate' | 'kerberos';
  username?: string;
  password?: string;
  certificate?: string;
  domain?: string;
}

export interface EncryptionConfig {
  enabled: boolean;
  algorithm?: string;
  keySize?: number;
}

// Testing and Validation Framework
export interface TestSuite {
  id: string;
  name: string;
  description: string;
  programId: string;
  testCases: TestCase[];
  configuration: TestConfiguration;
  results?: TestResults;
  created: Date;
  lastRun?: Date;
}

export interface TestCase {
  id: string;
  name: string;
  description: string;
  inputs: TestInput[];
  expectedOutputs: TestOutput[];
  preconditions: TestCondition[];
  postconditions: TestCondition[];
  timeout: number;
  category: 'unit' | 'integration' | 'safety' | 'performance';
}

export interface TestInput {
  tagName: string;
  value: any;
  timestamp: number; // relative to test start
  duration?: number;
}

export interface TestOutput {
  tagName: string;
  expectedValue: any;
  tolerance?: number;
  checkTime: number; // relative to test start
}

export interface TestCondition {
  expression: string;
  description: string;
}

export interface TestConfiguration {
  simulationSpeed: number;
  maxRunTime: number;
  stopOnFirstFailure: boolean;
  generateReport: boolean;
  reportFormat: 'html' | 'pdf' | 'xml';
}

export interface TestResults {
  id: string;
  testSuiteId: string;
  startTime: Date;
  endTime: Date;
  status: 'passed' | 'failed' | 'error' | 'timeout';
  totalTests: number;
  passedTests: number;
  failedTests: number;
  errorTests: number;
  coverage: TestCoverage;
  caseResults: TestCaseResult[];
  report?: string;
}

export interface TestCaseResult {
  testCaseId: string;
  status: 'passed' | 'failed' | 'error' | 'timeout';
  startTime: Date;
  endTime: Date;
  actualOutputs: TestOutput[];
  errors: string[];
  performance: PerformanceMetrics;
}

export interface TestCoverage {
  rungs: number;
  tags: number;
  branches: number;
  percentage: number;
}

export interface PerformanceMetrics {
  scanTime: number;
  memoryUsage: number;
  cpuUsage: number;
  networkLatency?: number;
}

// Deployment and Lifecycle Management
export interface Deployment {
  id: string;
  projectId: string;
  version: string;
  targetId: string;
  status: 'pending' | 'deploying' | 'success' | 'failed' | 'rollback';
  deployedBy: string;
  deployedAt: Date;
  rollbackVersion?: string;
  configuration: DeploymentConfig;
  logs: DeploymentLog[];
  validation?: DeploymentValidation;
}

export interface DeploymentConfig {
  strategy: 'full' | 'incremental' | 'hot_swap';
  backupBeforeDeploy: boolean;
  validateAfterDeploy: boolean;
  rollbackOnFailure: boolean;
  notificationChannels: string[];
  maintenanceWindow?: MaintenanceWindow;
}

export interface MaintenanceWindow {
  start: Date;
  end: Date;
  description: string;
  approvedBy: string;
}

export interface DeploymentLog {
  timestamp: Date;
  level: 'info' | 'warn' | 'error';
  message: string;
  component: string;
  details?: any;
}

export interface DeploymentValidation {
  preDeployment: ValidationResult[];
  postDeployment: ValidationResult[];
  overallStatus: 'passed' | 'failed' | 'warning';
}

export interface ValidationResult {
  check: string;
  status: 'passed' | 'failed' | 'warning';
  message: string;
  timestamp: Date;
}

// Plugin and Extension System
export interface Plugin {
  id: string;
  name: string;
  version: string;
  author: string;
  description: string;
  category: 'editor' | 'simulation' | 'deployment' | 'analysis' | 'integration';
  entryPoint: string;
  permissions: PluginPermission[];
  dependencies: PluginDependency[];
  configuration: PluginConfig;
  enabled: boolean;
  installed: Date;
  lastUpdated: Date;
}

export interface PluginPermission {
  resource: string;
  actions: string[];
  justification: string;
}

export interface PluginDependency {
  name: string;
  version: string;
  optional: boolean;
}

export interface PluginConfig {
  settings: Record<string, any>;
  ui?: PluginUIConfig;
  hooks: PluginHook[];
}

export interface PluginUIConfig {
  menuItems?: MenuItem[];
  toolbarButtons?: ToolbarButton[];
  panels?: Panel[];
}

export interface MenuItem {
  id: string;
  label: string;
  icon?: string;
  action: string;
  shortcut?: string;
}

export interface ToolbarButton {
  id: string;
  icon: string;
  tooltip: string;
  action: string;
}

export interface Panel {
  id: string;
  title: string;
  position: 'left' | 'right' | 'bottom';
  component: string;
}

export interface PluginHook {
  event: string;
  handler: string;
  priority: number;
}

// License and Subscription Management
export interface License {
  id: string;
  type: 'trial' | 'professional' | 'enterprise' | 'educational';
  features: LicenseFeature[];
  limits: LicenseLimits;
  validFrom: Date;
  validUntil: Date;
  seats: number;
  usedSeats: number;
  organization: string;
  contactEmail: string;
  status: 'active' | 'expired' | 'suspended' | 'revoked';
}

export interface LicenseFeature {
  name: string;
  enabled: boolean;
  limitations?: string[];
}

export interface LicenseLimits {
  maxProjects: number;
  maxPrograms: number;
  maxTags: number;
  maxTargets: number;
  cloudStorage: number; // GB
  supportLevel: 'community' | 'standard' | 'premium' | 'enterprise';
}

// Analytics and Telemetry
export interface UsageMetrics {
  userId: string;
  sessionId: string;
  timestamp: Date;
  event: string;
  properties: Record<string, any>;
  performance?: PerformanceData;
}

export interface PerformanceData {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  errorCount: number;
  crashCount: number;
}

// System Configuration
export interface SystemConfig {
  version: string;
  environment: 'development' | 'staging' | 'production';
  features: FeatureFlag[];
  security: SecurityConfig;
  database: DatabaseConfig;
  logging: LoggingConfig;
  backup: BackupConfig;
}

export interface FeatureFlag {
  name: string;
  enabled: boolean;
  rolloutPercentage: number;
  conditions?: Record<string, any>;
}

export interface SecurityConfig {
  passwordPolicy: PasswordPolicy;
  sessionTimeout: number;
  maxLoginAttempts: number;
  lockoutDuration: number;
  encryptionKey: string;
  certificatePath?: string;
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  maxAge: number; // days
}

export interface DatabaseConfig {
  type: 'sqlite' | 'postgresql' | 'mysql';
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl: boolean;
  poolSize: number;
}

export interface LoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  format: 'json' | 'text';
  outputs: LogOutput[];
  retention: number; // days
}

export interface LogOutput {
  type: 'console' | 'file' | 'database' | 'remote';
  configuration: Record<string, any>;
}

export interface BackupConfig {
  enabled: boolean;
  schedule: string; // cron expression
  retention: number; // days
  destination: string;
  encryption: boolean;
}