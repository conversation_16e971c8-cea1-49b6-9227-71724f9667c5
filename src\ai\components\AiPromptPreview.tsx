import React, { useState } from 'react';
import { Eye, EyeOff, Edit, Copy, RefreshCw } from 'lucide-react';

interface AiPromptPreviewProps {
  prompt: string;
  templateUsed?: string;
  onEdit?: (newPrompt: string) => void;
  onRerun?: (prompt: string) => void;
  className?: string;
}

const AiPromptPreview: React.FC<AiPromptPreviewProps> = ({
  prompt,
  templateUsed,
  onEdit,
  onRerun,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedPrompt, setEditedPrompt] = useState(prompt);

  const handleSave = () => {
    if (onEdit) {
      onEdit(editedPrompt);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedPrompt(prompt);
    setIsEditing(false);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(prompt);
  };

  return (
    <div className={`border-t border-gray-700 ${className}`}>
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="w-full flex items-center justify-between p-3 text-sm text-gray-400 hover:text-white hover:bg-gray-800/50 transition-colors"
      >
        <div className="flex items-center space-x-2">
          {isVisible ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
          <span>AI Prompt</span>
          {templateUsed && (
            <span className="text-xs bg-gray-700 px-2 py-1 rounded">
              {templateUsed}
            </span>
          )}
        </div>
        <span className="text-xs">
          {isVisible ? 'Hide' : 'Show'}
        </span>
      </button>

      {isVisible && (
        <div className="p-3 bg-gray-900/50">
          {isEditing ? (
            <div className="space-y-3">
              <textarea
                value={editedPrompt}
                onChange={(e) => setEditedPrompt(e.target.value)}
                className="w-full h-32 bg-gray-800 border border-gray-600 rounded px-3 py-2 text-white text-sm resize-none focus:border-blue-500 focus:outline-none"
                placeholder="Edit the AI prompt..."
              />
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleSave}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                  >
                    Save & Re-run
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm transition-colors"
                  >
                    Cancel
                  </button>
                </div>
                <div className="text-xs text-gray-400">
                  {editedPrompt.length} characters
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <pre className="bg-gray-800 text-gray-300 p-3 rounded text-sm overflow-x-auto whitespace-pre-wrap">
                {prompt}
              </pre>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {onEdit && (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="flex items-center space-x-1 text-blue-400 hover:text-blue-300 text-sm transition-colors"
                    >
                      <Edit className="w-3 h-3" />
                      <span>Edit</span>
                    </button>
                  )}
                  <button
                    onClick={copyToClipboard}
                    className="flex items-center space-x-1 text-gray-400 hover:text-white text-sm transition-colors"
                  >
                    <Copy className="w-3 h-3" />
                    <span>Copy</span>
                  </button>
                  {onRerun && (
                    <button
                      onClick={() => onRerun(prompt)}
                      className="flex items-center space-x-1 text-green-400 hover:text-green-300 text-sm transition-colors"
                    >
                      <RefreshCw className="w-3 h-3" />
                      <span>Re-run</span>
                    </button>
                  )}
                </div>
                <div className="text-xs text-gray-400">
                  {prompt.length} characters
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AiPromptPreview;