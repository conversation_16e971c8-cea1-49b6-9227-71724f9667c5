-- LUREON AI Backend Database Initialization

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255),
    prompt_hash VARCHAR(64) NOT NULL,
    response_hash VARCHAR(64) NOT NULL,
    model_used VARCHAR(50) NOT NULL,
    confidence_score FLOAT,
    ip_address INET,
    request_type VARCHAR(50),
    safety_validated BOOLEAN DEFAULT false,
    compliance_issues JSONB DEFAULT '[]'::jsonb,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create feedback table
CREATE TABLE IF NOT EXISTS feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    suggestion_id VARCHAR(255) NOT NULL,
    rating VARCHAR(20) NOT NULL,
    category VARCHAR(50),
    comment TEXT,
    context JSONB DEFAULT '{}'::jsonb,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create usage_metrics table
CREATE TABLE IF NOT EXISTS usage_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    organization_id VARCHAR(255),
    model_used VARCHAR(50) NOT NULL,
    request_type VARCHAR(50),
    tokens_used INTEGER,
    cost_usd DECIMAL(10,6),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create prompt_cache table
CREATE TABLE IF NOT EXISTS prompt_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    prompt_hash VARCHAR(64) UNIQUE NOT NULL,
    response_content TEXT NOT NULL,
    confidence_score FLOAT,
    hit_count INTEGER DEFAULT 1,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create digital_signatures table
CREATE TABLE IF NOT EXISTS digital_signatures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_hash VARCHAR(64) NOT NULL,
    signature TEXT NOT NULL,
    algorithm VARCHAR(50) NOT NULL,
    key_id VARCHAR(255),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_timestamp ON audit_logs(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_model_confidence ON audit_logs(model_used, confidence_score);
CREATE INDEX IF NOT EXISTS idx_feedback_rating_category ON feedback(rating, category, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_usage_metrics_org_period ON usage_metrics(organization_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_prompt_cache_hash_accessed ON prompt_cache(prompt_hash, last_accessed DESC);
CREATE INDEX IF NOT EXISTS idx_digital_signatures_hash ON digital_signatures(content_hash);

-- Set up data retention policies
CREATE OR REPLACE FUNCTION cleanup_old_data() RETURNS void AS $$
BEGIN
    -- Clean up cache entries older than 30 days
    DELETE FROM prompt_cache WHERE expires_at < NOW() - INTERVAL '30 days';
    
    -- Clean up audit logs older than 7 years (regulatory requirement)
    DELETE FROM audit_logs WHERE timestamp < NOW() - INTERVAL '7 years';
    
    -- Clean up usage metrics older than 2 years
    DELETE FROM usage_metrics WHERE timestamp < NOW() - INTERVAL '2 years';
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup to run daily
SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data();');