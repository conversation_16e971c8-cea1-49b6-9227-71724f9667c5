import express from 'express';
import {
  getContextItems,
  getContextItem,
  createContextItem,
  updateContextItem,
  deleteContextItem,
  collectProgramContext,
  collectTagContext,
  collectRungContext,
  collectSafetyContext,
  collectStandardContext,
  enhancePrompt,
  calculateConfidence,
  getRelatedContextItems,
  createContextRelationship
} from './mcpController';
import { authenticate } from '../backend/middleware/auth';

const router = express.Router();

// Context Items CRUD
router.get('/context', authenticate, getContextItems);
router.get('/context/:id', authenticate, getContextItem);
router.post('/context', authenticate, createContextItem);
router.put('/context/:id', authenticate, updateContextItem);
router.delete('/context/:id', authenticate, deleteContextItem);

// Context Collection
router.post('/collect/program/:programId', authenticate, collectProgramContext);
router.post('/collect/tag/:tagId', authenticate, collectTagContext);
router.post('/collect/rung/:rungId', authenticate, collectRungContext);
router.post('/collect/safety/:programId', authenticate, collectSafetyContext);
router.post('/collect/standard', authenticate, collectStandardContext);

// Prompt Enhancement
router.post('/enhance', authenticate, enhancePrompt);

// Confidence Scoring
router.post('/confidence', authenticate, calculateConfidence);

// Context Relationships
router.get('/context/:id/related', authenticate, getRelatedContextItems);
router.post('/relationship', authenticate, createContextRelationship);

export default router;