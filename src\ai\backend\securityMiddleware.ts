import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { body, validationResult } from 'express-validator';

export const securityMiddleware = [
    // Security headers
    helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                scriptSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'"],
                imgSrc: ["'self'", "data:", "https:"],
            },
        },
        hsts: {
            maxAge: 31536000,
            includeSubDomains: true,
            preload: true
        }
    }),

    // Advanced rate limiting
    rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: (req) => {
            // Dynamic rate limiting based on user tier
            const userPlan = req.user?.plan;
            switch (userPlan) {
                case 'enterprise': return 1000;
                case 'pro': return 500;
                case 'basic': return 100;
                default: return 50;
            }
        },
        message: {
            error: 'Rate limit exceeded',
            retryAfter: '15 minutes'
        },
        standardHeaders: true,
        legacyHeaders: false,
    })
];

// Input validation schemas
export const aiRequestValidation = [
    body('type').isIn(['explain', 'refactor', 'generate', 'suggest', 'debug']),
    body('prompt').isString().isLength({ min: 1, max: 10000 }),
    body('context').isObject(),
    body('model').optional().isString().isIn(['gpt-3.5-turbo', 'gpt-4', 'claude-3']),

    (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Invalid request parameters',
                details: errors.array()
            });
        }
        next();
    }
];