// Hybrid Online/Offline Sync Engine for PLC IDE

import { P<PERSON><PERSON>roject, PLCProgram, PLCTag } from '../types/plc';
import CryptoJS from 'crypto-js';

export interface SyncOperation {
  id: string;
  type: 'create' | 'update' | 'delete';
  resource: 'project' | 'program' | 'tag' | 'rung';
  resourceId: string;
  data: any;
  timestamp: Date;
  userId: string;
  hash: string;
  dependencies: string[];
}

export interface ConflictResolution {
  operation: SyncOperation;
  conflictType: 'concurrent_edit' | 'delete_modified' | 'version_mismatch';
  localVersion: any;
  remoteVersion: any;
  resolution: 'local' | 'remote' | 'merge' | 'manual';
  mergedData?: any;
}

export class SyncEngine {
  private pendingOperations: SyncOperation[] = [];
  private operationHistory: SyncOperation[] = [];
  private isOnline: boolean = navigator.onLine;
  private syncInProgress: boolean = false;
  private conflictResolver: ConflictResolver;

  constructor() {
    this.conflictResolver = new ConflictResolver();
    this.setupNetworkListeners();
    this.startPeriodicSync();
  }

  private setupNetworkListeners() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.triggerSync();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  private startPeriodicSync() {
    setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.triggerSync();
      }
    }, 30000); // Sync every 30 seconds when online
  }

  async queueOperation(
    type: SyncOperation['type'],
    resource: SyncOperation['resource'],
    resourceId: string,
    data: any,
    userId: string
  ): Promise<void> {
    const operation: SyncOperation = {
      id: `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      resource,
      resourceId,
      data,
      timestamp: new Date(),
      userId,
      hash: this.generateHash(data),
      dependencies: this.findDependencies(resource, resourceId, data)
    };

    this.pendingOperations.push(operation);
    this.operationHistory.push(operation);

    // Store in IndexedDB for offline persistence
    await this.persistOperation(operation);

    // Try immediate sync if online
    if (this.isOnline) {
      this.triggerSync();
    }
  }

  private generateHash(data: any): string {
    return CryptoJS.SHA256(JSON.stringify(data)).toString();
  }

  private findDependencies(resource: string, resourceId: string, data: any): string[] {
    const dependencies: string[] = [];
    
    // Find tag dependencies in ladder logic
    if (resource === 'program' && data.content) {
      const content = Array.isArray(data.content) ? data.content : [];
      content.forEach((rung: any) => {
        if (rung.elements) {
          rung.elements.forEach((element: any) => {
            if (element.tag) {
              dependencies.push(`tag:${element.tag}`);
            }
          });
        }
      });
    }

    return dependencies;
  }

  async triggerSync(): Promise<void> {
    if (this.syncInProgress || !this.isOnline) return;

    this.syncInProgress = true;
    try {
      await this.performSync();
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  private async performSync(): Promise<void> {
    if (this.pendingOperations.length === 0) return;

    // Get remote operations since last sync
    const remoteOperations = await this.fetchRemoteOperations();
    
    // Detect conflicts
    const conflicts = this.detectConflicts(this.pendingOperations, remoteOperations);
    
    if (conflicts.length > 0) {
      // Resolve conflicts
      const resolutions = await this.resolveConflicts(conflicts);
      await this.applyConflictResolutions(resolutions);
    }

    // Apply remote operations
    await this.applyRemoteOperations(remoteOperations);

    // Send local operations to server
    await this.sendLocalOperations(this.pendingOperations);

    // Clear pending operations
    this.pendingOperations = [];
  }

  private detectConflicts(
    localOps: SyncOperation[],
    remoteOps: SyncOperation[]
  ): ConflictResolution[] {
    const conflicts: ConflictResolution[] = [];

    localOps.forEach(localOp => {
      remoteOps.forEach(remoteOp => {
        if (localOp.resourceId === remoteOp.resourceId && 
            localOp.userId !== remoteOp.userId) {
          
          let conflictType: ConflictResolution['conflictType'] = 'concurrent_edit';
          
          if (localOp.type === 'delete' && remoteOp.type === 'update') {
            conflictType = 'delete_modified';
          } else if (localOp.type === 'update' && remoteOp.type === 'delete') {
            conflictType = 'delete_modified';
          } else if (localOp.hash !== remoteOp.hash) {
            conflictType = 'version_mismatch';
          }

          conflicts.push({
            operation: localOp,
            conflictType,
            localVersion: localOp.data,
            remoteVersion: remoteOp.data,
            resolution: 'manual' // Will be determined by conflict resolver
          });
        }
      });
    });

    return conflicts;
  }

  private async resolveConflicts(conflicts: ConflictResolution[]): Promise<ConflictResolution[]> {
    const resolutions: ConflictResolution[] = [];

    for (const conflict of conflicts) {
      const resolution = await this.conflictResolver.resolve(conflict);
      resolutions.push(resolution);
    }

    return resolutions;
  }

  private async applyConflictResolutions(resolutions: ConflictResolution[]): Promise<void> {
    for (const resolution of resolutions) {
      switch (resolution.resolution) {
        case 'local':
          // Keep local version, mark remote as resolved
          break;
        case 'remote':
          // Apply remote version, discard local
          await this.applyRemoteData(resolution.operation.resourceId, resolution.remoteVersion);
          break;
        case 'merge':
          // Apply merged data
          if (resolution.mergedData) {
            await this.applyRemoteData(resolution.operation.resourceId, resolution.mergedData);
          }
          break;
        case 'manual':
          // Present to user for manual resolution
          await this.presentConflictToUser(resolution);
          break;
      }
    }
  }

  private async presentConflictToUser(conflict: ConflictResolution): Promise<void> {
    // This would trigger a UI component to show the conflict
    const event = new CustomEvent('sync-conflict', { detail: conflict });
    window.dispatchEvent(event);
  }

  private async persistOperation(operation: SyncOperation): Promise<void> {
    // Store in IndexedDB for offline persistence
    const db = await this.openIndexedDB();
    const transaction = db.transaction(['operations'], 'readwrite');
    const store = transaction.objectStore('operations');
    await store.add(operation);
  }

  private async openIndexedDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PLCWorkspace', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        if (!db.objectStoreNames.contains('operations')) {
          const store = db.createObjectStore('operations', { keyPath: 'id' });
          store.createIndex('timestamp', 'timestamp');
          store.createIndex('resourceId', 'resourceId');
        }
        
        if (!db.objectStoreNames.contains('projects')) {
          db.createObjectStore('projects', { keyPath: 'id' });
        }
      };
    });
  }

  private async fetchRemoteOperations(): Promise<SyncOperation[]> {
    // In production, this would fetch from server
    return [];
  }

  private async sendLocalOperations(operations: SyncOperation[]): Promise<void> {
    // In production, this would send to server
    console.log('Sending operations to server:', operations);
  }

  private async applyRemoteOperations(operations: SyncOperation[]): Promise<void> {
    for (const operation of operations) {
      await this.applyRemoteData(operation.resourceId, operation.data);
    }
  }

  private async applyRemoteData(resourceId: string, data: any): Promise<void> {
    // Apply remote changes to local state
    const event = new CustomEvent('remote-update', { 
      detail: { resourceId, data } 
    });
    window.dispatchEvent(event);
  }

  // Public API for getting sync status
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      pendingOperations: this.pendingOperations.length,
      syncInProgress: this.syncInProgress,
      lastSync: this.operationHistory.length > 0 
        ? this.operationHistory[this.operationHistory.length - 1].timestamp 
        : null
    };
  }
}

class ConflictResolver {
  async resolve(conflict: ConflictResolution): Promise<ConflictResolution> {
    // Automatic resolution strategies
    switch (conflict.conflictType) {
      case 'concurrent_edit':
        return this.resolveConcurrentEdit(conflict);
      case 'delete_modified':
        return this.resolveDeleteModified(conflict);
      case 'version_mismatch':
        return this.resolveVersionMismatch(conflict);
      default:
        conflict.resolution = 'manual';
        return conflict;
    }
  }

  private async resolveConcurrentEdit(conflict: ConflictResolution): Promise<ConflictResolution> {
    // Try to merge ladder logic changes
    if (conflict.operation.resource === 'program') {
      const merged = this.mergeLadderLogic(conflict.localVersion, conflict.remoteVersion);
      if (merged) {
        conflict.resolution = 'merge';
        conflict.mergedData = merged;
        return conflict;
      }
    }

    // Default to manual resolution for complex conflicts
    conflict.resolution = 'manual';
    return conflict;
  }

  private async resolveDeleteModified(conflict: ConflictResolution): Promise<ConflictResolution> {
    // Generally prefer keeping the modification over deletion
    conflict.resolution = 'remote';
    return conflict;
  }

  private async resolveVersionMismatch(conflict: ConflictResolution): Promise<ConflictResolution> {
    // Use timestamp to determine which version is newer
    const localTime = new Date(conflict.localVersion.lastModified || 0);
    const remoteTime = new Date(conflict.remoteVersion.lastModified || 0);
    
    conflict.resolution = localTime > remoteTime ? 'local' : 'remote';
    return conflict;
  }

  private mergeLadderLogic(local: any, remote: any): any | null {
    // Intelligent merging of ladder logic
    if (!local.content || !remote.content) return null;
    
    const localRungs = Array.isArray(local.content) ? local.content : [];
    const remoteRungs = Array.isArray(remote.content) ? remote.content : [];
    
    // Simple merge strategy: combine rungs and remove duplicates
    const mergedRungs = [...localRungs];
    
    remoteRungs.forEach(remoteRung => {
      const existingIndex = mergedRungs.findIndex(r => r.id === remoteRung.id);
      if (existingIndex >= 0) {
        // Merge rung elements
        mergedRungs[existingIndex] = this.mergeRung(mergedRungs[existingIndex], remoteRung);
      } else {
        mergedRungs.push(remoteRung);
      }
    });

    return {
      ...local,
      content: mergedRungs,
      lastModified: new Date().toISOString()
    };
  }

  private mergeRung(localRung: any, remoteRung: any): any {
    // Merge individual rung changes
    const mergedElements = [...(localRung.elements || [])];
    
    (remoteRung.elements || []).forEach((remoteElement: any) => {
      const existingIndex = mergedElements.findIndex(e => e.id === remoteElement.id);
      if (existingIndex >= 0) {
        // Use the element with the latest timestamp
        const localTime = new Date(mergedElements[existingIndex].lastModified || 0);
        const remoteTime = new Date(remoteElement.lastModified || 0);
        if (remoteTime > localTime) {
          mergedElements[existingIndex] = remoteElement;
        }
      } else {
        mergedElements.push(remoteElement);
      }
    });

    return {
      ...localRung,
      elements: mergedElements,
      lastModified: new Date().toISOString()
    };
  }
}

export const syncEngine = new SyncEngine();