import React, { useState, useEffect } from 'react';
import { usePLCStore } from '../../store/plcStore';
import { HMIElement, HMIScreen } from '../../types/plc';
import { 
  Monitor, 
  Power, 
  Gauge, 
  BarChart3, 
  Alert<PERSON>riangle,
  CheckCircle2,
  Square,
  Circle,
  Play,
  Pause,
  Settings,
  Maximize2,
  Minimize2,
  RotateCcw,
  Plus,
  Edit,
  Trash2,
  Copy,
  Save,
  Download,
  Upload,
  ChevronLeft,
  ChevronRight,
  List,
  Grid,
  Move,
  Eye,
  EyeOff
} from 'lucide-react';

const HMIPreview: React.FC = () => {
  const { currentProject, updateTagValue, simulationMode } = usePLCStore();
  const [selectedScreen, setSelectedScreen] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showGrid, setShowGrid] = useState(false);
  const [animationSpeed, setAnimationSpeed] = useState(1);
  const [showScreenList, setShowScreenList] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [showElementProperties, setShowElementProperties] = useState(false);
  const [newScreenName, setNewScreenName] = useState('');
  const [showAddScreenModal, setShowAddScreenModal] = useState(false);

  // Enhanced default HMI screens with more elements
  const defaultScreens: HMIScreen[] = [
    {
      id: 'main-screen',
      name: 'Main Control Panel',
      size: { width: 1024, height: 768 },
      elements: [
        // Header
        {
          id: 'header-text',
          type: 'text' as const,
          position: { x: 20, y: 20 },
          size: { width: 400, height: 40 },
          properties: {
            text: 'PUMP STATION CONTROL',
            fontSize: 24,
            color: 'white',
            fontWeight: 'bold'
          }
        },
        {
          id: 'status-text',
          type: 'text' as const,
          position: { x: 20, y: 70 },
          size: { width: 200, height: 30 },
          properties: {
            text: 'System Status',
            fontSize: 16,
            color: 'white'
          }
        },
        
        // Control buttons
        {
          id: 'start-btn',
          type: 'button' as const,
          position: { x: 50, y: 120 },
          size: { width: 120, height: 60 },
          tag: 'Start_Button',
          properties: {
            text: 'START',
            color: 'green',
            pressAction: 'momentary'
          }
        },
        {
          id: 'stop-btn',
          type: 'button' as const,
          position: { x: 200, y: 120 },
          size: { width: 120, height: 60 },
          tag: 'Emergency_Stop',
          properties: {
            text: 'E-STOP',
            color: 'red',
            pressAction: 'toggle'
          }
        },
        
        // Indicators
        {
          id: 'pump-indicator',
          type: 'indicator' as const,
          position: { x: 400, y: 120 },
          size: { width: 80, height: 80 },
          tag: 'Pump_Run',
          properties: {
            shape: 'circle',
            onColor: 'green',
            offColor: 'gray',
            label: 'PUMP'
          }
        },
        {
          id: 'motor-indicator',
          type: 'indicator' as const,
          position: { x: 500, y: 120 },
          size: { width: 80, height: 80 },
          tag: 'Motor_Output',
          properties: {
            shape: 'square',
            onColor: 'blue',
            offColor: 'gray',
            label: 'MOTOR'
          }
        },
        
        // Gauges
        {
          id: 'pressure-gauge',
          type: 'gauge' as const,
          position: { x: 50, y: 250 },
          size: { width: 200, height: 200 },
          tag: 'Pressure_Value',
          properties: {
            minValue: 0,
            maxValue: 100,
            units: 'PSI',
            label: 'Pressure',
            color: 'blue'
          }
        },
        {
          id: 'temperature-gauge',
          type: 'gauge' as const,
          position: { x: 300, y: 250 },
          size: { width: 200, height: 200 },
          tag: 'Temperature_Value',
          properties: {
            minValue: 0,
            maxValue: 150,
            units: '°C',
            label: 'Temperature',
            color: 'red'
          }
        },
        
        // Trend chart
        {
          id: 'flow-trend',
          type: 'trend' as const,
          position: { x: 550, y: 250 },
          size: { width: 400, height: 200 },
          tag: 'Flow_Rate',
          properties: {
            title: 'Flow Rate',
            yAxisMin: 0,
            yAxisMax: 100,
            units: 'L/min',
            timeWindow: 60, // seconds
            color: 'purple'
          }
        },
        
        // Tank visualization
        {
          id: 'tank-level',
          type: 'tank' as const,
          position: { x: 50, y: 500 },
          size: { width: 150, height: 200 },
          tag: 'Tank_Level',
          properties: {
            minValue: 0,
            maxValue: 100,
            units: '%',
            label: 'Tank Level',
            color: 'lightblue'
          }
        },
        
        // Alarm panel
        {
          id: 'alarm-panel',
          type: 'alarmPanel' as const,
          position: { x: 250, y: 500 },
          size: { width: 700, height: 200 },
          properties: {
            title: 'Active Alarms',
            maxAlarms: 5
          }
        }
      ]
    },
    // Second screen for multi-screen demo
    {
      id: 'diagnostics-screen',
      name: 'System Diagnostics',
      size: { width: 1024, height: 768 },
      elements: [
        {
          id: 'diag-header',
          type: 'text' as const,
          position: { x: 20, y: 20 },
          size: { width: 400, height: 40 },
          properties: {
            text: 'SYSTEM DIAGNOSTICS',
            fontSize: 24,
            color: 'white',
            fontWeight: 'bold'
          }
        },
        {
          id: 'network-status',
          type: 'indicator' as const,
          position: { x: 50, y: 100 },
          size: { width: 80, height: 80 },
          tag: 'Network_OK',
          properties: {
            shape: 'circle',
            onColor: 'green',
            offColor: 'red',
            label: 'NETWORK'
          }
        },
        {
          id: 'cpu-gauge',
          type: 'gauge' as const,
          position: { x: 200, y: 100 },
          size: { width: 200, height: 200 },
          tag: 'CPU_Load',
          properties: {
            minValue: 0,
            maxValue: 100,
            units: '%',
            label: 'CPU Load',
            color: 'orange'
          }
        },
        {
          id: 'memory-gauge',
          type: 'gauge' as const,
          position: { x: 450, y: 100 },
          size: { width: 200, height: 200 },
          tag: 'Memory_Usage',
          properties: {
            minValue: 0,
            maxValue: 100,
            units: '%',
            label: 'Memory Usage',
            color: 'purple'
          }
        },
        {
          id: 'system-log',
          type: 'textDisplay' as const,
          position: { x: 50, y: 350 },
          size: { width: 900, height: 350 },
          tag: 'System_Log',
          properties: {
            title: 'System Log',
            fontSize: 14,
            rows: 10,
            scrollable: true
          }
        }
      ]
    },
    // Third screen for production overview
    {
      id: 'production-screen',
      name: 'Production Overview',
      size: { width: 1024, height: 768 },
      elements: [
        {
          id: 'prod-header',
          type: 'text' as const,
          position: { x: 20, y: 20 },
          size: { width: 400, height: 40 },
          properties: {
            text: 'PRODUCTION OVERVIEW',
            fontSize: 24,
            color: 'white',
            fontWeight: 'bold'
          }
        },
        {
          id: 'production-rate',
          type: 'gauge' as const,
          position: { x: 50, y: 100 },
          size: { width: 200, height: 200 },
          tag: 'Production_Rate',
          properties: {
            minValue: 0,
            maxValue: 1000,
            units: 'units/hr',
            label: 'Production Rate',
            color: 'green'
          }
        },
        {
          id: 'efficiency-gauge',
          type: 'gauge' as const,
          position: { x: 300, y: 100 },
          size: { width: 200, height: 200 },
          tag: 'Efficiency',
          properties: {
            minValue: 0,
            maxValue: 100,
            units: '%',
            label: 'Efficiency',
            color: 'blue'
          }
        },
        {
          id: 'quality-gauge',
          type: 'gauge' as const,
          position: { x: 550, y: 100 },
          size: { width: 200, height: 200 },
          tag: 'Quality_Rate',
          properties: {
            minValue: 0,
            maxValue: 100,
            units: '%',
            label: 'Quality',
            color: 'purple'
          }
        },
        {
          id: 'production-trend',
          type: 'trend' as const,
          position: { x: 50, y: 350 },
          size: { width: 900, height: 300 },
          tag: 'Production_History',
          properties: {
            title: 'Production History',
            yAxisMin: 0,
            yAxisMax: 1000,
            units: 'units/hr',
            timeWindow: 3600, // 1 hour
            color: 'green'
          }
        }
      ]
    }
  ];

  const [screens, setScreens] = useState<HMIScreen[]>(currentProject?.hmiScreens || defaultScreens);
  const currentScreen = screens[selectedScreen] || defaultScreens[0];

  // Initialize screens from project
  useEffect(() => {
    if (currentProject?.hmiScreens) {
      setScreens(currentProject.hmiScreens);
    }
  }, [currentProject]);

  // Simulate tag values for demo
  useEffect(() => {
    if (simulationMode) {
      const interval = setInterval(() => {
        // Update simulated tag values
        const tags = [
          { name: 'Pressure_Value', value: Math.random() * 100 },
          { name: 'Temperature_Value', value: 20 + Math.random() * 80 },
          { name: 'Flow_Rate', value: Math.random() * 100 },
          { name: 'Tank_Level', value: 20 + Math.random() * 60 },
          { name: 'CPU_Load', value: 10 + Math.random() * 30 },
          { name: 'Memory_Usage', value: 30 + Math.random() * 40 },
          { name: 'Production_Rate', value: 400 + Math.random() * 300 },
          { name: 'Efficiency', value: 75 + Math.random() * 20 },
          { name: 'Quality_Rate', value: 90 + Math.random() * 9 }
        ];
        
        tags.forEach(tag => {
          const existingTag = currentProject?.globalTags.find(t => t.name === tag.name);
          if (existingTag) {
            updateTagValue(existingTag.id, tag.value);
          }
        });
      }, 1000 / animationSpeed);
      
      return () => clearInterval(interval);
    }
  }, [simulationMode, currentProject, updateTagValue, animationSpeed]);

  const handleElementClick = (element: HMIElement) => {
    if (!simulationMode || !element.tag) return;

    const tag = currentProject?.globalTags.find(t => t.name === element.tag);
    if (!tag) return;

    switch (element.type) {
      case 'button':
        if (element.properties?.pressAction === 'momentary') {
          // Momentary button - set true then false after delay
          updateTagValue(tag.id, true);
          setTimeout(() => updateTagValue(tag.id, false), 200);
        } else {
          // Toggle button
          updateTagValue(tag.id, !tag.value);
        }
        break;
    }
  };

  const handleNextScreen = () => {
    setSelectedScreen((prev) => (prev + 1) % screens.length);
  };

  const handlePrevScreen = () => {
    setSelectedScreen((prev) => (prev - 1 + screens.length) % screens.length);
  };

  const handleAddScreen = () => {
    setShowAddScreenModal(true);
  };

  const handleCreateScreen = () => {
    if (!newScreenName.trim()) {
      alert('Please enter a screen name');
      return;
    }
    
    const newScreen: HMIScreen = {
      id: `screen-${Date.now()}`,
      name: newScreenName,
      size: { width: 1024, height: 768 },
      elements: []
    };
    
    setScreens([...screens, newScreen]);
    setSelectedScreen(screens.length);
    setShowAddScreenModal(false);
    setNewScreenName('');
  };

  const handleDeleteScreen = () => {
    if (screens.length <= 1) {
      alert('Cannot delete the only screen');
      return;
    }
    
    if (confirm(`Delete screen "${currentScreen.name}"?`)) {
      const updatedScreens = screens.filter((_, index) => index !== selectedScreen);
      setScreens(updatedScreens);
      setSelectedScreen(Math.min(selectedScreen, updatedScreens.length - 1));
    }
  };

  const handleDuplicateScreen = () => {
    const duplicatedScreen: HMIScreen = {
      ...currentScreen,
      id: `screen-${Date.now()}`,
      name: `${currentScreen.name} (Copy)`,
      elements: [...currentScreen.elements]
    };
    
    setScreens([...screens, duplicatedScreen]);
    setSelectedScreen(screens.length);
  };

  const handleElementSelect = (elementId: string) => {
    if (!editMode) return;
    
    setSelectedElement(elementId);
    setShowElementProperties(true);
  };

  const handleRunSequence = () => {
    if (!simulationMode) {
      alert('Please enable simulation mode first');
      return;
    }
    
    alert('Running HMI sequence...');
    // In a real implementation, this would run a predefined sequence of HMI interactions
  };

  const renderHMIElement = (element: HMIElement) => {
    const tag = currentProject?.globalTags.find(t => t.name === element.tag);
    const tagValue = tag?.value;

    const elementStyle = {
      position: 'absolute' as const,
      left: element.position.x,
      top: element.position.y,
      width: element.size.width,
      height: element.size.height,
      border: selectedElement === element.id && editMode ? '2px dashed #3b82f6' : 'none',
      cursor: editMode ? 'move' : 'default'
    };

    switch (element.type) {
      case 'button':
        const isPressed = tagValue && element.properties?.pressAction === 'momentary';
        return (
          <button
            key={element.id}
            style={elementStyle}
            onClick={() => editMode ? handleElementSelect(element.id) : handleElementClick(element)}
            disabled={!simulationMode && !editMode}
            className={`rounded-lg font-semibold text-white transition-all transform ${
              isPressed ? 'scale-95' : 'hover:scale-105'
            } ${
              element.properties?.color === 'red' 
                ? 'bg-red-600 hover:bg-red-700 shadow-red-500/50' 
                : 'bg-green-600 hover:bg-green-700 shadow-green-500/50'
            } shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center`}
          >
            {element.properties?.text || 'Button'}
          </button>
        );

      case 'indicator':
        const isOn = Boolean(tagValue);
        const shape = element.properties?.shape || 'circle';
        const onColor = element.properties?.onColor || 'green';
        const offColor = element.properties?.offColor || 'gray';
        const label = element.properties?.label || '';
        
        return (
          <div
            key={element.id}
            style={elementStyle}
            className="flex flex-col items-center justify-center"
            onClick={() => editMode && handleElementSelect(element.id)}
          >
            <div className={`flex items-center justify-center transition-all ${
              shape === 'circle' ? 'rounded-full' : 'rounded-lg'
            } ${
              isOn 
                ? `bg-${onColor}-500 shadow-lg shadow-${onColor}-500/50` 
                : `bg-${offColor}-600`
            }`}
            style={{ width: '80%', height: '80%' }}
            >
              {shape === 'circle' ? (
                <Circle className={`w-8 h-8 ${isOn ? 'text-white' : 'text-gray-400'}`} />
              ) : (
                <Square className={`w-8 h-8 ${isOn ? 'text-white' : 'text-gray-400'}`} />
              )}
            </div>
            {label && (
              <div className="text-white text-sm mt-2">{label}</div>
            )}
          </div>
        );

      case 'text':
        return (
          <div
            key={element.id}
            style={{
              ...elementStyle,
              fontSize: element.properties?.fontSize || 14,
              color: element.properties?.color || 'white',
              fontWeight: element.properties?.fontWeight || 'normal'
            }}
            className="flex items-center"
            onClick={() => editMode && handleElementSelect(element.id)}
          >
            {element.properties?.text || 'Text'}
          </div>
        );

      case 'gauge':
        const gaugeValue = typeof tagValue === 'number' ? tagValue : 0;
        const maxValue = element.properties?.maxValue || 100;
        const minValue = element.properties?.minValue || 0;
        const percentage = Math.min(((gaugeValue - minValue) / (maxValue - minValue)) * 100, 100);
        const units = element.properties?.units || '';
        const gaugeLabel = element.properties?.label || '';
        const gaugeColor = element.properties?.color || 'blue';
        
        return (
          <div 
            key={element.id} 
            style={elementStyle} 
            className="bg-gray-800/70 rounded-lg p-4 flex flex-col items-center"
            onClick={() => editMode && handleElementSelect(element.id)}
          >
            <div className="text-white text-sm mb-2">{gaugeLabel}</div>
            <div className="w-full flex items-center justify-center mb-2">
              <div className="relative w-32 h-32">
                <svg viewBox="0 0 100 100" className="w-full h-full">
                  {/* Background arc */}
                  <path 
                    d="M10,50 A40,40 0 1,1 90,50" 
                    fill="none" 
                    stroke="#374151" 
                    strokeWidth="10" 
                    strokeLinecap="round"
                  />
                  {/* Value arc */}
                  <path 
                    d={`M10,50 A40,40 0 ${percentage > 50 ? 1 : 0},1 ${
                      10 + 80 * (percentage / 100)
                    },${
                      50 - 40 * Math.sin(Math.PI * (percentage / 100))
                    }`} 
                    fill="none" 
                    stroke={`var(--color-${gaugeColor}-500)`}
                    strokeWidth="10" 
                    strokeLinecap="round"
                  />
                  {/* Center text */}
                  <text 
                    x="50" 
                    y="60" 
                    textAnchor="middle" 
                    fontSize="15" 
                    fill="white" 
                    fontWeight="bold"
                  >
                    {gaugeValue.toFixed(1)}
                  </text>
                </svg>
              </div>
            </div>
            <div className="text-white text-lg font-mono">
              {gaugeValue.toFixed(1)} {units}
            </div>
          </div>
        );

      case 'trend':
        const trendValue = typeof tagValue === 'number' ? tagValue : 0;
        const trendTitle = element.properties?.title || '';
        const trendUnits = element.properties?.units || '';
        const trendColor = element.properties?.color || 'blue';
        
        return (
          <div 
            key={element.id} 
            style={elementStyle} 
            className="bg-gray-800/70 rounded-lg p-4"
            onClick={() => editMode && handleElementSelect(element.id)}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="text-white text-sm font-medium">{trendTitle}</div>
              <div className="text-white text-sm">{trendValue.toFixed(1)} {trendUnits}</div>
            </div>
            <div className="relative w-full h-32 bg-gray-900 rounded border border-gray-700">
              {/* Simulated trend line */}
              <svg className="w-full h-full" viewBox="0 0 100 50">
                <polyline
                  points="0,50 10,45 20,48 30,40 40,42 50,35 60,30 70,32 80,25 90,20 100,15"
                  fill="none"
                  stroke={`var(--color-${trendColor}-500)`}
                  strokeWidth="2"
                />
              </svg>
              
              {/* Y-axis labels */}
              <div className="absolute left-2 top-0 text-xs text-gray-400">
                {element.properties?.yAxisMax}
              </div>
              <div className="absolute left-2 bottom-0 text-xs text-gray-400">
                {element.properties?.yAxisMin}
              </div>
            </div>
          </div>
        );

      case 'tank':
        const tankValue = typeof tagValue === 'number' ? tagValue : 0;
        const tankMaxValue = element.properties?.maxValue || 100;
        const tankMinValue = element.properties?.minValue || 0;
        const tankPercentage = Math.min(((tankValue - tankMinValue) / (tankMaxValue - tankMinValue)) * 100, 100);
        const tankUnits = element.properties?.units || '';
        const tankLabel = element.properties?.label || '';
        const tankColor = element.properties?.color || 'blue';
        
        return (
          <div 
            key={element.id} 
            style={elementStyle} 
            className="bg-gray-800/70 rounded-lg p-4 flex flex-col items-center"
            onClick={() => editMode && handleElementSelect(element.id)}
          >
            <div className="text-white text-sm mb-2">{tankLabel}</div>
            <div className="relative w-24 h-32 bg-gray-900 rounded-lg border border-gray-700 overflow-hidden">
              <div 
                className={`absolute bottom-0 left-0 right-0 bg-${tankColor}-500/70 transition-all duration-1000`}
                style={{ height: `${tankPercentage}%` }}
              ></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-white font-bold text-lg">{tankValue.toFixed(0)}%</span>
              </div>
            </div>
            <div className="text-white text-sm mt-2">
              {tankValue.toFixed(1)} {tankUnits}
            </div>
          </div>
        );

      case 'alarmPanel':
        const alarms = [
          { id: 'alarm-1', active: true, message: 'High Pressure Warning', timestamp: new Date(Date.now() - 1000 * 60 * 5), severity: 'warning' },
          { id: 'alarm-2', active: false, message: 'Motor Overload Fault', timestamp: new Date(Date.now() - 1000 * 60 * 30), severity: 'critical' },
          { id: 'alarm-3', active: true, message: 'Low Tank Level', timestamp: new Date(Date.now() - 1000 * 60 * 2), severity: 'warning' }
        ];
        
        return (
          <div 
            key={element.id} 
            style={elementStyle} 
            className="bg-gray-800/70 rounded-lg p-4"
            onClick={() => editMode && handleElementSelect(element.id)}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="text-white font-medium">{element.properties?.title || 'Alarms'}</div>
              <div className="flex items-center space-x-2">
                <span className="text-xs text-red-400">{alarms.filter(a => a.active).length} Active</span>
                <button className="text-gray-400 hover:text-white">
                  <RotateCcw className="w-3 h-3" />
                </button>
              </div>
            </div>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {alarms.slice(0, element.properties?.maxAlarms || 5).map(alarm => (
                <div 
                  key={alarm.id} 
                  className={`p-2 rounded text-xs ${
                    alarm.active 
                      ? alarm.severity === 'critical' 
                        ? 'bg-red-900/30 border border-red-700/30' 
                        : 'bg-yellow-900/30 border border-yellow-700/30'
                      : 'bg-gray-700/30 border border-gray-600/30'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {alarm.severity === 'critical' ? (
                        <AlertTriangle className={`w-3 h-3 ${alarm.active ? 'text-red-400' : 'text-gray-400'}`} />
                      ) : (
                        <AlertTriangle className={`w-3 h-3 ${alarm.active ? 'text-yellow-400' : 'text-gray-400'}`} />
                      )}
                      <span className={alarm.active ? 'text-white' : 'text-gray-400'}>
                        {alarm.message}
                      </span>
                    </div>
                    <span className="text-gray-400">
                      {alarm.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );

      case 'textDisplay':
        const logEntries = [
          { timestamp: new Date(Date.now() - 1000 * 60 * 1), message: 'System startup complete' },
          { timestamp: new Date(Date.now() - 1000 * 60 * 2), message: 'Network connection established' },
          { timestamp: new Date(Date.now() - 1000 * 60 * 3), message: 'PLC communication active' },
          { timestamp: new Date(Date.now() - 1000 * 60 * 4), message: 'Pump station initialized' },
          { timestamp: new Date(Date.now() - 1000 * 60 * 5), message: 'Pressure sensor calibrated' },
          { timestamp: new Date(Date.now() - 1000 * 60 * 6), message: 'Temperature sensor calibrated' },
          { timestamp: new Date(Date.now() - 1000 * 60 * 7), message: 'Flow meter initialized' },
          { timestamp: new Date(Date.now() - 1000 * 60 * 8), message: 'Valve control system ready' },
          { timestamp: new Date(Date.now() - 1000 * 60 * 9), message: 'Safety systems verified' },
          { timestamp: new Date(Date.now() - 1000 * 60 * 10), message: 'HMI interface loaded' }
        ];
        
        return (
          <div 
            key={element.id} 
            style={elementStyle} 
            className="bg-gray-800/70 rounded-lg p-4"
            onClick={() => editMode && handleElementSelect(element.id)}
          >
            <div className="text-white font-medium mb-2">{element.properties?.title || 'Text Display'}</div>
            <div 
              className="bg-gray-900 rounded border border-gray-700 p-2 overflow-y-auto"
              style={{ 
                height: element.size.height - 60,
                fontSize: element.properties?.fontSize || 14
              }}
            >
              {logEntries.slice(0, element.properties?.rows || 10).map((entry, index) => (
                <div key={index} className="text-gray-300 mb-1">
                  <span className="text-gray-500">[{entry.timestamp.toLocaleTimeString()}]</span> {entry.message}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const renderScreenList = () => {
    if (!showScreenList) return null;
    
    return (
      <div className="absolute left-4 top-20 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-10 w-64">
        <div className="p-3 border-b border-gray-700 flex items-center justify-between">
          <h3 className="text-white font-medium">HMI Screens</h3>
          <button
            onClick={() => setShowScreenList(false)}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        <div className="max-h-96 overflow-y-auto">
          {screens.map((screen, index) => (
            <div 
              key={screen.id}
              className={`p-3 border-b border-gray-700 last:border-b-0 flex items-center justify-between cursor-pointer hover:bg-gray-700/50 ${
                selectedScreen === index ? 'bg-blue-600/20 border-l-2 border-blue-500' : ''
              }`}
              onClick={() => {
                setSelectedScreen(index);
                setShowScreenList(false);
              }}
            >
              <div className="flex items-center space-x-2">
                <Monitor className="w-4 h-4 text-gray-400" />
                <span className="text-white">{screen.name}</span>
              </div>
              <div className="text-xs text-gray-400">
                {screen.size.width}×{screen.size.height}
              </div>
            </div>
          ))}
        </div>
        <div className="p-3 border-t border-gray-700">
          <button
            onClick={handleAddScreen}
            className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Add New Screen</span>
          </button>
        </div>
      </div>
    );
  };

  const renderAddScreenModal = () => {
    if (!showAddScreenModal) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md">
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Add New HMI Screen</h3>
            <button
              onClick={() => setShowAddScreenModal(false)}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="p-6 space-y-4">
            <div>
              <label className="block text-sm text-gray-400 mb-2">Screen Name</label>
              <input
                type="text"
                value={newScreenName}
                onChange={(e) => setNewScreenName(e.target.value)}
                placeholder="Enter screen name"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
            </div>
            
            <div className="pt-4 border-t border-gray-700 flex justify-end">
              <button
                onClick={() => setShowAddScreenModal(false)}
                className="text-gray-400 hover:text-white px-4 py-2 mr-2"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateScreen}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
                disabled={!newScreenName.trim()}
              >
                Create Screen
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderElementProperties = () => {
    if (!selectedElement || !showElementProperties) return null;
    
    const element = currentScreen.elements.find(e => e.id === selectedElement);
    if (!element) return null;
    
    return (
      <div className="absolute right-6 top-24 w-80 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-10">
        <div className="flex items-center justify-between p-3 border-b border-gray-700">
          <h3 className="text-white font-medium">Element Properties</h3>
          <button
            onClick={() => setShowElementProperties(false)}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        <div className="p-4 space-y-4">
          <div>
            <label className="block text-sm text-gray-400 mb-2">Element Type</label>
            <div className="bg-gray-700 rounded p-2 text-white">
              {element.type.charAt(0).toUpperCase() + element.type.slice(1)}
            </div>
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-2">Position</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">X</label>
                <input
                  type="number"
                  value={element.position.x}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Y</label>
                <input
                  type="number"
                  value={element.position.y}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                />
              </div>
            </div>
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-2">Size</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Width</label>
                <input
                  type="number"
                  value={element.size.width}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Height</label>
                <input
                  type="number"
                  value={element.size.height}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                />
              </div>
            </div>
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-2">Tag Binding</label>
            <input
              type="text"
              value={element.tag || ''}
              placeholder="Enter tag name"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            />
          </div>
          
          {element.type === 'text' && (
            <div>
              <label className="block text-sm text-gray-400 mb-2">Text Content</label>
              <textarea
                value={element.properties?.text || ''}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white h-20 resize-none"
              />
              
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Font Size</label>
                  <input
                    type="number"
                    value={element.properties?.fontSize || 14}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Color</label>
                  <select
                    value={element.properties?.color || 'white'}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                  >
                    <option value="white">White</option>
                    <option value="red">Red</option>
                    <option value="green">Green</option>
                    <option value="blue">Blue</option>
                    <option value="yellow">Yellow</option>
                  </select>
                </div>
              </div>
            </div>
          )}
          
          {element.type === 'button' && (
            <div>
              <label className="block text-sm text-gray-400 mb-2">Button Text</label>
              <input
                type="text"
                value={element.properties?.text || ''}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              />
              
              <div className="grid grid-cols-2 gap-2 mt-2">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Color</label>
                  <select
                    value={element.properties?.color || 'blue'}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                  >
                    <option value="blue">Blue</option>
                    <option value="green">Green</option>
                    <option value="red">Red</option>
                    <option value="gray">Gray</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Action</label>
                  <select
                    value={element.properties?.pressAction || 'momentary'}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                  >
                    <option value="momentary">Momentary</option>
                    <option value="toggle">Toggle</option>
                  </select>
                </div>
              </div>
            </div>
          )}
          
          <div className="pt-4 border-t border-gray-700 flex justify-between">
            <button
              className="flex items-center space-x-1 text-red-400 hover:text-red-300"
            >
              <Trash2 className="w-4 h-4" />
              <span>Delete</span>
            </button>
            <button
              onClick={() => setShowElementProperties(false)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded"
            >
              Apply
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`bg-gray-900 flex flex-col ${isFullscreen ? 'fixed inset-0 z-50' : 'h-full'}`}>
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Monitor className="w-5 h-5 text-blue-400" />
            <h3 className="text-lg font-semibold text-white">HMI Preview</h3>
            <div className={`px-3 py-1 rounded-full text-sm ${
              simulationMode 
                ? 'bg-green-600/20 text-green-400 border border-green-600/30' 
                : 'bg-gray-600/20 text-gray-400 border border-gray-600/30'
            }`}>
              {simulationMode ? 'LIVE' : 'OFFLINE'}
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowScreenList(!showScreenList)}
                className={`p-2 rounded transition-colors ${
                  showScreenList ? 'bg-blue-600/20 text-blue-400' : 'text-gray-400 hover:text-white'
                }`}
                title="Screen List"
              >
                <List className="w-4 h-4" />
              </button>
              
              <button
                onClick={() => setEditMode(!editMode)}
                className={`p-2 rounded transition-colors ${
                  editMode ? 'bg-blue-600/20 text-blue-400' : 'text-gray-400 hover:text-white'
                }`}
                title={editMode ? 'Exit Edit Mode' : 'Edit Mode'}
              >
                <Edit className="w-4 h-4" />
              </button>
              
              <button
                onClick={() => setShowGrid(!showGrid)}
                className={`p-2 rounded transition-colors ${
                  showGrid ? 'bg-blue-600/20 text-blue-400' : 'text-gray-400 hover:text-white'
                }`}
                title="Toggle Grid"
              >
                <Grid className="w-4 h-4" />
              </button>
              
              <button
                onClick={toggleFullscreen}
                className="p-2 text-gray-400 hover:text-white rounded transition-colors"
                title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
              >
                {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </button>
            </div>
            
            <div className="flex items-center space-x-2 border-l border-gray-700 pl-4">
              <button
                onClick={handlePrevScreen}
                className="p-2 text-gray-400 hover:text-white rounded transition-colors"
                title="Previous Screen"
                disabled={screens.length <= 1}
              >
                <ChevronLeft className="w-4 h-4" />
              </button>
              
              <select
                value={selectedScreen}
                onChange={(e) => setSelectedScreen(parseInt(e.target.value))}
                className="bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm min-w-40"
              >
                {screens.map((screen, index) => (
                  <option key={screen.id} value={index}>
                    {screen.name}
                  </option>
                ))}
              </select>
              
              <button
                onClick={handleNextScreen}
                className="p-2 text-gray-400 hover:text-white rounded transition-colors"
                title="Next Screen"
                disabled={screens.length <= 1}
              >
                <ChevronRight className="w-4 h-4" />
              </button>
              
              <div className="flex items-center space-x-1 ml-2">
                <button
                  onClick={handleAddScreen}
                  className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                  title="Add Screen"
                >
                  <Plus className="w-4 h-4" />
                </button>
                <button
                  onClick={handleDuplicateScreen}
                  className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
                  title="Duplicate Screen"
                >
                  <Copy className="w-4 h-4" />
                </button>
                <button
                  onClick={handleDeleteScreen}
                  className="p-1 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded transition-colors"
                  title="Delete Screen"
                  disabled={screens.length <= 1}
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {!simulationMode && !editMode && (
          <div className="mt-3 p-3 bg-amber-600/20 border border-amber-600/30 rounded">
            <p className="text-amber-400 text-sm">
              Enable simulation mode to interact with HMI elements or enter edit mode to modify the design
            </p>
          </div>
        )}
        
        {editMode && (
          <div className="mt-3 p-3 bg-blue-600/20 border border-blue-600/30 rounded">
            <div className="flex items-center justify-between">
              <p className="text-blue-400 text-sm">
                Edit Mode: Click on elements to select and edit their properties
              </p>
              <button
                onClick={() => setEditMode(false)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
              >
                Exit Edit Mode
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Simulation Controls */}
      {simulationMode && (
        <div className="bg-gray-800/50 border-b border-gray-700 p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-400">Animation Speed:</span>
                <select
                  value={animationSpeed}
                  onChange={(e) => setAnimationSpeed(Number(e.target.value))}
                  className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-sm"
                >
                  <option value={0.5}>0.5x</option>
                  <option value={1}>1x</option>
                  <option value={2}>2x</option>
                  <option value={5}>5x</option>
                </select>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-400">Screen:</span>
                <span className="text-white text-sm">
                  {currentScreen.size.width} × {currentScreen.size.height}
                </span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button 
                onClick={handleRunSequence}
                className="flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                <Play className="w-3 h-3" />
                <span>Run Sequence</span>
              </button>
              
              <button className="flex items-center space-x-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm transition-colors">
                <Pause className="w-3 h-3" />
                <span>Pause</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Screen Navigation */}
      {screens.length > 1 && (
        <div className="bg-gray-800/30 border-b border-gray-700 py-1 px-4">
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-1">
              {screens.map((screen, index) => (
                <button
                  key={screen.id}
                  onClick={() => setSelectedScreen(index)}
                  className={`w-3 h-3 rounded-full ${
                    selectedScreen === index ? 'bg-blue-500' : 'bg-gray-600 hover:bg-gray-500'
                  }`}
                  title={screen.name}
                />
              ))}
            </div>
          </div>
        </div>
      )}

      {/* HMI Screen */}
      <div className="flex-1 p-6 overflow-auto flex items-center justify-center bg-gray-950">
        <div className="mx-auto bg-gray-800 rounded-lg border-2 border-gray-700 shadow-2xl relative" 
             style={{ 
               width: currentScreen.size.width, 
               height: currentScreen.size.height,
               minWidth: currentScreen.size.width,
               minHeight: currentScreen.size.height
             }}>
          
          {/* Screen Header */}
          <div className="bg-gray-700 px-4 py-2 rounded-t-lg border-b border-gray-600">
            <div className="flex items-center justify-between">
              <h4 className="text-white font-semibold">{currentScreen.name}</h4>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-gray-400">
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>
          
          {/* Screen Content */}
          <div className="relative bg-gray-900 rounded-b-lg" 
               style={{ 
                 width: currentScreen.size.width, 
                 height: currentScreen.size.height - 40 
               }}>
            {currentScreen.elements.map(renderHMIElement)}
            
            {/* Grid overlay for design mode */}
            {showGrid && (
              <div className="absolute inset-0 pointer-events-none opacity-10">
                <div className="w-full h-full" 
                     style={{
                       backgroundImage: 'linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',
                       backgroundSize: '20px 20px'
                     }} />
              </div>
            )}
            
            {/* Edit mode overlay */}
            {editMode && (
              <div className="absolute top-4 right-4 bg-blue-600/90 text-white px-3 py-2 rounded-lg text-sm">
                Edit Mode
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="bg-gray-800 border-t border-gray-700 p-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <span className="text-gray-400">Screen:</span>
            <span className="text-white">
              {selectedScreen + 1} of {screens.length}
            </span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-gray-400">Size:</span>
            <span className="text-white">
              {currentScreen.size.width} × {currentScreen.size.height}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {simulationMode ? (
              <CheckCircle2 className="w-4 h-4 text-green-400" />
            ) : editMode ? (
              <Edit className="w-4 h-4 text-blue-400" />
            ) : (
              <AlertTriangle className="w-4 h-4 text-amber-400" />
            )}
            <span className={simulationMode ? 'text-green-400' : editMode ? 'text-blue-400' : 'text-amber-400'}>
              {simulationMode ? 'Interactive' : editMode ? 'Edit Mode' : 'Preview Only'}
            </span>
          </div>
        </div>
      </div>
      
      {renderScreenList()}
      {renderElementProperties()}
      {renderAddScreenModal()}
    </div>
  );
};

// X icon component
const X = ({ className }: { className?: string }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    width="24" 
    height="24" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round" 
    className={className}
  >
    <path d="M18 6 6 18"></path>
    <path d="m6 6 12 12"></path>
  </svg>
);

export default HMIPreview;