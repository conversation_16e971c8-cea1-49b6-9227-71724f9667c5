import React, { useState, useEffect } from 'react';
import TopBar from './components/TopBar';
import ProjectNavigator from './components/ProjectNavigator';
import EditorPane from './components/EditorPane';
import PropertiesPanel from './components/PropertiesPanel';
import BottomPanel from './components/BottomPanel';
import CommandPalette from './components/CommandPalette';
import UserGuide from './components/help/UserGuide';
import AdminDashboard from './components/admin/AdminDashboard';

function App() {
  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);
  const [userGuideOpen, setUserGuideOpen] = useState(false);
  const [adminMode, setAdminMode] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setCommandPaletteOpen(true);
      }
      
      if ((e.metaKey || e.ctrlKey) && e.key === 'h') {
        e.preventDefault();
        setUserGuideOpen(prev => !prev);
      }
      
      // Toggle admin mode with Ctrl+Shift+A
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'A') {
        e.preventDefault();
        setAdminMode(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleOpenUserGuide = () => {
    setUserGuideOpen(true);
  };

  if (userGuideOpen) {
    return <UserGuide onClose={() => setUserGuideOpen(false)} />;
  }
  
  if (adminMode) {
    return <AdminDashboard />;
  }

  return (
    <div className="h-screen bg-base-dark text-neutral overflow-hidden">
      <TopBar 
        onCommandPaletteOpen={() => setCommandPaletteOpen(true)} 
        onOpenUserGuide={handleOpenUserGuide}
      />
      
      <div className="flex h-[calc(100vh-3.5rem)]">
        <ProjectNavigator />
        
        <div className="flex-1 flex flex-col">
          <div className="flex flex-1">
            <EditorPane />
            <PropertiesPanel />
          </div>
          <BottomPanel />
        </div>
      </div>

      <CommandPalette 
        isOpen={commandPaletteOpen}
        onClose={() => setCommandPaletteOpen(false)}
      />
    </div>
  );
}

export default App;