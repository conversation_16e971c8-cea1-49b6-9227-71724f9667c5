import React, { useState } from 'react';
import { CheckCircle2, Al<PERSON><PERSON>riangle, <PERSON>Circle, Info, HelpCircle } from 'lucide-react';

interface ConfidenceBadgeProps {
  confidence: number;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  showBreakdown?: boolean;
  breakdown?: {
    contextQuality: number;
    promptClarity: number;
    domainMatch: number;
    safetyCheck: number;
  };
  className?: string;
}

const ConfidenceBadge: React.FC<ConfidenceBadgeProps> = ({ 
  confidence, 
  size = 'md', 
  showText = true,
  showBreakdown = false,
  breakdown = {
    contextQuality: 0.8,
    promptClarity: 0.7,
    domainMatch: 0.9,
    safetyCheck: 0.85
  },
  className = ''
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const getConfidenceLevel = () => {
    if (confidence >= 0.8) return 'high';
    if (confidence >= 0.6) return 'medium';
    if (confidence >= 0.4) return 'low';
    return 'very-low';
  };

  const getConfidenceColor = () => {
    const level = getConfidenceLevel();
    switch (level) {
      case 'high': return 'text-green-400 bg-green-400/20 border-green-400/30';
      case 'medium': return 'text-yellow-400 bg-yellow-400/20 border-yellow-400/30';
      case 'low': return 'text-orange-400 bg-orange-400/20 border-orange-400/30';
      case 'very-low': return 'text-red-400 bg-red-400/20 border-red-400/30';
      default: return 'text-gray-400 bg-gray-400/20 border-gray-400/30';
    }
  };

  const getConfidenceIcon = () => {
    const level = getConfidenceLevel();
    const iconSize = size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4';
    
    switch (level) {
      case 'high': return <CheckCircle2 className={iconSize} />;
      case 'medium': return <Info className={iconSize} />;
      case 'low': return <AlertTriangle className={iconSize} />;
      case 'very-low': return <XCircle className={iconSize} />;
      default: return <Info className={iconSize} />;
    }
  };

  const getConfidenceText = () => {
    const level = getConfidenceLevel();
    switch (level) {
      case 'high': return 'High Confidence';
      case 'medium': return 'Medium Confidence';
      case 'low': return 'Low Confidence';
      case 'very-low': return 'Very Low Confidence';
      default: return 'Unknown';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'px-2 py-1 text-xs';
      case 'lg': return 'px-4 py-2 text-base';
      default: return 'px-3 py-1 text-sm';
    }
  };

  const renderBreakdownTooltip = () => {
    if (!breakdown || !showTooltip) return null;

    return (
      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-gray-800 border border-gray-600 rounded-lg shadow-xl z-50 p-3">
        <div className="text-white font-semibold mb-2 text-sm">Confidence Breakdown</div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-400">Context Quality</span>
            <div className="flex items-center space-x-2">
              <div className="w-16 bg-gray-700 rounded-full h-1">
                <div 
                  className="bg-blue-400 h-1 rounded-full" 
                  style={{ width: `${breakdown.contextQuality * 100}%` }}
                />
              </div>
              <span className="text-white w-8">{Math.round(breakdown.contextQuality * 100)}%</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-400">Prompt Clarity</span>
            <div className="flex items-center space-x-2">
              <div className="w-16 bg-gray-700 rounded-full h-1">
                <div 
                  className="bg-green-400 h-1 rounded-full" 
                  style={{ width: `${breakdown.promptClarity * 100}%` }}
                />
              </div>
              <span className="text-white w-8">{Math.round(breakdown.promptClarity * 100)}%</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-400">Domain Match</span>
            <div className="flex items-center space-x-2">
              <div className="w-16 bg-gray-700 rounded-full h-1">
                <div 
                  className="bg-purple-400 h-1 rounded-full" 
                  style={{ width: `${breakdown.domainMatch * 100}%` }}
                />
              </div>
              <span className="text-white w-8">{Math.round(breakdown.domainMatch * 100)}%</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-400">Safety Check</span>
            <div className="flex items-center space-x-2">
              <div className="w-16 bg-gray-700 rounded-full h-1">
                <div 
                  className="bg-orange-400 h-1 rounded-full" 
                  style={{ width: `${breakdown.safetyCheck * 100}%` }}
                />
              </div>
              <span className="text-white w-8">{Math.round(breakdown.safetyCheck * 100)}%</span>
            </div>
          </div>
        </div>
        
        <div className="mt-3 pt-2 border-t border-gray-700 text-xs text-gray-400">
          Overall confidence is calculated from these factors
        </div>
      </div>
    );
  };

  return (
    <div className="relative">
      <div 
        className={`inline-flex items-center space-x-1 rounded-full border ${getConfidenceColor()} ${getSizeClasses()} ${className} ${showBreakdown ? 'cursor-help' : ''}`}
        title={`AI Confidence: ${Math.round(confidence * 100)}%`}
        onMouseEnter={() => showBreakdown && setShowTooltip(true)}
        onMouseLeave={() => showBreakdown && setShowTooltip(false)}
      >
        {getConfidenceIcon()}
        {showText && (
          <span className="font-medium">
            {getConfidenceText()}
          </span>
        )}
        <span className="font-mono">
          {Math.round(confidence * 100)}%
        </span>
        {showBreakdown && (
          <HelpCircle className="w-3 h-3 opacity-60" />
        )}
      </div>
      
      {renderBreakdownTooltip()}
    </div>
  );
};

export default ConfidenceBadge;