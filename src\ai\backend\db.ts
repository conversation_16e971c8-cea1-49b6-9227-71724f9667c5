import { Pool } from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL || 'postgresql://postgres:password@localhost:5432/lureon_ai',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Simple query wrapper
export const db = {
  query: (text: string, params?: any[]) => pool.query(text, params),
  getClient: () => pool.connect()
};

// Initialize database tables if they don't exist
export const initDatabase = async () => {
  try {
    // Create context items table
    await db.query(`
      CREATE TABLE IF NOT EXISTS mcp_context_items (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        type VARCHAR(50) NOT NULL,
        source VARCHAR(50) NOT NULL,
        value TEXT NOT NULL,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        user_id UUID,
        project_id UUID,
        metadata JSONB DEFAULT '{}'::jsonb,
        expires_at TIMESTAMP WITH TIME ZONE,
        version INT DEFAULT 1,
        CONSTRAINT valid_type CHECK (type IN ('tag', 'program', 'rung', 'safety', 'doc', 'version', 'standard'))
      )
    `);

    // Create context relationships table
    await db.query(`
      CREATE TABLE IF NOT EXISTS mcp_context_relationships (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        source_id UUID REFERENCES mcp_context_items(id) ON DELETE CASCADE,
        target_id UUID REFERENCES mcp_context_items(id) ON DELETE CASCADE,
        relationship_type VARCHAR(50) NOT NULL,
        strength FLOAT DEFAULT 1.0,
        metadata JSONB DEFAULT '{}'::jsonb,
        CONSTRAINT valid_relationship CHECK (relationship_type IN ('depends_on', 'related_to', 'conflicts_with', 'enhances'))
      )
    `);

    // Create context usage table
    await db.query(`
      CREATE TABLE IF NOT EXISTS mcp_context_usage (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        context_id UUID REFERENCES mcp_context_items(id) ON DELETE CASCADE,
        request_id UUID NOT NULL,
        prompt_type VARCHAR(50) NOT NULL,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        confidence_contribution FLOAT,
        user_id UUID,
        metadata JSONB DEFAULT '{}'::jsonb
      )
    `);

    // Create indexes
    await db.query(`CREATE INDEX IF NOT EXISTS idx_context_items_type ON mcp_context_items(type)`);
    await db.query(`CREATE INDEX IF NOT EXISTS idx_context_items_project ON mcp_context_items(project_id)`);
    await db.query(`CREATE INDEX IF NOT EXISTS idx_context_items_timestamp ON mcp_context_items(timestamp)`);
    await db.query(`CREATE INDEX IF NOT EXISTS idx_context_relationships_source ON mcp_context_relationships(source_id)`);
    await db.query(`CREATE INDEX IF NOT EXISTS idx_context_relationships_target ON mcp_context_relationships(target_id)`);
    await db.query(`CREATE INDEX IF NOT EXISTS idx_context_usage_context ON mcp_context_usage(context_id)`);
    await db.query(`CREATE INDEX IF NOT EXISTS idx_context_usage_request ON mcp_context_usage(request_id)`);
    await db.query(`CREATE INDEX IF NOT EXISTS idx_context_usage_timestamp ON mcp_context_usage(timestamp)`);

    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
};

// Function to clean up expired context items
export const cleanupExpiredContext = async () => {
  try {
    const result = await db.query(
      'DELETE FROM mcp_context_items WHERE expires_at < NOW() RETURNING id'
    );
    
    if (result.rowCount > 0) {
      console.log(`Cleaned up ${result.rowCount} expired context items`);
    }
  } catch (error) {
    console.error('Error cleaning up expired context:', error);
  }
};

// Schedule cleanup to run daily
setInterval(cleanupExpiredContext, 24 * 60 * 60 * 1000);