import React, { useState } from 'react';
import { 
  Users, 
  FileText, 
  Key, 
  BookOpen, 
  Settings, 
  BarChart3, 
  Shield, 
  Package, 
  Grid, 
  Database,
  Home,
  HelpCircle,
  Bell,
  Search,
  User,
  LogOut
} from 'lucide-react';
import UserManagement from './UserManagement';
import TemplateManager from './TemplateManager';
import LicenseManager from './LicenseManager';
import TutorialManager from './TutorialManager';
import SystemSettings from './SystemSettings';
import AnalyticsDashboard from './AnalyticsDashboard';
import SecurityCenter from './SecurityCenter';
import DeploymentManager from './DeploymentManager';

const AdminDashboard: React.FC = () => {
  const [activeModule, setActiveModule] = useState('overview');
  
  const modules = [
    { id: 'overview', name: 'Overview', icon: Home },
    { id: 'users', name: 'User Management', icon: Users },
    { id: 'templates', name: 'Template Library', icon: FileText },
    { id: 'licenses', name: 'License Management', icon: Key },
    { id: 'tutorials', name: 'Tutorials & Guides', icon: BookOpen },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'security', name: 'Security Center', icon: Shield },
    { id: 'deployments', name: 'Deployment Manager', icon: Package },
    { id: 'settings', name: 'System Settings', icon: Settings }
  ];

  const renderActiveModule = () => {
    switch (activeModule) {
      case 'users':
        return <UserManagement />;
      case 'templates':
        return <TemplateManager />;
      case 'licenses':
        return <LicenseManager />;
      case 'tutorials':
        return <TutorialManager />;
      case 'analytics':
        return <AnalyticsDashboard />;
      case 'security':
        return <SecurityCenter />;
      case 'deployments':
        return <DeploymentManager />;
      case 'settings':
        return <SystemSettings />;
      default:
        return <OverviewDashboard setActiveModule={setActiveModule} />;
    }
  };

  return (
    <div className="h-screen bg-gray-900 flex overflow-hidden">
      {/* Sidebar */}
      <div className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-700 flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-teal-500 to-blue-500 rounded-lg flex items-center justify-center">
            <Grid className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-white font-semibold">LUREON Admin</h1>
            <p className="text-xs text-gray-400">Administration Portal</p>
          </div>
        </div>
        
        <nav className="flex-1 overflow-y-auto py-4">
          <ul className="space-y-1 px-2">
            {modules.map(module => {
              const IconComponent = module.icon;
              return (
                <li key={module.id}>
                  <button
                    onClick={() => setActiveModule(module.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                      activeModule === module.id
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-300 hover:bg-gray-700'
                    }`}
                  >
                    <IconComponent className="w-5 h-5" />
                    <span>{module.name}</span>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>
        
        <div className="p-4 border-t border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center">
              <span className="text-white font-semibold">SA</span>
            </div>
            <div>
              <p className="text-white">Sarah Admin</p>
              <p className="text-xs text-gray-400">Administrator</p>
            </div>
          </div>
          <button className="mt-4 w-full flex items-center justify-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded-lg text-sm transition-colors">
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-gray-800 border-b border-gray-700 py-4 px-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-white">
              {modules.find(m => m.id === activeModule)?.name || 'Overview'}
            </h2>
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <button className="p-2 text-gray-300 hover:text-white relative">
                <Bell className="w-5 h-5" />
                <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>
              <button className="p-2 text-gray-300 hover:text-white">
                <HelpCircle className="w-5 h-5" />
              </button>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 overflow-y-auto bg-gray-900 p-6">
          {renderActiveModule()}
        </main>
      </div>
    </div>
  );
};

const OverviewDashboard: React.FC<{ setActiveModule: (module: string) => void }> = ({ setActiveModule }) => {
  const stats = [
    { name: 'Total Users', value: '1,248', change: '+12%', icon: Users, color: 'bg-blue-500' },
    { name: 'Active Licenses', value: '876', change: '+5%', icon: Key, color: 'bg-green-500' },
    { name: 'Templates', value: '124', change: '+8%', icon: FileText, color: 'bg-purple-500' },
    { name: 'Deployments', value: '3,421', change: '+22%', icon: Package, color: 'bg-orange-500' }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div key={index} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <span className="text-green-400 text-sm">{stat.change}</span>
              </div>
              <h3 className="text-2xl font-bold text-white">{stat.value}</h3>
              <p className="text-gray-400">{stat.name}</p>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => setActiveModule('users')}
              className="flex flex-col items-center justify-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
            >
              <Users className="w-8 h-8 text-blue-400 mb-2" />
              <span className="text-white">Manage Users</span>
            </button>
            <button
              onClick={() => setActiveModule('templates')}
              className="flex flex-col items-center justify-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
            >
              <FileText className="w-8 h-8 text-purple-400 mb-2" />
              <span className="text-white">Edit Templates</span>
            </button>
            <button
              onClick={() => setActiveModule('licenses')}
              className="flex flex-col items-center justify-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
            >
              <Key className="w-8 h-8 text-green-400 mb-2" />
              <span className="text-white">Issue License</span>
            </button>
            <button
              onClick={() => setActiveModule('security')}
              className="flex flex-col items-center justify-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors"
            >
              <Shield className="w-8 h-8 text-red-400 mb-2" />
              <span className="text-white">Security</span>
            </button>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">System Status</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-400">Server Load</span>
                <span className="text-white">24%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '24%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-400">Database</span>
                <span className="text-white">68%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '68%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-400">Storage</span>
                <span className="text-white">42%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div className="bg-blue-500 h-2 rounded-full" style={{ width: '42%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span className="text-gray-400">Memory</span>
                <span className="text-white">35%</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div className="bg-purple-500 h-2 rounded-full" style={{ width: '35%' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6">
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 col-span-2">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {[
              { user: 'John Engineer', action: 'created a new project', time: '2 minutes ago' },
              { user: 'Sarah Admin', action: 'issued a new license', time: '15 minutes ago' },
              { user: 'Mike Safety', action: 'updated safety template', time: '1 hour ago' },
              { user: 'Lisa Operator', action: 'deployed to PLC target', time: '3 hours ago' },
              { user: 'David Manager', action: 'added a new user', time: '5 hours ago' }
            ].map((activity, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-700/50 rounded-lg">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-semibold">
                  {activity.user.charAt(0)}
                </div>
                <div className="flex-1">
                  <p className="text-white">
                    <span className="font-semibold">{activity.user}</span> {activity.action}
                  </p>
                  <p className="text-gray-400 text-sm">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Upcoming Maintenance</h3>
          <div className="space-y-4">
            <div className="p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-blue-400 font-medium">Database Backup</h4>
                <span className="text-xs text-gray-400">Scheduled</span>
              </div>
              <p className="text-sm text-gray-300 mb-2">Automated backup of all project data</p>
              <p className="text-xs text-gray-400">Tomorrow, 2:00 AM</p>
            </div>
            <div className="p-3 bg-purple-500/10 border border-purple-500/30 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-purple-400 font-medium">System Update</h4>
                <span className="text-xs text-gray-400">Pending</span>
              </div>
              <p className="text-sm text-gray-300 mb-2">Security patches and performance improvements</p>
              <p className="text-xs text-gray-400">June 30, 1:00 AM</p>
            </div>
            <div className="p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-yellow-400 font-medium">License Renewal</h4>
                <span className="text-xs text-gray-400">Action Required</span>
              </div>
              <p className="text-sm text-gray-300 mb-2">Enterprise license expires soon</p>
              <p className="text-xs text-gray-400">July 15</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;