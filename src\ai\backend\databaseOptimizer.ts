import { Pool } from 'pg';

export class DatabaseOptimizer {
    private pool: Pool;

    constructor(pool: Pool) {
        this.pool = pool;
    }

    async createOptimalIndexes() {
        const indexQueries = [
            // Audit logs performance indexes
            `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_user_timestamp 
       ON audit_logs(user_id, timestamp DESC)`,

            `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_model_confidence 
       ON audit_logs(model_used, confidence_score) WHERE confidence_score < 0.7`,

            // Usage metrics indexes
            `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_usage_metrics_org_period 
       ON usage_metrics(organization_id, created_at DESC)`,

            // Feedback indexes
            `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_feedback_rating_category 
       ON feedback(rating, category, timestamp DESC)`,

            // Cache indexes
            `CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_prompt_cache_hash_timestamp 
       ON prompt_cache(prompt_hash, created_at DESC)`
        ];

        for (const query of indexQueries) {
            try {
                await this.pool.query(query);
                console.log('Created index:', query.split('IF NOT EXISTS')[1].split('ON')[0].trim());
            } catch (error) {
                console.error('Failed to create index:', error);
            }
        }
    }

    async optimizeQueries() {
        // Analyze query performance
        const slowQueries = await this.pool.query(`
      SELECT query, mean_exec_time, calls, total_exec_time
      FROM pg_stat_statements 
      WHERE mean_exec_time > 1000 
      ORDER BY mean_exec_time DESC 
      LIMIT 10
    `);

        return slowQueries.rows;
    }

    async setupConnectionPooling() {
        // Configure optimal connection pool settings
        return {
            host: process.env.DB_HOST,
            database: process.env.DB_NAME,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            port: parseInt(process.env.DB_PORT || '5432'),
            max: 20, // Maximum pool size
            min: 5,  // Minimum pool size
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 10000,
            statement_timeout: 30000,
            query_timeout: 30000
        };
    }
}