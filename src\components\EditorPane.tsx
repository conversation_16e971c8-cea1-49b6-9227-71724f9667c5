import React, { useState } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  Code, 
  Grid3X3, 
  Workflow, 
  GitBranch,
  Play,
  Square,
  Download,
  Upload,
  Sparkles,
  Zap,
  Cable,
  Eye,
  Monitor,
  Network,
  BookOpen,
  Settings,
  Users,
  Shield,
  FileText,
  Activity
} from 'lucide-react';
import { usePLCStore } from '../store/plcStore';
import LadderEditor from './editors/LadderEditor';
import LadderToolbar from './editors/LadderToolbar';
import StructuredTextEditor from './editors/StructuredTextEditor';
import StructuredTextToolbar from './editors/StructuredTextToolbar';
import SafetyToolbar from './editors/SafetyToolbar';
import PLCSimulator from './simulation/PLCSimulator';
import IOMapping from './io/IOMapping';
import SignalTracer from './debugging/SignalTracer';
import HMIPreview from './hmi/HMIPreview';
import NetworkTopology from './network/NetworkTopology';
import TemplateLibrary from './templates/TemplateLibrary';
import SystemAdmin from './enterprise/SystemAdmin';
import AiActionLogPanel from '../ai/components/AiActionLogPanel';

interface EditorTab {
  id: string;
  name: string;
  type: 'ladder' | 'st' | 'fbd' | 'sfc' | 'safety' | 'simulator' | 'io-mapping' | 'signal-tracer' | 'hmi-preview' | 'network' | 'templates' | 'system-admin' | 'ai-logs';
  modified: boolean;
}

const EditorPane: React.FC = () => {
  const { 
    currentProject, 
    activeProgram, 
    simulationMode, 
    toggleSimulation,
    updateProgram,
    addRung
  } = usePLCStore();
  
  const [activeTab, setActiveTab] = useState('main-program');

  const program = currentProject?.programs.find(p => p.id === activeProgram);
  
  const tabs: EditorTab[] = [
    ...(currentProject?.programs.map(p => ({
      id: p.id,
      name: p.name,
      type: p.type,
      modified: p.modified
    })) || []),
    {
      id: 'simulator',
      name: 'Simulator',
      type: 'simulator' as const,
      modified: false
    },
    {
      id: 'io-mapping',
      name: 'I/O Mapping',
      type: 'io-mapping' as const,
      modified: false
    },
    {
      id: 'signal-tracer',
      name: 'Signal Tracer',
      type: 'signal-tracer' as const,
      modified: false
    },
    {
      id: 'hmi-preview',
      name: 'HMI Preview',
      type: 'hmi-preview' as const,
      modified: false
    },
    {
      id: 'network',
      name: 'Network',
      type: 'network' as const,
      modified: false
    },
    {
      id: 'templates',
      name: 'Templates',
      type: 'templates' as const,
      modified: false
    },
    {
      id: 'ai-logs',
      name: 'AI Activity',
      type: 'ai-logs' as const,
      modified: false
    },
    {
      id: 'system-admin',
      name: 'System Admin',
      type: 'system-admin' as const,
      modified: false
    }
  ];

  const getTabIcon = (type: string) => {
    switch (type) {
      case 'ladder': return <Grid3X3 className="w-4 h-4" />;
      case 'st': return <Code className="w-4 h-4" />;
      case 'fbd': return <Workflow className="w-4 h-4" />;
      case 'sfc': return <GitBranch className="w-4 h-4" />;
      case 'safety': return <Sparkles className="w-4 h-4" />;
      case 'simulator': return <Zap className="w-4 h-4" />;
      case 'io-mapping': return <Cable className="w-4 h-4" />;
      case 'signal-tracer': return <Eye className="w-4 h-4" />;
      case 'hmi-preview': return <Monitor className="w-4 h-4" />;
      case 'network': return <Network className="w-4 h-4" />;
      case 'templates': return <BookOpen className="w-4 h-4" />;
      case 'ai-logs': return <Activity className="w-4 h-4" />;
      case 'system-admin': return <Settings className="w-4 h-4" />;
      default: return <Code className="w-4 h-4" />;
    }
  };

  const getTabColor = (type: string) => {
    switch (type) {
      case 'ladder': return 'text-accent';
      case 'st': return 'text-success';
      case 'fbd': return 'text-secondary';
      case 'sfc': return 'text-primary';
      case 'safety': return 'text-error';
      case 'simulator': return 'text-primary';
      case 'io-mapping': return 'text-accent';
      case 'signal-tracer': return 'text-secondary';
      case 'hmi-preview': return 'text-control-400';
      case 'network': return 'text-accent';
      case 'templates': return 'text-secondary';
      case 'ai-logs': return 'text-purple-400';
      case 'system-admin': return 'text-control-400';
      default: return 'text-control-400';
    }
  };

  const handleTabClick = (tab: EditorTab) => {
    setActiveTab(tab.id);
  };

  const handleAddElement = (elementType: string) => {
    if (elementType === 'add-rung' && program && program.type === 'ladder') {
      // Add a new rung to the ladder program
      const content = program.content as LadderRung[];
      const newRung: LadderRung = {
        id: `rung-${Date.now()}`,
        number: content.length,
        elements: [],
        enabled: true
      };
      
      updateProgram(program.id, {
        content: [...content, newRung]
      });
    } else if (elementType === 'validate') {
      // Validation is handled within the LadderEditor component
      console.log('Validate logic');
    } else {
      // Pass the element type to the editor
      const activeTabData = tabs.find(t => t.id === activeTab);
      if (activeTabData?.type === 'ladder') {
        // Find the active ladder program
        const ladderProgram = currentProject?.programs.find(p => p.id === activeTab);
        if (ladderProgram) {
          const content = ladderProgram.content as LadderRung[];
          if (content.length > 0) {
            // Add element to the last rung
            const lastRung = content[content.length - 1];
            
            // Create a new element
            const newElement = {
              id: `elem-${Date.now()}`,
              type: elementType === 'contact-nc' ? 'contact' : elementType,
              position: { x: lastRung.elements.length * 2 + 1, y: 0 },
              connections: [],
              properties: elementType === 'contact-nc' 
                ? { normally: 'closed' } 
                : elementType === 'timer-tof'
                  ? { timerType: 'TOF', preset: 'T#5S' }
                  : elementType === 'counter-ctd'
                    ? { counterType: 'CTD', preset: 10 }
                    : {}
            };
            
            // Add the element to the rung
            const updatedContent = content.map((rung, index) => {
              if (index === content.length - 1) {
                return {
                  ...rung,
                  elements: [...rung.elements, newElement]
                };
              }
              return rung;
            });
            
            // Update the program
            updateProgram(ladderProgram.id, {
              content: updatedContent
            });
          } else {
            // Create a new rung first
            const newRung: LadderRung = {
              id: `rung-${Date.now()}`,
              number: 0,
              elements: [],
              enabled: true
            };
            
            updateProgram(ladderProgram.id, {
              content: [newRung]
            });
            
            // After creating the rung, add the element (in a setTimeout to ensure the rung is created first)
            setTimeout(() => {
              handleAddElement(elementType);
            }, 100);
          }
        }
      }
    }
  };

  const renderToolbar = () => {
    const activeTabData = tabs.find(t => t.id === activeTab);
    if (!activeTabData) return null;

    switch (activeTabData.type) {
      case 'ladder':
        return <LadderToolbar onAddElement={handleAddElement} />;
      case 'st':
        return <StructuredTextToolbar onAddElement={handleAddElement} />;
      case 'safety':
        return <SafetyToolbar onAddElement={handleAddElement} />;
      default:
        return null;
    }
  };

  const renderEditor = () => {
    const activeTabData = tabs.find(t => t.id === activeTab);
    
    if (!activeTabData) {
      return (
        <div className="h-full bg-base-900 flex items-center justify-center">
          <div className="text-center text-control-400">
            <Code className="w-16 h-16 mx-auto mb-4" />
            <p className="text-lg mb-2">No Tab Selected</p>
            <p className="text-sm">Select a tab to view content</p>
          </div>
        </div>
      );
    }

    switch (activeTabData.type) {
      case 'simulator':
        return <PLCSimulator />;
      case 'io-mapping':
        return <IOMapping />;
      case 'signal-tracer':
        return <SignalTracer />;
      case 'hmi-preview':
        return <HMIPreview />;
      case 'network':
        return <NetworkTopology />;
      case 'templates':
        return <TemplateLibrary />;
      case 'ai-logs':
        return <AiActionLogPanel />;
      case 'system-admin':
        return <SystemAdmin />;
      case 'ladder':
        return <LadderEditor />;
      case 'st':
        return <StructuredTextEditor />;
      case 'safety':
        return (
          <div className="h-full bg-base-900 flex items-center justify-center">
            <div className="text-center text-control-400">
              <Sparkles className="w-16 h-16 mx-auto mb-4 text-error" />
              <p className="text-lg mb-2 text-error">Safety Logic Editor</p>
              <p className="text-sm">SIL-rated safety programming environment</p>
              <p className="text-xs mt-2">Coming Soon - Certified safety function blocks</p>
            </div>
          </div>
        );
      case 'fbd':
        return (
          <div className="h-full bg-base-900 flex items-center justify-center">
            <div className="text-center text-control-400">
              <Workflow className="w-16 h-16 mx-auto mb-4" />
              <p className="text-lg mb-2">Function Block Diagram Editor</p>
              <p className="text-sm">Coming Soon - Advanced visual programming interface</p>
            </div>
          </div>
        );
      case 'sfc':
        return (
          <div className="h-full bg-base-900 flex items-center justify-center">
            <div className="text-center text-control-400">
              <GitBranch className="w-16 h-16 mx-auto mb-4" />
              <p className="text-lg mb-2">Sequential Function Chart Editor</p>
              <p className="text-sm">Coming Soon - State machine programming</p>
            </div>
          </div>
        );
      default:
        return (
          <div className="h-full bg-base-900 flex items-center justify-center">
            <div className="text-center text-control-400">
              <Code className="w-16 h-16 mx-auto mb-4" />
              <p className="text-lg mb-2">Editor Not Available</p>
              <p className="text-sm">This editor type is not yet implemented</p>
            </div>
          </div>
        );
    }
  };

  const handleDownload = () => {
    console.log('Downloading program to PLC...');
  };

  const handleUpload = () => {
    console.log('Uploading program from PLC...');
  };

  return (
    <div className="flex-1 flex flex-col bg-base-dark">
      {/* Tab Bar */}
      <div className="bg-control-800 border-b border-control-600 flex items-center justify-between">
        <div className="flex overflow-x-auto">
          {tabs.map(tab => (
            <div
              key={tab.id}
              className={`flex items-center px-4 py-2 text-sm cursor-pointer transition-colors border-r border-control-600 whitespace-nowrap ${
                activeTab === tab.id 
                  ? 'bg-base-dark text-neutral' 
                  : 'text-control-400 hover:text-neutral hover:bg-control-700'
              }`}
              onClick={() => handleTabClick(tab)}
            >
              <span className={getTabColor(tab.type)}>
                {getTabIcon(tab.type)}
              </span>
              <span className="ml-2">{tab.name}</span>
              {tab.modified && (
                <div className="w-2 h-2 bg-primary rounded-full ml-2"></div>
              )}
              {tab.type === 'ai-logs' && (
                <div className="ml-2 w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
              )}
            </div>
          ))}
        </div>
        
        <div className="flex items-center space-x-2 px-4">
          <button 
            onClick={toggleSimulation}
            className={`p-2 rounded transition-colors ${
              simulationMode 
                ? 'text-primary bg-primary/20 hover:bg-primary/30' 
                : 'text-control-400 hover:text-primary hover:bg-control-800'
            }`}
            title={simulationMode ? 'Stop Simulation' : 'Start Simulation'}
          >
            <Zap className="w-4 h-4" />
          </button>
          <button 
            className="p-2 text-control-400 hover:text-success hover:bg-control-800 rounded transition-colors"
            title="Start PLC"
          >
            <Play className="w-4 h-4" />
          </button>
          <button 
            className="p-2 text-control-400 hover:text-error hover:bg-control-800 rounded transition-colors"
            title="Stop PLC"
          >
            <Square className="w-4 h-4" />
          </button>
          <div className="w-px h-6 bg-control-600 mx-2"></div>
          <button 
            onClick={handleDownload}
            className="p-2 text-control-400 hover:text-accent hover:bg-control-800 rounded transition-colors"
            title="Download to PLC"
          >
            <Download className="w-4 h-4" />
          </button>
          <button 
            onClick={handleUpload}
            className="p-2 text-control-400 hover:text-secondary hover:bg-control-800 rounded transition-colors"
            title="Upload from PLC"
          >
            <Upload className="w-4 h-4" />
          </button>
          <button 
            className="p-2 text-control-400 hover:text-purple-400 hover:bg-control-800 rounded transition-colors"
            title="AI Assistant"
          >
            <Sparkles className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Toolbar */}
      {renderToolbar()}

      {/* Editor Content */}
      <div className="flex-1">
        {renderEditor()}
      </div>
    </div>
  );
};

export default EditorPane;