import { Request, Response } from 'express';
import { contextCollectionService } from './contextCollection';
import { promptEnhancementService } from './promptEnhancement';
import { confidenceScoringService } from './confidenceScoring';
import { contextStorageService } from './contextStorage';
import { ContextItem, ContextItemType } from '../../types/mcp';

// Context Items API
export const getContextItems = async (req: Request, res: Response) => {
  try {
    const { 
      types, 
      userId, 
      projectId, 
      source, 
      since,
      limit = '50',
      offset = '0'
    } = req.query;
    
    const filters: any = {};
    
    if (types) {
      filters.types = (types as string).split(',') as ContextItemType[];
    }
    
    if (userId) {
      filters.userId = userId as string;
    }
    
    if (projectId) {
      filters.projectId = projectId as string;
    }
    
    if (source) {
      filters.source = source as string;
    }
    
    if (since) {
      filters.since = new Date(since as string);
    }
    
    const contextItems = await contextStorageService.getContextItems(
      filters,
      parseInt(limit as string),
      parseInt(offset as string)
    );
    
    res.json(contextItems);
  } catch (error) {
    console.error('Failed to fetch context items:', error);
    res.status(500).json({ error: 'Failed to fetch context items' });
  }
};

export const getContextItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const contextItem = await contextStorageService.getContextItem(id);
    
    if (!contextItem) {
      return res.status(404).json({ error: 'Context item not found' });
    }
    
    res.json(contextItem);
  } catch (error) {
    console.error('Failed to fetch context item:', error);
    res.status(500).json({ error: 'Failed to fetch context item' });
  }
};

export const createContextItem = async (req: Request, res: Response) => {
  try {
    const { type, source, value, projectId, metadata } = req.body;
    
    // Validate input
    if (!type || !source || !value) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Create context item
    const contextItem = await contextStorageService.createContextItem({
      type,
      source,
      value,
      user_id: req.user?.id,
      project_id: projectId,
      metadata
    });
    
    if (!contextItem) {
      return res.status(500).json({ error: 'Failed to create context item' });
    }
    
    res.status(201).json(contextItem);
  } catch (error) {
    console.error('Failed to create context item:', error);
    res.status(500).json({ error: 'Failed to create context item' });
  }
};

export const updateContextItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { value, metadata, expires_at, version } = req.body;
    
    const contextItem = await contextStorageService.updateContextItem(id, {
      value,
      metadata,
      expires_at,
      version
    });
    
    if (!contextItem) {
      return res.status(404).json({ error: 'Context item not found' });
    }
    
    res.json(contextItem);
  } catch (error) {
    console.error('Failed to update context item:', error);
    res.status(500).json({ error: 'Failed to update context item' });
  }
};

export const deleteContextItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    
    const success = await contextStorageService.deleteContextItem(id);
    
    if (!success) {
      return res.status(404).json({ error: 'Context item not found' });
    }
    
    res.status(204).send();
  } catch (error) {
    console.error('Failed to delete context item:', error);
    res.status(500).json({ error: 'Failed to delete context item' });
  }
};

// Context Collection API
export const collectProgramContext = async (req: Request, res: Response) => {
  try {
    const { programId } = req.params;
    const { projectId } = req.query;
    
    const contextId = await contextCollectionService.collectProgramContext(
      programId,
      req.user?.id,
      projectId as string
    );
    
    res.json({ contextId });
  } catch (error) {
    console.error('Failed to collect program context:', error);
    res.status(500).json({ error: 'Failed to collect program context' });
  }
};

export const collectTagContext = async (req: Request, res: Response) => {
  try {
    const { tagId } = req.params;
    const { projectId } = req.query;
    
    const contextId = await contextCollectionService.collectTagContext(
      tagId,
      req.user?.id,
      projectId as string
    );
    
    res.json({ contextId });
  } catch (error) {
    console.error('Failed to collect tag context:', error);
    res.status(500).json({ error: 'Failed to collect tag context' });
  }
};

export const collectRungContext = async (req: Request, res: Response) => {
  try {
    const { rungId } = req.params;
    const { projectId } = req.query;
    
    const contextId = await contextCollectionService.collectRungContext(
      rungId,
      req.user?.id,
      projectId as string
    );
    
    res.json({ contextId });
  } catch (error) {
    console.error('Failed to collect rung context:', error);
    res.status(500).json({ error: 'Failed to collect rung context' });
  }
};

export const collectSafetyContext = async (req: Request, res: Response) => {
  try {
    const { programId } = req.params;
    const { projectId } = req.query;
    
    const contextId = await contextCollectionService.collectSafetyContext(
      programId,
      req.user?.id,
      projectId as string
    );
    
    res.json({ contextId });
  } catch (error) {
    console.error('Failed to collect safety context:', error);
    res.status(500).json({ error: 'Failed to collect safety context' });
  }
};

export const collectStandardContext = async (req: Request, res: Response) => {
  try {
    const { standard } = req.body;
    const { projectId } = req.query;
    
    if (!standard) {
      return res.status(400).json({ error: 'Standard name is required' });
    }
    
    const contextId = await contextCollectionService.collectStandardContext(
      standard,
      req.user?.id,
      projectId as string
    );
    
    res.json({ contextId });
  } catch (error) {
    console.error('Failed to collect standard context:', error);
    res.status(500).json({ error: 'Failed to collect standard context' });
  }
};

// Prompt Enhancement API
export const enhancePrompt = async (req: Request, res: Response) => {
  try {
    const { prompt, contextTypes, maxTokens } = req.body;
    
    // Validate input
    if (!prompt) {
      return res.status(400).json({ error: 'Missing prompt' });
    }
    
    // Enhance prompt with context
    const enhancedPrompt = await promptEnhancementService.enhancePrompt({
      prompt,
      contextTypes: contextTypes || ['program', 'tag', 'safety', 'standard'],
      maxTokens
    });
    
    res.json(enhancedPrompt);
  } catch (error) {
    console.error('Failed to enhance prompt:', error);
    res.status(500).json({ error: 'Failed to enhance prompt' });
  }
};

// Confidence Scoring API
export const calculateConfidence = async (req: Request, res: Response) => {
  try {
    const { contextItems, promptType } = req.body;
    
    // Validate input
    if (!contextItems || !Array.isArray(contextItems)) {
      return res.status(400).json({ error: 'Missing context items' });
    }
    
    // Calculate confidence
    const confidence = await confidenceScoringService.calculateConfidence({
      contextItems,
      promptType: promptType || 'generate'
    });
    
    res.json(confidence);
  } catch (error) {
    console.error('Failed to calculate confidence:', error);
    res.status(500).json({ error: 'Failed to calculate confidence' });
  }
};

// Context Relationships API
export const getRelatedContextItems = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { relationshipType } = req.query;
    
    const relatedItems = await contextStorageService.getRelatedContextItems(
      id,
      relationshipType as string
    );
    
    res.json(relatedItems);
  } catch (error) {
    console.error('Failed to get related context items:', error);
    res.status(500).json({ error: 'Failed to get related context items' });
  }
};

export const createContextRelationship = async (req: Request, res: Response) => {
  try {
    const { sourceId, targetId, relationshipType, strength, metadata } = req.body;
    
    // Validate input
    if (!sourceId || !targetId || !relationshipType) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Create relationship
    const relationship = await contextStorageService.createContextRelationship({
      source_id: sourceId,
      target_id: targetId,
      relationship_type: relationshipType,
      strength,
      metadata
    });
    
    if (!relationship) {
      return res.status(500).json({ error: 'Failed to create context relationship' });
    }
    
    res.status(201).json(relationship);
  } catch (error) {
    console.error('Failed to create context relationship:', error);
    res.status(500).json({ error: 'Failed to create context relationship' });
  }
};