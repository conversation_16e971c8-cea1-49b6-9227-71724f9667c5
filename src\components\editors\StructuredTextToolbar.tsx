import React from 'react';
import { 
  Code, 
  RotateCcw, 
  GitBranch, 
  Timer, 
  Hash, 
  Calculator,
  Zap,
  Shield,
  FileText,
  Play,
  Pause
} from 'lucide-react';

interface StructuredTextToolbarProps {
  onAddElement: (elementType: string) => void;
  disabled?: boolean;
}

const StructuredTextToolbar: React.FC<StructuredTextToolbarProps> = ({ onAddElement, disabled = false }) => {
  const toolbarItems = [
    {
      id: 'if-then',
      name: 'IF-THEN',
      icon: <GitBranch className="w-4 h-4" />,
      code: 'IF condition THEN\n    // statements\nEND_IF;',
      description: 'Conditional Statement',
      action: () => onAddElement('if-then')
    },
    {
      id: 'for-loop',
      name: 'FOR Loop',
      icon: <RotateCcw className="w-4 h-4" />,
      code: 'FOR i := 1 TO 10 DO\n    // statements\nEND_FOR;',
      description: 'For Loop',
      action: () => onAddElement('for-loop')
    },
    {
      id: 'while-loop',
      name: 'WHILE Loop',
      icon: <RotateCcw className="w-4 h-4" />,
      code: 'WHILE condition DO\n    // statements\nEND_WHILE;',
      description: 'While Loop',
      action: () => onAddElement('while-loop')
    },
    {
      id: 'case',
      name: 'CASE',
      icon: <GitBranch className="w-4 h-4" />,
      code: 'CASE variable OF\n    1: // statement\n    2: // statement\nEND_CASE;',
      description: 'Case Statement',
      action: () => onAddElement('case')
    },
    {
      id: 'function-block',
      name: 'Function Block',
      icon: <Code className="w-4 h-4" />,
      code: 'FUNCTION_BLOCK FB_Name\nVAR_INPUT\n    // inputs\nEND_VAR\nVAR_OUTPUT\n    // outputs\nEND_VAR\n// logic\nEND_FUNCTION_BLOCK',
      description: 'Function Block Declaration',
      action: () => onAddElement('function-block')
    },
    {
      id: 'timer-ton',
      name: 'Timer TON',
      icon: <Timer className="w-4 h-4" />,
      code: 'Timer_1(IN := Start_Signal, PT := T#5S);\nOutput := Timer_1.Q;',
      description: 'Timer On Delay',
      action: () => onAddElement('timer-ton')
    },
    {
      id: 'counter',
      name: 'Counter',
      icon: <Hash className="w-4 h-4" />,
      code: 'Counter_1(CU := Count_Signal, R := Reset_Signal, PV := 10);\nCount_Value := Counter_1.CV;',
      description: 'Counter Function',
      action: () => onAddElement('counter')
    },
    {
      id: 'math',
      name: 'Math',
      icon: <Calculator className="w-4 h-4" />,
      code: 'Result := Value1 + Value2;',
      description: 'Math Operations',
      action: () => onAddElement('math')
    },
    {
      id: 'pid',
      name: 'PID',
      icon: <Zap className="w-4 h-4" />,
      code: 'PID_1(AUTO := TRUE, SP := Setpoint, PV := Process_Value);\nOutput := PID_1.CV;',
      description: 'PID Controller',
      action: () => onAddElement('pid')
    },
    {
      id: 'safety',
      name: 'Safety FB',
      icon: <Shield className="w-4 h-4" />,
      code: 'Safety_FB(Enable := TRUE, Reset := Reset_Button);\nSafe_Output := Safety_FB.Q;',
      description: 'Safety Function Block',
      action: () => onAddElement('safety')
    },
    {
      id: 'comment',
      name: 'Comment',
      icon: <FileText className="w-4 h-4" />,
      code: '(* Multi-line comment *)\n// Single line comment',
      description: 'Add Comments',
      action: () => onAddElement('comment')
    }
  ];

  return (
    <div className="bg-gray-800 border-b border-gray-700 p-2">
      <div className="flex items-center space-x-1 overflow-x-auto">
        <div className="text-sm text-gray-400 mr-4 whitespace-nowrap">ST Elements:</div>
        
        {toolbarItems.map(item => (
          <button
            key={item.id}
            onClick={item.action}
            disabled={disabled}
            className="flex flex-col items-center p-2 min-w-20 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed border border-gray-600 rounded transition-colors group"
            title={`${item.description}\n\n${item.code}`}
          >
            <div className="text-green-400 mb-1">
              {item.icon}
            </div>
            <div className="text-xs text-white font-semibold text-center leading-tight">
              {item.name}
            </div>
          </button>
        ))}
        
        <div className="w-px h-12 bg-gray-600 mx-2" />
        
        {/* Control buttons */}
        <button
          className="flex flex-col items-center p-2 min-w-16 bg-green-700 hover:bg-green-600 border border-green-600 rounded transition-colors"
          title="Compile & Run"
        >
          <Play className="w-4 h-4 text-white mb-1" />
          <div className="text-xs text-white">Compile</div>
        </button>
        
        <button
          className="flex flex-col items-center p-2 min-w-16 bg-red-700 hover:bg-red-600 border border-red-600 rounded transition-colors"
          title="Stop Execution"
        >
          <Pause className="w-4 h-4 text-white mb-1" />
          <div className="text-xs text-white">Stop</div>
        </button>
      </div>
    </div>
  );
};

export default StructuredTextToolbar;