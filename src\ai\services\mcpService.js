"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mcpService = void 0;
class MCPService {
    constructor() {
        this.baseUrl = '/api/mcp';
    }
    // Context Items API
    async getContextItems(filters = {}, limit = 50, offset = 0) {
        try {
            const queryParams = new URLSearchParams();
            if (filters.types && filters.types.length > 0) {
                queryParams.set('types', filters.types.join(','));
            }
            if (filters.userId) {
                queryParams.set('userId', filters.userId);
            }
            if (filters.projectId) {
                queryParams.set('projectId', filters.projectId);
            }
            if (filters.source) {
                queryParams.set('source', filters.source);
            }
            if (filters.since) {
                queryParams.set('since', filters.since.toISOString());
            }
            queryParams.set('limit', limit.toString());
            queryParams.set('offset', offset.toString());
            const response = await fetch(`${this.baseUrl}/context?${queryParams.toString()}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to get context items: ${response.statusText}`);
            }
            return await response.json();
        }
        catch (error) {
            console.error('Error getting context items:', error);
            return [];
        }
    }
    async getContextItem(id) {
        try {
            const response = await fetch(`${this.baseUrl}/context/${id}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (!response.ok) {
                if (response.status === 404) {
                    return null;
                }
                throw new Error(`Failed to get context item: ${response.statusText}`);
            }
            return await response.json();
        }
        catch (error) {
            console.error(`Error getting context item ${id}:`, error);
            return null;
        }
    }
    async createContextItem(item) {
        try {
            const response = await fetch(`${this.baseUrl}/context`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(item)
            });
            if (!response.ok) {
                throw new Error(`Failed to create context item: ${response.statusText}`);
            }
            return await response.json();
        }
        catch (error) {
            console.error('Error creating context item:', error);
            return null;
        }
    }
    async updateContextItem(id, updates) {
        try {
            const response = await fetch(`${this.baseUrl}/context/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(updates)
            });
            if (!response.ok) {
                if (response.status === 404) {
                    return null;
                }
                throw new Error(`Failed to update context item: ${response.statusText}`);
            }
            return await response.json();
        }
        catch (error) {
            console.error(`Error updating context item ${id}:`, error);
            return null;
        }
    }
    async deleteContextItem(id) {
        try {
            const response = await fetch(`${this.baseUrl}/context/${id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            return response.status === 204;
        }
        catch (error) {
            console.error(`Error deleting context item ${id}:`, error);
            return false;
        }
    }
    // Context Collection API
    async collectProgramContext(programId, projectId) {
        try {
            const queryParams = new URLSearchParams();
            if (projectId) {
                queryParams.set('projectId', projectId);
            }
            const response = await fetch(`${this.baseUrl}/collect/program/${programId}?${queryParams.toString()}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to collect program context: ${response.statusText}`);
            }
            const data = await response.json();
            return data.contextId;
        }
        catch (error) {
            console.error(`Error collecting program context for ${programId}:`, error);
            return null;
        }
    }
    async collectTagContext(tagId, projectId) {
        try {
            const queryParams = new URLSearchParams();
            if (projectId) {
                queryParams.set('projectId', projectId);
            }
            const response = await fetch(`${this.baseUrl}/collect/tag/${tagId}?${queryParams.toString()}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to collect tag context: ${response.statusText}`);
            }
            const data = await response.json();
            return data.contextId;
        }
        catch (error) {
            console.error(`Error collecting tag context for ${tagId}:`, error);
            return null;
        }
    }
    async collectRungContext(rungId, projectId) {
        try {
            const queryParams = new URLSearchParams();
            if (projectId) {
                queryParams.set('projectId', projectId);
            }
            const response = await fetch(`${this.baseUrl}/collect/rung/${rungId}?${queryParams.toString()}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to collect rung context: ${response.statusText}`);
            }
            const data = await response.json();
            return data.contextId;
        }
        catch (error) {
            console.error(`Error collecting rung context for ${rungId}:`, error);
            return null;
        }
    }
    async collectSafetyContext(programId, projectId) {
        try {
            const queryParams = new URLSearchParams();
            if (projectId) {
                queryParams.set('projectId', projectId);
            }
            const response = await fetch(`${this.baseUrl}/collect/safety/${programId}?${queryParams.toString()}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to collect safety context: ${response.statusText}`);
            }
            const data = await response.json();
            return data.contextId;
        }
        catch (error) {
            console.error(`Error collecting safety context for ${programId}:`, error);
            return null;
        }
    }
    async collectStandardContext(standard, projectId) {
        try {
            const queryParams = new URLSearchParams();
            if (projectId) {
                queryParams.set('projectId', projectId);
            }
            const response = await fetch(`${this.baseUrl}/collect/standard?${queryParams.toString()}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({ standard })
            });
            if (!response.ok) {
                throw new Error(`Failed to collect standard context: ${response.statusText}`);
            }
            const data = await response.json();
            return data.contextId;
        }
        catch (error) {
            console.error(`Error collecting standard context for ${standard}:`, error);
            return null;
        }
    }
    // Prompt Enhancement API
    async enhancePrompt(request) {
        try {
            const response = await fetch(`${this.baseUrl}/enhance`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(request)
            });
            if (!response.ok) {
                throw new Error(`Failed to enhance prompt: ${response.statusText}`);
            }
            return await response.json();
        }
        catch (error) {
            console.error('Error enhancing prompt:', error);
            // Return minimal enhancement if error occurs
            return {
                enhancedPrompt: `User Request: ${request.prompt}`,
                contextItems: [],
                confidence: 0.1,
                contextFingerprint: 'error'
            };
        }
    }
    // Confidence Scoring API
    async calculateConfidence(request) {
        try {
            const response = await fetch(`${this.baseUrl}/confidence`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify(request)
            });
            if (!response.ok) {
                throw new Error(`Failed to calculate confidence: ${response.statusText}`);
            }
            return await response.json();
        }
        catch (error) {
            console.error('Error calculating confidence:', error);
            // Return default confidence if error occurs
            return {
                confidence: 0.5,
                breakdown: {
                    completeness: 0.5,
                    freshness: 0.5,
                    relevance: 0.5,
                    quality: 0.5
                },
                suggestions: ['Add more context to improve confidence']
            };
        }
    }
    // Context Relationships API
    async getRelatedContextItems(contextId, relationshipType) {
        try {
            const queryParams = new URLSearchParams();
            if (relationshipType) {
                queryParams.set('relationshipType', relationshipType);
            }
            const response = await fetch(`${this.baseUrl}/context/${contextId}/related?${queryParams.toString()}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to get related context items: ${response.statusText}`);
            }
            return await response.json();
        }
        catch (error) {
            console.error(`Error getting related context items for ${contextId}:`, error);
            return [];
        }
    }
    async createContextRelationship(sourceId, targetId, relationshipType, strength, metadata) {
        try {
            const response = await fetch(`${this.baseUrl}/relationship`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    sourceId,
                    targetId,
                    relationshipType,
                    strength,
                    metadata
                })
            });
            return response.ok;
        }
        catch (error) {
            console.error('Error creating context relationship:', error);
            return false;
        }
    }
    // Helper methods
    getAuthToken() {
        // In a real application, this would get the auth token from localStorage or a state management store
        return localStorage.getItem('authToken') || 'mock-token-for-development';
    }
}
exports.mcpService = new MCPService();
