import { useEffect } from 'react';
import { useMCPContext } from '../context/MCPStore';
import { usePLCStore } from '../../store/plcStore';
import { mcpService } from '../services/mcpService';

export interface MCPOptions {
  autoCollect?: boolean;
  includeGlobalTags?: boolean;
  includeSafetyInfo?: boolean;
  includeStandards?: boolean;
}

export function useMCP(options: MCPOptions = {}) {
  const {
    autoCollect = true,
    includeGlobalTags = true,
    includeSafetyInfo = true,
    includeStandards = true
  } = options;
  
  const { 
    contextItems, 
    addContextItem, 
    removeContextItem, 
    clearContext,
    getContextByType,
    serializeForPrompt,
    getContextConfidence
  } = useMCPContext();
  
  const { currentProject, activeProgram } = usePLCStore();
  
  // Auto-collect context when project or program changes
  useEffect(() => {
    if (!autoCollect) return;
    
    if (currentProject && activeProgram) {
      const program = currentProject.programs.find(p => p.id === activeProgram);
      
      if (program) {
        // Add program context
        addContextItem({
          id: `program-${program.id}`,
          type: 'program',
          source: 'auto',
          value: `Program: ${program.name}, Type: ${program.type}`,
          timestamp: new Date().toISOString(),
          metadata: {
            programId: program.id,
            programName: program.name,
            programType: program.type
          }
        });
        
        // Add safety context if applicable
        if (includeSafetyInfo && program.safetyProgram) {
          addContextItem({
            id: `safety-${program.id}`,
            type: 'safety',
            source: 'auto',
            value: `Safety Program: ${program.name}, SIL Rating: ${program.silRating || 'Not specified'}`,
            timestamp: new Date().toISOString(),
            metadata: {
              programId: program.id,
              silRating: program.silRating
            }
          });
        }
        
        // Add standard context
        if (includeStandards) {
          addContextItem({
            id: 'standard-iec61131',
            type: 'standard',
            source: 'auto',
            value: 'IEC 61131-3 Standard for PLC Programming',
            timestamp: new Date().toISOString()
          });
        }
        
        // In a real implementation, we would also call the backend API
        // to collect and store context items
        if (program.id) {
          mcpService.collectProgramContext(program.id, currentProject.id)
            .then(contextId => {
              console.log('Program context collected:', contextId);
            })
            .catch(error => {
              console.error('Failed to collect program context:', error);
            });
            
          if (includeSafetyInfo && program.safetyProgram) {
            mcpService.collectSafetyContext(program.id, currentProject.id)
              .then(contextId => {
                console.log('Safety context collected:', contextId);
              })
              .catch(error => {
                console.error('Failed to collect safety context:', error);
              });
          }
        }
      }
    }
    
    return () => {
      // Clean up auto-collected program context when component unmounts
      const programItems = contextItems.filter(
        item => item.type === 'program' && item.source === 'auto'
      );
      programItems.forEach(item => removeContextItem(item.id));
    };
  }, [currentProject, activeProgram, autoCollect, includeStandards, includeSafetyInfo, addContextItem, removeContextItem, contextItems]);
  
  // Add global tags to context
  useEffect(() => {
    if (!autoCollect || !includeGlobalTags) return;
    
    if (currentProject) {
      // Clear existing auto-collected tag context
      contextItems
        .filter(item => item.type === 'tag' && item.source === 'auto')
        .forEach(item => removeContextItem(item.id));
      
      // Add global tags (limit to 10 most relevant)
      currentProject.globalTags.slice(0, 10).forEach(tag => {
        addContextItem({
          id: `tag-${tag.id}`,
          type: 'tag',
          source: 'auto',
          value: `${tag.name} (${tag.type}): ${tag.description || 'No description'}`,
          timestamp: new Date().toISOString(),
          metadata: {
            tagId: tag.id,
            tagName: tag.name,
            tagType: tag.type,
            tagScope: tag.scope
          }
        });
        
        // In a real implementation, we would also call the backend API
        if (tag.id) {
          mcpService.collectTagContext(tag.id, currentProject.id)
            .catch(error => {
              console.error('Failed to collect tag context:', error);
            });
        }
      });
    }
    
    return () => {
      // Clean up auto-collected tag context when component unmounts
      const tagItems = contextItems.filter(
        item => item.type === 'tag' && item.source === 'auto'
      );
      tagItems.forEach(item => removeContextItem(item.id));
    };
  }, [currentProject, autoCollect, includeGlobalTags, addContextItem, removeContextItem, contextItems]);
  
  // Helper functions for manually adding specific context
  const addProgramContext = (programName: string, programType: string, metadata?: any) => {
    addContextItem({
      id: `program-manual-${Date.now()}`,
      type: 'program',
      source: 'user',
      value: `Program: ${programName}, Type: ${programType}`,
      timestamp: new Date().toISOString(),
      metadata
    });
  };
  
  const addTagContext = (tag: any) => {
    addContextItem({
      id: `tag-manual-${tag.id || Date.now()}`,
      type: 'tag',
      source: 'user',
      value: `${tag.name} (${tag.type}): ${tag.description || 'No description'}`,
      timestamp: new Date().toISOString(),
      metadata: {
        tagId: tag.id,
        tagName: tag.name,
        tagType: tag.type,
        tagScope: tag.scope
      }
    });
  };
  
  const addRungContext = (rungNumber: number, rungComment?: string) => {
    addContextItem({
      id: `rung-manual-${Date.now()}`,
      type: 'rung',
      source: 'user',
      value: `Rung ${rungNumber}${rungComment ? `: ${rungComment}` : ''}`,
      timestamp: new Date().toISOString(),
      metadata: {
        rungNumber
      }
    });
  };
  
  const addSafetyContext = (safetyInfo: string, silRating?: string) => {
    addContextItem({
      id: `safety-manual-${Date.now()}`,
      type: 'safety',
      source: 'user',
      value: safetyInfo,
      timestamp: new Date().toISOString(),
      metadata: {
        silRating
      }
    });
  };
  
  const addStandardContext = (standard: string) => {
    addContextItem({
      id: `standard-manual-${Date.now()}`,
      type: 'standard',
      source: 'user',
      value: standard,
      timestamp: new Date().toISOString()
    });
  };
  
  // Function to enhance a prompt with context
  const enhancePrompt = async (prompt: string, contextTypes?: string[]) => {
    try {
      const response = await mcpService.enhancePrompt({
        prompt,
        contextTypes: contextTypes as any[]
      });
      
      return response.enhancedPrompt;
    } catch (error) {
      console.error('Error enhancing prompt:', error);
      return prompt;
    }
  };
  
  return {
    // Core context operations
    contextItems,
    addContextItem,
    removeContextItem,
    clearContext,
    getContextByType,
    serializeForPrompt,
    getContextConfidence,
    enhancePrompt,
    
    // Helper functions for specific context types
    addProgramContext,
    addTagContext,
    addRungContext,
    addSafetyContext,
    addStandardContext
  };
}