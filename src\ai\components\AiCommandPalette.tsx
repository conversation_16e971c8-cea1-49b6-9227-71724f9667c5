import React, { useState, useEffect } from 'react';
import { <PERSON>, Sparkles, Zap, Code, Lightbulb, Bug, Wrench } from 'lucide-react';
import { useAI } from '../hooks/useAI';
import { promptTemplates } from '../services/promptTemplates';

interface AiCommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  context?: any;
}

const AiCommandPalette: React.FC<AiCommandPaletteProps> = ({ isOpen, onClose, context }) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const { generateCode, explainCode, suggestRefactor, debugLogic, isLoading } = useAI();

  const aiCommands = [
    {
      id: 'generate-motor-control',
      title: 'Generate Motor Control Logic',
      description: 'Create start/stop motor control with safety interlocks',
      icon: <Zap className="w-4 h-4" />,
      category: 'Generate',
      action: () => generateCode('motor control with start/stop buttons and emergency stop', 'ladder')
    },
    {
      id: 'generate-safety-logic',
      title: 'Generate Safety Logic',
      description: 'Create safety interlock logic with light curtains',
      icon: <Sparkles className="w-4 h-4" />,
      category: 'Generate',
      action: () => generateCode('safety logic with light curtains and emergency stops', 'ladder')
    },
    {
      id: 'explain-current-code',
      title: 'Explain Current Code',
      description: 'Get detailed explanation of selected code',
      icon: <Lightbulb className="w-4 h-4" />,
      category: 'Explain',
      action: () => explainCode(context?.selectedCode || '', context?.language || 'ladder')
    },
    {
      id: 'refactor-code',
      title: 'Suggest Code Improvements',
      description: 'Get optimization and safety suggestions',
      icon: <Wrench className="w-4 h-4" />,
      category: 'Refactor',
      action: () => suggestRefactor(context?.selectedCode || '', context?.language || 'ladder')
    },
    {
      id: 'debug-logic',
      title: 'Debug Logic Issues',
      description: 'Help troubleshoot logic problems',
      icon: <Bug className="w-4 h-4" />,
      category: 'Debug',
      action: () => debugLogic(context?.elements || [], 'logic not working as expected')
    },
    {
      id: 'generate-timer-logic',
      title: 'Generate Timer Logic',
      description: 'Create timer-based sequential control',
      icon: <Code className="w-4 h-4" />,
      category: 'Generate',
      action: () => generateCode('timer-based sequential control logic', 'ladder')
    },
    {
      id: 'generate-analog-control',
      title: 'Generate Analog Control',
      description: 'Create analog input/output control logic',
      icon: <Code className="w-4 h-4" />,
      category: 'Generate',
      action: () => generateCode('analog control with scaling and alarms', 'st')
    }
  ];

  const snippets = promptTemplates.getSnippets();

  const filteredCommands = aiCommands.filter(cmd =>
    cmd.title.toLowerCase().includes(query.toLowerCase()) ||
    cmd.description.toLowerCase().includes(query.toLowerCase()) ||
    cmd.category.toLowerCase().includes(query.toLowerCase())
  );

  useEffect(() => {
    setSelectedIndex(0);
  }, [query]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            handleCommandSelect(filteredCommands[selectedIndex]);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, filteredCommands, onClose]);

  const handleCommandSelect = async (command: any) => {
    try {
      await command.action();
      onClose();
    } catch (error) {
      console.error('AI command failed:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-32">
      <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-2xl w-full max-w-2xl">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <Sparkles className="w-5 h-5 text-purple-400" />
            <input
              type="text"
              placeholder="Ask AI to generate, explain, or improve your code..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="flex-1 bg-transparent text-white outline-none text-lg placeholder-gray-400"
              autoFocus
            />
            {isLoading && (
              <div className="w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full animate-spin"></div>
            )}
          </div>
        </div>
        
        <div className="max-h-96 overflow-y-auto">
          {filteredCommands.length === 0 ? (
            <div className="p-8 text-center text-gray-400">
              <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No AI commands found</p>
              <p className="text-sm">Try "generate motor control" or "explain code"</p>
            </div>
          ) : (
            <div className="p-2">
              {filteredCommands.reduce((acc, cmd, index) => {
                const isNewCategory = index === 0 || cmd.category !== filteredCommands[index - 1].category;
                
                if (isNewCategory) {
                  acc.push(
                    <div key={`category-${cmd.category}`} className="px-3 py-2 text-xs font-semibold text-gray-400 sticky top-0 bg-gray-900">
                      {cmd.category}
                    </div>
                  );
                }
                
                acc.push(
                  <div
                    key={cmd.id}
                    className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                      index === selectedIndex ? 'bg-purple-600/20 border border-purple-500/30' : 'hover:bg-gray-800/50'
                    }`}
                    onClick={() => handleCommandSelect(cmd)}
                  >
                    <span className="text-purple-400">{cmd.icon}</span>
                    <div className="flex-1">
                      <div className="text-white font-medium">{cmd.title}</div>
                      <div className="text-sm text-gray-400">{cmd.description}</div>
                    </div>
                    <div className="text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded font-mono">
                      AI
                    </div>
                  </div>
                );
                
                return acc;
              }, [] as React.ReactNode[])}
            </div>
          )}
        </div>

        {/* Quick Snippets */}
        {query === '' && (
          <div className="border-t border-gray-700 p-4">
            <div className="text-sm font-semibold text-gray-400 mb-3">Quick Snippets</div>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(snippets).slice(0, 4).map(([name, snippet]) => (
                <button
                  key={name}
                  onClick={() => setQuery(snippet.prompt)}
                  className="text-left p-2 bg-gray-800/50 hover:bg-gray-800 border border-gray-700 rounded transition-colors"
                >
                  <div className="text-sm text-white font-medium">{name}</div>
                  <div className="text-xs text-gray-400 mt-1">{snippet.description}</div>
                </button>
              ))}
            </div>
          </div>
        )}
        
        <div className="p-3 border-t border-gray-700 text-xs text-gray-400 flex items-center justify-between">
          <div className="space-x-4">
            <span>↑↓ Navigate</span>
            <span>↵ Execute</span>
            <span>ESC Close</span>
          </div>
          <div className="flex items-center space-x-1">
            <span>Powered by</span>
            <Sparkles className="w-3 h-3" />
            <span>AI</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AiCommandPalette;