# LUREON AI Backend Setup Guide

This guide provides detailed instructions for setting up the LUREON AI backend services for local development and production deployment.

## Overview

The LUREON AI backend consists of several components:

- **AI Backend Service**: Node.js Express server that handles AI requests
- **Redis**: For caching and job queue management
- **PostgreSQL**: For storing audit logs, feedback, and usage metrics
- **Nginx**: Reverse proxy for routing and security

## Local Development Setup

### Prerequisites

- Node.js 18+ installed
- Docker and Docker Compose installed
- OpenAI API key and/or Claude API key

### Option 1: Direct Node.js Setup

1. **Set up environment variables**

   Create a `.env` file in the project root:

   ```env
   # AI Provider API Keys
   VITE_OPENAI_API_KEY=your_openai_key
   VITE_CLAUDE_API_KEY=your_claude_key
   
   # Server Configuration
   PORT=3001
   FRONTEND_URL=http://localhost:5173
   
   # Optional: Local Ollama Configuration
   OLLAMA_HOST=http://localhost:11434
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start the AI backend server**

   ```bash
   npm run ai-server
   ```

4. **Start the frontend with AI backend**

   ```bash
   npm run dev:full
   ```

### Option 2: Docker Setup

1. **Set up environment variables**

   Create a `.env` file in the `src/ai/backend` directory:

   ```env
   # AI Provider API Keys
   OPENAI_API_KEY=your_openai_key
   CLAUDE_API_KEY=your_claude_key
   
   # Database Configuration
   POSTGRES_PASSWORD=your_secure_password
   
   # Server Configuration
   PORT=3001
   FRONTEND_URL=http://localhost:5173
   ```

2. **Start the Docker containers**

   ```bash
   cd src/ai/backend
   docker-compose up -d
   ```

3. **Verify services are running**

   ```bash
   docker-compose ps
   ```

   You should see the following services running:
   - ai-backend
   - redis
   - postgres
   - nginx

4. **Access the API**

   The AI backend API will be available at `http://localhost:3001/api/ai`

## Production Deployment

### Docker Swarm or Kubernetes

For production environments, we recommend using Docker Swarm or Kubernetes.

#### Docker Swarm Deployment

1. **Initialize Docker Swarm**

   ```bash
   docker swarm init
   ```

2. **Deploy the stack**

   ```bash
   docker stack deploy -c docker-compose.prod.yml lureon-ai
   ```

#### Kubernetes Deployment

1. **Apply Kubernetes manifests**

   ```bash
   kubectl apply -f k8s/ai-backend/
   ```

2. **Verify deployment**

   ```bash
   kubectl get pods -n lureon
   ```

### Environment Configuration

For production, set these additional environment variables:

```env
# Environment
NODE_ENV=production

# Security
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=your_encryption_key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info
```

## Scaling Considerations

### Horizontal Scaling

The AI backend is designed to scale horizontally. You can increase the number of replicas:

```bash
# Docker Swarm
docker service scale lureon-ai_backend=3

# Kubernetes
kubectl scale deployment ai-backend -n lureon --replicas=3
```

### Redis Configuration

For production, configure Redis with persistence and replication:

```yaml
redis:
  image: redis:7-alpine
  command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
  volumes:
    - ./redis.conf:/usr/local/etc/redis/redis.conf
    - redis_data:/data
```

### Database Scaling

For high-availability PostgreSQL:

1. Set up a primary-replica configuration
2. Implement connection pooling with PgBouncer
3. Configure regular backups

## Security Best Practices

1. **API Keys**: Never commit API keys to version control
2. **Network Security**: Use internal networks for service communication
3. **Rate Limiting**: Configure appropriate rate limits
4. **Authentication**: Implement proper JWT authentication
5. **Encryption**: Enable TLS for all services

## Monitoring and Logging

1. **Health Checks**: Access `/health` endpoint for service status
2. **Logs**: Configure centralized logging with ELK or similar
3. **Metrics**: Set up Prometheus and Grafana for monitoring

## Troubleshooting

### Common Issues

**Connection Refused**:
- Check if the service is running: `docker-compose ps`
- Verify port mappings: `docker-compose port ai-backend 3001`

**Authentication Errors**:
- Verify API keys are correctly set in environment variables
- Check JWT configuration

**Performance Issues**:
- Monitor Redis memory usage
- Check database connection pool settings
- Adjust rate limits if necessary

### Logs

Access logs for debugging:

```bash
# Docker logs
docker-compose logs -f ai-backend

# Kubernetes logs
kubectl logs -f deployment/ai-backend -n lureon
```

## Advanced Configuration

### Custom Model Integration

To integrate custom models:

1. Modify `src/ai/backend/llmClient.ts`
2. Add your model provider configuration
3. Update the model routing logic

### Implementing Fine-tuned Models

1. Train your model using the fine-tuning pipeline
2. Update the model registry in `model.json`
3. Configure the backend to use your fine-tuned model

## Backup and Recovery

### Database Backups

```bash
# Manual backup
docker exec -t lureon-ai_postgres_1 pg_dump -U postgres lureon_ai > backup.sql

# Restore from backup
cat backup.sql | docker exec -i lureon-ai_postgres_1 psql -U postgres lureon_ai
```

### Automated Backups

Configure cron jobs for regular backups:

```bash
0 2 * * * docker exec lureon-ai_postgres_1 pg_dump -U postgres lureon_ai | gzip > /backups/lureon_ai_$(date +\%Y\%m\%d).sql.gz
```

## Support and Resources

- GitHub Repository: [github.com/lureon/ide](https://github.com/lureon/ide)
- Documentation: [docs.lureon.com](https://docs.lureon.com)
- Community Forum: [community.lureon.com](https://community.lureon.com)
- Email Support: <EMAIL>