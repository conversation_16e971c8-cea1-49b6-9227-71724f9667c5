const { monitoringService } = require('../monitoring');

class AlertManager {
    thresholds: {
        responseTime: number;
        errorRate: number;
        cpuUsage: number;
        memoryUsage: number;
    };

    constructor() {
        this.thresholds = {
            responseTime: 5000, // 5 seconds
            errorRate: 0.05,    // 5%
            cpuUsage: 80,       // 80%
            memoryUsage: 90,    // 90%
        };
    }

    async checkAlerts() {
        const metrics = await monitoringService.getPerformanceMetrics();

        // Check response time
        if (metrics.averageResponseTime > this.thresholds.responseTime) {
            await this.sendAlert('HIGH_RESPONSE_TIME', {
                current: metrics.averageResponseTime,
                threshold: this.thresholds.responseTime
            });
        }

        // Check error rate
        if (metrics.errorRate > this.thresholds.errorRate) {
            await this.sendAlert('HIGH_ERROR_RATE', {
                current: metrics.errorRate,
                threshold: this.thresholds.errorRate
            });
        }

        // Check system resources
        const systemMetrics = process.memoryUsage();
        const memoryUsage = (systemMetrics.heapUsed / systemMetrics.heapTotal) * 100;

        if (memoryUsage > this.thresholds.memoryUsage) {
            await this.sendAlert('HIGH_MEMORY_USAGE', {
                current: memoryUsage,
                threshold: this.thresholds.memoryUsage
            });
        }
    }

    async sendAlert(type: string, data: any) {
        console.error(`🚨 ALERT: ${type}`, data);

        // In production, integrate with:
        // - Slack webhooks
        // - Email notifications
        // - PagerDuty
        // - SMS alerts
    }
}

// Run alerts check every minute
const alertManager = new AlertManager();
setInterval(() => {
    alertManager.checkAlerts().catch(console.error);
}, 60000);

module.exports = { AlertManager };