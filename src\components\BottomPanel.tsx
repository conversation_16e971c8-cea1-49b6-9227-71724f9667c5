import React, { useState, useEffect } from 'react';
import { 
  Terminal, 
  Activity, 
  MessageSquare, 
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Clock,
  Cpu,
  Play,
  Square,
  RotateCcw,
  Download,
  Upload,
  Settings,
  Trash2,
  Copy,
  Save,
  Filter,
  Search
} from 'lucide-react';
import { usePLCStore } from '../store/plcStore';
import AIAssistant from './ai/AIAssistant';

const BottomPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState('console');
  const [isExpanded, setIsExpanded] = useState(true);
  const [consoleFilter, setConsoleFilter] = useState('all');
  const [consoleSearch, setConsoleSearch] = useState('');
  const { currentProject, simulationMode, toggleSimulation } = usePLCStore();
  
  const [consoleMessages, setConsoleMessages] = useState([
    { id: 1, time: '14:32:15', level: 'info', message: 'PLC connection established - Siemens S7-1500 CPU 1511-1 PN', component: 'plc-driver' },
    { id: 2, time: '14:32:16', level: 'success', message: 'Program download completed successfully', component: 'deployment' },
    { id: 3, time: '14:32:18', level: 'warning', message: 'Tag "Pressure_Sensor" not connected to physical input', component: 'io-mapping' },
    { id: 4, time: '14:32:20', level: 'info', message: 'Scan cycle: 2.3ms average, 3.1ms maximum', component: 'plc-runtime' },
  ]);

  // Add simulation messages when simulation mode changes
  useEffect(() => {
    if (simulationMode) {
      const newMessage = {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'info' as const,
        message: 'Simulation mode activated - Virtual PLC running',
        component: 'simulator'
      };
      setConsoleMessages(prev => [...prev, newMessage]);
    } else {
      const newMessage = {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        level: 'warning' as const,
        message: 'Simulation mode deactivated',
        component: 'simulator'
      };
      setConsoleMessages(prev => [...prev, newMessage]);
    }
  }, [simulationMode]);

  const tabs = [
    { id: 'console', name: 'PLC Console', icon: Terminal },
    { id: 'diagnostics', name: 'Diagnostics', icon: Activity },
    { id: 'ai-chat', name: 'AI Chat', icon: MessageSquare },
  ];

  const target = currentProject?.targets[0];
  const diagnosticData = [
    { name: 'CPU Load', value: simulationMode ? '5%' : (target?.cpuLoad ? `${target.cpuLoad}%` : 'N/A'), status: 'good', icon: Cpu },
    { name: 'Memory Usage', value: '34%', status: 'good', icon: Activity },
    { name: 'I/O Status', value: simulationMode ? '4/4 SIM' : '15/16 OK', status: simulationMode ? 'good' : 'warning', icon: Activity },
    { name: 'Network Status', value: simulationMode ? 'Simulated' : (target?.connected ? 'Connected' : 'Disconnected'), status: simulationMode || target?.connected ? 'good' : 'error', icon: Activity },
    { name: 'Scan Time', value: simulationMode ? '0.1ms' : (target?.scanTime ? `${target.scanTime}ms` : 'N/A'), status: 'good', icon: Clock },
  ];

  const getStatusColor = (level: string) => {
    switch (level) {
      case 'error': return 'text-error';
      case 'warning': return 'text-primary';
      case 'success': return 'text-success';
      case 'info': return 'text-accent';
      default: return 'text-control-400';
    }
  };

  const getStatusIcon = (level: string) => {
    switch (level) {
      case 'error': return <XCircle className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'success': return <CheckCircle2 className="w-4 h-4" />;
      case 'info': return <Terminal className="w-4 h-4" />;
      default: return <Terminal className="w-4 h-4" />;
    }
  };

  const filteredConsoleMessages = consoleMessages.filter(msg => {
    const matchesFilter = consoleFilter === 'all' || msg.level === consoleFilter;
    const matchesSearch = consoleSearch === '' || 
                         msg.message.toLowerCase().includes(consoleSearch.toLowerCase()) ||
                         msg.component.toLowerCase().includes(consoleSearch.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const handleClearConsole = () => {
    if (confirm('Clear all console messages?')) {
      setConsoleMessages([]);
    }
  };

  const handleCopyConsole = () => {
    const text = filteredConsoleMessages.map(msg => 
      `[${msg.time}] [${msg.level.toUpperCase()}] ${msg.message}`
    ).join('\n');
    
    navigator.clipboard.writeText(text);
    alert('Console messages copied to clipboard');
  };

  const handleSaveConsole = () => {
    const text = filteredConsoleMessages.map(msg => 
      `[${msg.time}] [${msg.level.toUpperCase()}] [${msg.component}] ${msg.message}`
    ).join('\n');
    
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `plc-console-log-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderConsole = () => (
    <div className="h-full flex flex-col">
      <div className="p-3 border-b border-control-600 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <select
            value={consoleFilter}
            onChange={(e) => setConsoleFilter(e.target.value)}
            className="bg-control-800 border border-control-600 rounded px-2 py-1 text-sm text-neutral"
          >
            <option value="all">All Levels</option>
            <option value="info">Info</option>
            <option value="success">Success</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
          </select>
          
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-control-400" />
            <input
              type="text"
              placeholder="Search messages..."
              value={consoleSearch}
              onChange={(e) => setConsoleSearch(e.target.value)}
              className="bg-control-800 border border-control-600 rounded pl-7 pr-2 py-1 text-sm text-neutral w-48"
            />
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleClearConsole}
            className="p-1 text-control-400 hover:text-error hover:bg-control-800 rounded transition-colors"
            title="Clear Console"
          >
            <Trash2 className="w-4 h-4" />
          </button>
          <button
            onClick={handleCopyConsole}
            className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            title="Copy to Clipboard"
          >
            <Copy className="w-4 h-4" />
          </button>
          <button
            onClick={handleSaveConsole}
            className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            title="Save to File"
          >
            <Save className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4 font-mono text-sm space-y-1">
        {filteredConsoleMessages.length === 0 ? (
          <div className="text-center py-8 text-control-400">
            <Terminal className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No console messages</p>
          </div>
        ) : (
          filteredConsoleMessages.map((msg) => (
            <div key={msg.id} className="flex items-start space-x-3">
              <span className="text-control-500 text-xs">{msg.time}</span>
              <span className={getStatusColor(msg.level)}>
                {getStatusIcon(msg.level)}
              </span>
              <span className={getStatusColor(msg.level)}>{msg.message}</span>
              <span className="text-control-500 text-xs ml-auto">{msg.component}</span>
            </div>
          ))
        )}
      </div>
      <div className="border-t border-control-600 p-4">
        <div className="flex items-center space-x-2">
          <span className="text-control-400">{'>'}</span>
          <input 
            type="text" 
            placeholder="Enter PLC command..."
            className="flex-1 bg-transparent text-neutral outline-none font-mono text-sm"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                const input = e.currentTarget.value;
                if (input.trim()) {
                  const newMessage = {
                    id: Date.now(),
                    time: new Date().toLocaleTimeString(),
                    level: 'info' as const,
                    message: `Command executed: ${input}`,
                    component: 'console'
                  };
                  setConsoleMessages(prev => [...prev, newMessage]);
                  e.currentTarget.value = '';
                }
              }
            }}
          />
          <button
            className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            title="Run Command"
          >
            <Play className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );

  const renderDiagnostics = () => (
    <div className="h-full p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-neutral">System Diagnostics</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleSimulation}
            className={`flex items-center space-x-2 px-3 py-1 rounded text-sm transition-colors ${
              simulationMode
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {simulationMode ? <Square className="w-3 h-3" /> : <Play className="w-3 h-3" />}
            <span>{simulationMode ? 'Stop' : 'Start'} Simulation</span>
          </button>
          <button
            className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
            title="Reset Diagnostics"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-5 gap-4 mb-6">
        {diagnosticData.map((item, index) => {
          const IconComponent = item.icon;
          return (
            <div key={index} className="bg-control-800/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <IconComponent className="w-5 h-5 text-control-400" />
                <span className={`w-2 h-2 rounded-full ${
                  item.status === 'good' ? 'bg-success' :
                  item.status === 'warning' ? 'bg-primary' :
                  'bg-error'
                }`}></span>
              </div>
              <div className="text-lg font-semibold text-neutral">{item.value}</div>
              <div className="text-xs text-control-400">{item.name}</div>
            </div>
          );
        })}
      </div>
      
      <div className="bg-control-800/50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-neutral">Active Tags Monitor</h3>
          <div className="flex items-center space-x-2">
            <button
              className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
              title="Refresh Tags"
            >
              <RotateCcw className="w-3 h-3" />
            </button>
            <button
              className="p-1 text-control-400 hover:text-neutral hover:bg-control-800 rounded transition-colors"
              title="Filter Tags"
            >
              <Filter className="w-3 h-3" />
            </button>
          </div>
        </div>
        <div className="space-y-2">
          {currentProject?.globalTags.slice(0, 4).map((tag, index) => (
            <div key={index} className="flex items-center justify-between text-sm">
              <span className="text-control-300">{tag.name}</span>
              <div className="flex items-center space-x-3">
                <span className={`font-mono text-sm ${
                  tag.type === 'BOOL' 
                    ? (tag.value ? 'text-success' : 'text-control-400')
                    : 'text-accent'
                }`}>
                  {tag.type === 'BOOL' ? (tag.value ? 'TRUE' : 'FALSE') : tag.value.toString()}
                </span>
                <span className="text-xs text-control-500">
                  {tag.lastUpdated?.toLocaleTimeString() || 'N/A'}
                </span>
              </div>
            </div>
          )) || []}
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4 mt-4">
        <div className="bg-control-800/50 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-neutral mb-3">PLC Control</h3>
          <div className="flex items-center space-x-2">
            <button
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm transition-colors"
            >
              <Play className="w-3 h-3" />
              <span>Start PLC</span>
            </button>
            <button
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm transition-colors"
            >
              <Square className="w-3 h-3" />
              <span>Stop PLC</span>
            </button>
            <button
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors"
            >
              <Settings className="w-3 h-3" />
              <span>Configure</span>
            </button>
          </div>
        </div>
        
        <div className="bg-control-800/50 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-neutral mb-3">Program Transfer</h3>
          <div className="flex items-center space-x-2">
            <button
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm transition-colors"
            >
              <Download className="w-3 h-3" />
              <span>Download to PLC</span>
            </button>
            <button
              className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded text-sm transition-colors"
            >
              <Upload className="w-3 h-3" />
              <span>Upload from PLC</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  if (!isExpanded) {
    return (
      <div className="h-8 bg-base-dark border-t border-control-600 flex items-center justify-between px-4">
        <div className="flex items-center space-x-4 text-xs text-control-400">
          <span>PLC: {simulationMode ? 'Simulated' : (target?.connected ? 'Connected' : 'Disconnected')}</span>
          <span>Scan: {simulationMode ? '0.1ms' : (target?.scanTime ? `${target.scanTime}ms` : 'N/A')}</span>
          <span>CPU: {simulationMode ? '5%' : (target?.cpuLoad ? `${target.cpuLoad}%` : 'N/A')}</span>
        </div>
        <button 
          onClick={() => setIsExpanded(true)}
          className="text-control-400 hover:text-neutral"
        >
          ▲
        </button>
      </div>
    );
  }

  return (
    <div className="h-80 bg-base-dark border-t border-control-600 flex flex-col">
      <div className="bg-control-800 border-b border-control-600 flex items-center justify-between">
        <div className="flex">
          {tabs.map(tab => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                className={`flex items-center px-4 py-2 text-sm transition-colors ${
                  activeTab === tab.id 
                    ? 'bg-base-dark text-neutral border-b-2 border-accent' 
                    : 'text-control-400 hover:text-neutral hover:bg-control-700'
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                <IconComponent className="w-4 h-4 mr-2" />
                {tab.name}
              </button>
            );
          })}
        </div>
        <button 
          onClick={() => setIsExpanded(false)}
          className="p-2 text-control-400 hover:text-neutral"
        >
          ▼
        </button>
      </div>
      
      <div className="flex-1">
        {activeTab === 'console' && renderConsole()}
        {activeTab === 'diagnostics' && renderDiagnostics()}
        {activeTab === 'ai-chat' && <AIAssistant />}
      </div>
    </div>
  );
};

export default BottomPanel;