import React, { useState, useEffect } from 'react';
import { AuditLog } from '../../types/enterprise';
import { enterpriseServices } from '../../services/enterpriseServices';
import { 
  FileText, 
  Search, 
  Filter, 
  Download, 
  Calendar,
  User,
  Activity,
  AlertTriangle,
  CheckCircle2,
  Info,
  XCircle,
  Clock
} from 'lucide-react';

const AuditTrail: React.FC = () => {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<AuditLog[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterUser, setFilterUser] = useState<string>('all');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);

  useEffect(() => {
    loadAuditLogs();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [auditLogs, searchQuery, filterUser, filterAction, filterSeverity, dateRange]);

  const loadAuditLogs = async () => {
    // Mock audit logs for demonstration
    const mockLogs: AuditLog[] = [
      {
        id: 'audit-1',
        timestamp: new Date(Date.now() - 1000 * 60 * 30),
        userId: 'user-1',
        action: {
          type: 'update',
          category: 'program',
          description: 'Modified ladder logic in Main_Program',
          severity: 'medium'
        },
        resource: 'program',
        resourceId: 'main-program',
        oldValue: { rung: 'original logic' },
        newValue: { rung: 'updated logic' },
        ipAddress: '*************',
        sessionId: 'session-123'
      },
      {
        id: 'audit-2',
        timestamp: new Date(Date.now() - 1000 * 60 * 60),
        userId: 'user-2',
        action: {
          type: 'deploy',
          category: 'project',
          description: 'Deployed project to PLC target',
          severity: 'critical'
        },
        resource: 'project',
        resourceId: 'project-1',
        ipAddress: '*************',
        sessionId: 'session-124',
        compliance: {
          standard: 'FDA_CFR_21_Part_11',
          requiresSignature: true,
          retentionPeriod: 2555, // 7 years
          classification: 'GxP Critical'
        }
      },
      {
        id: 'audit-3',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
        userId: 'user-1',
        action: {
          type: 'create',
          category: 'tag',
          description: 'Created new safety tag Emergency_Stop_2',
          severity: 'high'
        },
        resource: 'tag',
        resourceId: 'tag-emergency-2',
        newValue: { name: 'Emergency_Stop_2', type: 'BOOL', safetyRated: true },
        ipAddress: '*************',
        sessionId: 'session-125'
      },
      {
        id: 'audit-4',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),
        userId: 'user-3',
        action: {
          type: 'delete',
          category: 'program',
          description: 'Deleted test program Test_Logic_1',
          severity: 'high'
        },
        resource: 'program',
        resourceId: 'test-program-1',
        oldValue: { name: 'Test_Logic_1', type: 'ladder' },
        ipAddress: '*************',
        sessionId: 'session-126'
      }
    ];
    
    setAuditLogs(mockLogs);
  };

  const applyFilters = () => {
    let filtered = auditLogs;

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(log =>
        log.action.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.resource.toLowerCase().includes(searchQuery.toLowerCase()) ||
        log.resourceId.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // User filter
    if (filterUser !== 'all') {
      filtered = filtered.filter(log => log.userId === filterUser);
    }

    // Action filter
    if (filterAction !== 'all') {
      filtered = filtered.filter(log => log.action.type === filterAction);
    }

    // Severity filter
    if (filterSeverity !== 'all') {
      filtered = filtered.filter(log => log.action.severity === filterSeverity);
    }

    // Date range filter
    if (dateRange.start) {
      filtered = filtered.filter(log => log.timestamp >= new Date(dateRange.start));
    }
    if (dateRange.end) {
      filtered = filtered.filter(log => log.timestamp <= new Date(dateRange.end));
    }

    setFilteredLogs(filtered);
  };

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'create': return <CheckCircle2 className="w-4 h-4 text-green-400" />;
      case 'update': return <Info className="w-4 h-4 text-blue-400" />;
      case 'delete': return <XCircle className="w-4 h-4 text-red-400" />;
      case 'deploy': return <Activity className="w-4 h-4 text-purple-400" />;
      default: return <Info className="w-4 h-4 text-gray-400" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-400 bg-red-400/20';
      case 'high': return 'text-orange-400 bg-orange-400/20';
      case 'medium': return 'text-yellow-400 bg-yellow-400/20';
      case 'low': return 'text-green-400 bg-green-400/20';
      default: return 'text-gray-400 bg-gray-400/20';
    }
  };

  const exportAuditReport = async () => {
    const report = await enterpriseServices.audit.generateComplianceReport(
      'FDA_CFR_21_Part_11',
      {
        start: dateRange.start ? new Date(dateRange.start) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: dateRange.end ? new Date(dateRange.end) : new Date()
      }
    );
    
    // Create and download report
    const blob = new Blob([report], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `audit-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderLogEntry = (log: AuditLog) => (
    <div
      key={log.id}
      className={`bg-gray-800 rounded-lg border border-gray-700 p-4 cursor-pointer transition-all hover:border-blue-500 ${
        selectedLog?.id === log.id ? 'border-blue-500 bg-blue-500/10' : ''
      }`}
      onClick={() => setSelectedLog(log)}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-3">
          {getActionIcon(log.action.type)}
          <div>
            <h3 className="text-white font-medium">{log.action.description}</h3>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <User className="w-3 h-3" />
              <span>{log.userId}</span>
              <Clock className="w-3 h-3 ml-2" />
              <span>{log.timestamp.toLocaleString()}</span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 rounded text-xs ${getSeverityColor(log.action.severity)}`}>
            {log.action.severity}
          </span>
          {log.compliance && (
            <span className="px-2 py-1 rounded text-xs bg-purple-400/20 text-purple-400">
              {log.compliance.standard}
            </span>
          )}
        </div>
      </div>
      
      <div className="text-sm text-gray-400">
        <span className="capitalize">{log.action.type}</span> • {log.resource} • {log.resourceId}
      </div>
    </div>
  );

  const renderLogDetails = () => {
    if (!selectedLog) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400">
          <div className="text-center">
            <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p>Select an audit log to view details</p>
          </div>
        </div>
      );
    }

    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-white">Audit Log Details</h3>
          <span className={`px-3 py-1 rounded ${getSeverityColor(selectedLog.action.severity)}`}>
            {selectedLog.action.severity.toUpperCase()}
          </span>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-semibold mb-3">Action Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-400 mb-1">Action Type</label>
              <div className="flex items-center space-x-2">
                {getActionIcon(selectedLog.action.type)}
                <span className="text-white capitalize">{selectedLog.action.type}</span>
              </div>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Category</label>
              <span className="text-white capitalize">{selectedLog.action.category}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Resource</label>
              <span className="text-white">{selectedLog.resource}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Resource ID</label>
              <span className="text-white font-mono">{selectedLog.resourceId}</span>
            </div>
          </div>
          <div className="mt-4">
            <label className="block text-sm text-gray-400 mb-1">Description</label>
            <p className="text-white">{selectedLog.action.description}</p>
          </div>
        </div>

        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-semibold mb-3">Session Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-400 mb-1">User ID</label>
              <span className="text-white">{selectedLog.userId}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">IP Address</label>
              <span className="text-white font-mono">{selectedLog.ipAddress}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Session ID</label>
              <span className="text-white font-mono">{selectedLog.sessionId}</span>
            </div>
            <div>
              <label className="block text-sm text-gray-400 mb-1">Timestamp</label>
              <span className="text-white">{selectedLog.timestamp.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {(selectedLog.oldValue || selectedLog.newValue) && (
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h4 className="text-white font-semibold mb-3">Data Changes</h4>
            <div className="space-y-4">
              {selectedLog.oldValue && (
                <div>
                  <label className="block text-sm text-gray-400 mb-1">Previous Value</label>
                  <pre className="bg-gray-900 rounded p-3 text-sm text-gray-300 overflow-x-auto">
                    {JSON.stringify(selectedLog.oldValue, null, 2)}
                  </pre>
                </div>
              )}
              {selectedLog.newValue && (
                <div>
                  <label className="block text-sm text-gray-400 mb-1">New Value</label>
                  <pre className="bg-gray-900 rounded p-3 text-sm text-gray-300 overflow-x-auto">
                    {JSON.stringify(selectedLog.newValue, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        {selectedLog.compliance && (
          <div className="bg-gray-800/50 rounded-lg p-4">
            <h4 className="text-white font-semibold mb-3">Compliance Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-400 mb-1">Standard</label>
                <span className="text-white">{selectedLog.compliance.standard}</span>
              </div>
              <div>
                <label className="block text-sm text-gray-400 mb-1">Classification</label>
                <span className="text-white">{selectedLog.compliance.classification}</span>
              </div>
              <div>
                <label className="block text-sm text-gray-400 mb-1">Requires Signature</label>
                <span className={selectedLog.compliance.requiresSignature ? 'text-yellow-400' : 'text-green-400'}>
                  {selectedLog.compliance.requiresSignature ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <label className="block text-sm text-gray-400 mb-1">Retention Period</label>
                <span className="text-white">{selectedLog.compliance.retentionPeriod} days</span>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full bg-gray-900 flex">
      {/* Audit Log List */}
      <div className="w-1/2 border-r border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-white">Audit Trail</h2>
            <button
              onClick={exportAuditReport}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Export Report</span>
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search audit logs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-400 mb-1">Start Date</label>
                <input
                  type="date"
                  value={dateRange.start}
                  onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                />
              </div>
              <div>
                <label className="block text-sm text-gray-400 mb-1">End Date</label>
                <input
                  type="date"
                  value={dateRange.end}
                  onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <select
                value={filterAction}
                onChange={(e) => setFilterAction(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="all">All Actions</option>
                <option value="create">Create</option>
                <option value="update">Update</option>
                <option value="delete">Delete</option>
                <option value="deploy">Deploy</option>
              </select>
              
              <select
                value={filterSeverity}
                onChange={(e) => setFilterSeverity(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="all">All Severities</option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
              
              <select
                value={filterUser}
                onChange={(e) => setFilterUser(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
              >
                <option value="all">All Users</option>
                <option value="user-1">user-1</option>
                <option value="user-2">user-2</option>
                <option value="user-3">user-3</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {filteredLogs.map(renderLogEntry)}
          
          {filteredLogs.length === 0 && (
            <div className="text-center py-12 text-gray-400">
              <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No audit logs found</p>
            </div>
          )}
        </div>
      </div>
      
      {/* Log Details */}
      <div className="w-1/2">
        {renderLogDetails()}
      </div>
    </div>
  );
};

export default AuditTrail;