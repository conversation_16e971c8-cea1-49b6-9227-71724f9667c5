import React, { useEffect, useRef } from 'react';
import { CompletionSuggestion } from '../hooks/useAICompletion';
import { Sparkles, Check, X, ChevronUp, ChevronDown, HelpCircle } from 'lucide-react';
import AiConfidenceBadge from './AiConfidenceBadge';

interface AIGhostBlockProps {
  suggestion: CompletionSuggestion | null;
  position: { x?: number; y?: number; top?: number; left?: number };
  onAccept: () => void;
  onDismiss: () => void;
  onCycleAlternative: (direction: 'next' | 'prev') => void;
  mode: 'ladder' | 'st';
  alternativeIndex?: number;
  alternativesCount?: number;
  showExplanation?: boolean;
  onToggleExplanation?: () => void;
}

const AIGhostBlock: React.FC<AIGhostBlockProps> = ({
  suggestion,
  position,
  onAccept,
  onDismiss,
  onCycleAlternative,
  mode,
  alternativeIndex = 0,
  alternativesCount = 0,
  showExplanation = false,
  onToggleExplanation
}) => {
  const ghostRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!suggestion) return;
      
      switch (e.key) {
        case 'Tab':
          e.preventDefault();
          onAccept();
          break;
        case 'Escape':
          e.preventDefault();
          onDismiss();
          break;
        case 'ArrowUp':
          e.preventDefault();
          onCycleAlternative('prev');
          break;
        case 'ArrowDown':
          e.preventDefault();
          onCycleAlternative('next');
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [suggestion, onAccept, onDismiss, onCycleAlternative]);

  if (!suggestion) return null;

  // Determine position style based on provided props
  const positionStyle: React.CSSProperties = {};
  if (position.x !== undefined && position.y !== undefined) {
    positionStyle.left = position.x;
    positionStyle.top = position.y;
  } else if (position.left !== undefined && position.top !== undefined) {
    positionStyle.left = position.left;
    positionStyle.top = position.top;
  }

  const renderLadderGhost = () => (
    <div className="bg-gray-800/90 backdrop-blur-sm border border-purple-500/50 rounded-lg p-3 shadow-lg">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <Sparkles className="w-4 h-4 text-purple-400" />
          <span className="text-white text-sm font-medium">AI Suggestion</span>
        </div>
        <AiConfidenceBadge confidence={suggestion.confidence} size="sm" />
      </div>
      
      <div className="bg-gray-900 rounded p-2 text-gray-300 text-sm mb-2">
        {suggestion.content}
      </div>
      
      {alternativesCount > 1 && (
        <div className="flex items-center justify-between text-xs text-gray-400 mb-2">
          <span>Alternative {alternativeIndex + 1}/{alternativesCount}</span>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => onCycleAlternative('prev')}
              className="p-1 hover:bg-gray-700 rounded"
            >
              <ChevronUp className="w-3 h-3" />
            </button>
            <button
              onClick={() => onCycleAlternative('next')}
              className="p-1 hover:bg-gray-700 rounded"
            >
              <ChevronDown className="w-3 h-3" />
            </button>
          </div>
        </div>
      )}
      
      {showExplanation && suggestion.explanation && (
        <div className="bg-gray-700/50 rounded p-2 text-xs text-gray-300 mb-2">
          {suggestion.explanation}
        </div>
      )}
      
      <div className="flex items-center justify-between">
        <button
          onClick={onToggleExplanation}
          className="text-gray-400 hover:text-white text-xs flex items-center space-x-1"
        >
          <HelpCircle className="w-3 h-3" />
          <span>{showExplanation ? 'Hide' : 'Why?'}</span>
        </button>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={onDismiss}
            className="flex items-center space-x-1 text-gray-400 hover:text-white text-xs"
          >
            <X className="w-3 h-3" />
            <span>ESC</span>
          </button>
          <button
            onClick={onAccept}
            className="flex items-center space-x-1 bg-purple-600 hover:bg-purple-700 text-white px-2 py-1 rounded text-xs"
          >
            <Check className="w-3 h-3" />
            <span>TAB</span>
          </button>
        </div>
      </div>
    </div>
  );

  const renderSTGhost = () => (
    <div className="bg-gray-800/90 backdrop-blur-sm border-l-2 border-purple-500 pl-2 py-1 text-gray-400 text-sm flex items-center">
      <Sparkles className="w-3 h-3 text-purple-400 mr-2" />
      <span className="font-mono">{suggestion.content}</span>
      <div className="ml-2 text-xs text-gray-500">TAB to accept</div>
    </div>
  );

  return (
    <div 
      ref={ghostRef}
      className="absolute z-50 pointer-events-auto"
      style={positionStyle}
    >
      {mode === 'ladder' ? renderLadderGhost() : renderSTGhost()}
    </div>
  );
};

export default AIGhostBlock;