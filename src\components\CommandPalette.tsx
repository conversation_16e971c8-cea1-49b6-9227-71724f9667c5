import React, { useState, useEffect } from 'react';
import { Search, Command, Zap, Upload, Download, Play, Square, Code, Grid3X3, <PERSON>rkles, Settings, Users, Shield, Monitor, Network, BookOpen, FileText, Activity, Cpu, HardDrive, Wifi, Bug, Lightbulb, Wrench, Plus, Trash2, Copy, Save, FolderOpen, Eye, RotateCcw } from 'lucide-react';
import { usePLCStore } from '../store/plcStore';
import AiCommandPalette from '../ai/components/AiCommandPalette';

interface CommandItem {
  id: string;
  title: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  shortcut?: string;
  action: () => void;
}

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
}

const CommandPalette: React.FC<CommandPaletteProps> = ({ isOpen, onClose }) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [showAiPalette, setShowAiPalette] = useState(false);
  const { 
    createProgram, 
    toggleSimulation, 
    simulationMode,
    currentProject,
    createProject,
    saveProject,
    createTag,
    addTarget
  } = usePLCStore();

  // Check if query starts with AI trigger
  const isAiQuery = query.toLowerCase().startsWith('ai ') || 
                    query.toLowerCase().includes('generate') || 
                    query.toLowerCase().includes('explain') ||
                    query.toLowerCase().includes('autocomplete');

  const commands: CommandItem[] = [
    // AI Commands
    {
      id: 'ai-assistant',
      title: 'Open AI Assistant',
      description: 'Access AI-powered code generation and assistance',
      category: 'AI',
      icon: <Sparkles className="w-4 h-4" />,
      shortcut: 'Ctrl+Shift+A',
      action: () => {
        setShowAiPalette(true);
        onClose();
      }
    },
    {
      id: 'ai-generate-motor',
      title: 'AI: Generate Motor Control',
      description: 'Generate motor start/stop logic with AI',
      category: 'AI',
      icon: <Zap className="w-4 h-4" />,
      action: () => {
        setShowAiPalette(true);
        onClose();
      }
    },
    {
      id: 'ai-explain-code',
      title: 'AI: Explain Selected Code',
      description: 'Get AI explanation of selected ladder logic',
      category: 'AI',
      icon: <Lightbulb className="w-4 h-4" />,
      action: () => {
        setShowAiPalette(true);
        onClose();
      }
    },
    {
      id: 'ai-optimize-code',
      title: 'AI: Optimize Code',
      description: 'Get AI suggestions for code optimization',
      category: 'AI',
      icon: <Wrench className="w-4 h-4" />,
      action: () => {
        setShowAiPalette(true);
        onClose();
      }
    },
    {
      id: 'ai-autocomplete',
      title: 'AI: Toggle Autocomplete',
      description: 'Enable or disable AI-powered code completion',
      category: 'AI',
      icon: <Sparkles className="w-4 h-4" />,
      action: () => {
        alert('AI autocomplete toggled');
        onClose();
      }
    },

    // File Operations
    {
      id: 'new-project',
      title: 'New Project',
      description: 'Create a new PLC project',
      category: 'File Operations',
      icon: <Plus className="w-4 h-4" />,
      shortcut: 'Ctrl+Shift+N',
      action: () => {
        const name = prompt('Enter project name:');
        if (name) {
          createProject(name, 'New PLC automation project');
        }
        onClose();
      }
    },
    {
      id: 'save-project',
      title: 'Save Project',
      description: 'Save current project',
      category: 'File Operations',
      icon: <Save className="w-4 h-4" />,
      shortcut: 'Ctrl+S',
      action: () => {
        saveProject();
        onClose();
      }
    },
    {
      id: 'export-project',
      title: 'Export Project',
      description: 'Export project as JSON file',
      category: 'File Operations',
      icon: <Download className="w-4 h-4" />,
      action: () => {
        if (currentProject) {
          const dataStr = JSON.stringify(currentProject, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `${currentProject.name}.json`;
          link.click();
          URL.revokeObjectURL(url);
        }
        onClose();
      }
    },

    // Program Operations
    {
      id: 'create-ladder',
      title: 'New Ladder Program',
      description: 'Create a new ladder logic program',
      category: 'Program Operations',
      icon: <Grid3X3 className="w-4 h-4" />,
      shortcut: 'Ctrl+N',
      action: () => {
        const name = prompt('Enter program name:');
        if (name) {
          createProgram(name, 'ladder');
        }
        onClose();
      }
    },
    {
      id: 'create-st',
      title: 'New Structured Text Program',
      description: 'Create a new structured text program',
      category: 'Program Operations',
      icon: <Code className="w-4 h-4" />,
      action: () => {
        const name = prompt('Enter program name:');
        if (name) {
          createProgram(name, 'st');
        }
        onClose();
      }
    },
    {
      id: 'create-safety',
      title: 'New Safety Program',
      description: 'Create a new safety-rated program',
      category: 'Program Operations',
      icon: <Shield className="w-4 h-4" />,
      action: () => {
        const name = prompt('Enter safety program name:');
        if (name) {
          createProgram(name, 'safety');
        }
        onClose();
      }
    },

    // Tag Operations
    {
      id: 'create-input-tag',
      title: 'Create Input Tag',
      description: 'Create a new input tag',
      category: 'Tag Operations',
      icon: <Plus className="w-4 h-4" />,
      action: () => {
        const name = prompt('Enter tag name:');
        if (name) {
          createTag({
            name,
            type: 'BOOL',
            value: false,
            scope: 'INPUT',
            description: 'New input tag'
          });
        }
        onClose();
      }
    },
    {
      id: 'create-output-tag',
      title: 'Create Output Tag',
      description: 'Create a new output tag',
      category: 'Tag Operations',
      icon: <Plus className="w-4 h-4" />,
      action: () => {
        const name = prompt('Enter tag name:');
        if (name) {
          createTag({
            name,
            type: 'BOOL',
            value: false,
            scope: 'OUTPUT',
            description: 'New output tag'
          });
        }
        onClose();
      }
    },

    // PLC Operations
    {
      id: 'download-plc',
      title: 'Download to PLC',
      description: 'Deploy current program to connected PLC',
      category: 'PLC Operations',
      icon: <Download className="w-4 h-4" />,
      shortcut: 'Ctrl+D',
      action: () => {
        alert('Downloading program to PLC...');
        onClose();
      }
    },
    {
      id: 'upload-plc',
      title: 'Upload from PLC',
      description: 'Retrieve program from connected PLC',
      category: 'PLC Operations',
      icon: <Upload className="w-4 h-4" />,
      shortcut: 'Ctrl+U',
      action: () => {
        alert('Uploading program from PLC...');
        onClose();
      }
    },
    {
      id: 'connect-plc',
      title: 'Connect to PLC',
      description: 'Establish connection to PLC target',
      category: 'PLC Operations',
      icon: <Wifi className="w-4 h-4" />,
      action: () => {
        const ip = prompt('Enter PLC IP address:', '*************');
        if (ip) {
          alert(`Connecting to PLC at ${ip}...`);
        }
        onClose();
      }
    },
    {
      id: 'add-target',
      title: 'Add PLC Target',
      description: 'Add a new PLC target to the project',
      category: 'PLC Operations',
      icon: <Cpu className="w-4 h-4" />,
      action: () => {
        const name = prompt('Enter target name:', 'New PLC');
        if (name) {
          addTarget({
            name,
            brand: 'siemens',
            model: 'CPU 1511-1 PN',
            connected: false,
            status: 'stop'
          });
        }
        onClose();
      }
    },

    // Simulation
    {
      id: 'toggle-simulation',
      title: simulationMode ? 'Stop Simulation' : 'Start Simulation',
      description: simulationMode ? 'Stop PLC simulation mode' : 'Start PLC simulation mode',
      category: 'Simulation',
      icon: <Zap className="w-4 h-4" />,
      shortcut: 'F5',
      action: () => {
        toggleSimulation();
        onClose();
      }
    },
    {
      id: 'reset-simulation',
      title: 'Reset Simulation',
      description: 'Reset all tag values to defaults',
      category: 'Simulation',
      icon: <RotateCcw className="w-4 h-4" />,
      action: () => {
        alert('Simulation reset');
        onClose();
      }
    },

    // PLC Control
    {
      id: 'start-plc',
      title: 'Start PLC',
      description: 'Put PLC in RUN mode',
      category: 'PLC Control',
      icon: <Play className="w-4 h-4" />,
      shortcut: 'Shift+F5',
      action: () => {
        alert('Starting PLC...');
        onClose();
      }
    },
    {
      id: 'stop-plc',
      title: 'Stop PLC',
      description: 'Put PLC in STOP mode',
      category: 'PLC Control',
      icon: <Square className="w-4 h-4" />,
      shortcut: 'Shift+F6',
      action: () => {
        alert('Stopping PLC...');
        onClose();
      }
    },

    // View Operations
    {
      id: 'open-hmi',
      title: 'Open HMI Preview',
      description: 'Open HMI design and preview panel',
      category: 'View Operations',
      icon: <Monitor className="w-4 h-4" />,
      action: () => {
        // This would switch to HMI tab
        alert('Opening HMI Preview...');
        onClose();
      }
    },
    {
      id: 'open-network',
      title: 'Open Network Topology',
      description: 'View network configuration and topology',
      category: 'View Operations',
      icon: <Network className="w-4 h-4" />,
      action: () => {
        alert('Opening Network Topology...');
        onClose();
      }
    },
    {
      id: 'open-templates',
      title: 'Open Template Library',
      description: 'Browse and use code templates',
      category: 'View Operations',
      icon: <BookOpen className="w-4 h-4" />,
      action: () => {
        alert('Opening Template Library...');
        onClose();
      }
    },
    {
      id: 'open-diagnostics',
      title: 'Open System Diagnostics',
      description: 'View system health and diagnostics',
      category: 'View Operations',
      icon: <Activity className="w-4 h-4" />,
      action: () => {
        alert('Opening System Diagnostics...');
        onClose();
      }
    },

    // Debug Operations
    {
      id: 'start-trace',
      title: 'Start Signal Tracing',
      description: 'Begin signal flow tracing',
      category: 'Debug Operations',
      icon: <Eye className="w-4 h-4" />,
      action: () => {
        alert('Starting signal trace...');
        onClose();
      }
    },
    {
      id: 'debug-program',
      title: 'Debug Current Program',
      description: 'Start debugging the active program',
      category: 'Debug Operations',
      icon: <Bug className="w-4 h-4" />,
      shortcut: 'F9',
      action: () => {
        alert('Starting debugger...');
        onClose();
      }
    },

    // System Operations
    {
      id: 'system-settings',
      title: 'System Settings',
      description: 'Open system configuration panel',
      category: 'System Operations',
      icon: <Settings className="w-4 h-4" />,
      action: () => {
        alert('Opening system settings...');
        onClose();
      }
    },
    {
      id: 'user-management',
      title: 'User Management',
      description: 'Manage users and permissions',
      category: 'System Operations',
      icon: <Users className="w-4 h-4" />,
      action: () => {
        alert('Opening user management...');
        onClose();
      }
    },
    {
      id: 'backup-project',
      title: 'Backup Project',
      description: 'Create a backup of the current project',
      category: 'System Operations',
      icon: <HardDrive className="w-4 h-4" />,
      action: () => {
        alert('Creating project backup...');
        onClose();
      }
    }
  ];

  const filteredCommands = commands.filter(cmd =>
    cmd.title.toLowerCase().includes(query.toLowerCase()) ||
    cmd.description.toLowerCase().includes(query.toLowerCase()) ||
    cmd.category.toLowerCase().includes(query.toLowerCase())
  );

  useEffect(() => {
    setSelectedIndex(0);
  }, [query]);

  useEffect(() => {
    // Auto-open AI palette for AI queries
    if (isAiQuery && query.length > 3) {
      setShowAiPalette(true);
      onClose();
    }
  }, [query, isAiQuery, onClose]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, filteredCommands, onClose]);

  if (!isOpen) {
    return (
      <AiCommandPalette 
        isOpen={showAiPalette} 
        onClose={() => setShowAiPalette(false)}
        context={{ selectedCode: '', language: 'ladder' }}
      />
    );
  }

  return (
    <>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-32">
        <div className="bg-base-dark border border-control-600 rounded-lg shadow-2xl w-full max-w-2xl">
          <div className="p-4 border-b border-control-600">
            <div className="flex items-center space-x-3">
              <Search className="w-5 h-5 text-control-400" />
              <input
                type="text"
                placeholder="Type a command or search... (try 'ai generate motor control')"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                className="flex-1 bg-transparent text-neutral outline-none text-lg"
                autoFocus
              />
              {isAiQuery && (
                <div className="flex items-center space-x-1 text-purple-400">
                  <Sparkles className="w-4 h-4" />
                  <span className="text-sm">AI Mode</span>
                </div>
              )}
            </div>
          </div>
          
          <div className="max-h-96 overflow-y-auto">
            {filteredCommands.length === 0 ? (
              <div className="p-8 text-center text-control-400">
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No commands found</p>
                <p className="text-sm">Try "ai generate" for AI assistance</p>
              </div>
            ) : (
              <div className="p-2">
                {filteredCommands.reduce((acc, cmd, index) => {
                  const isNewCategory = index === 0 || cmd.category !== filteredCommands[index - 1].category;
                  
                  if (isNewCategory) {
                    acc.push(
                      <div key={`category-${cmd.category}`} className="px-3 py-2 text-xs font-semibold text-control-400 sticky top-0 bg-base-dark">
                        {cmd.category}
                      </div>
                    );
                  }
                  
                  acc.push(
                    <div
                      key={cmd.id}
                      className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                        index === selectedIndex ? 'bg-accent/20 border border-accent/30' : 'hover:bg-control-800/50'
                      }`}
                      onClick={() => cmd.action()}
                    >
                      <span className="text-control-400">{cmd.icon}</span>
                      <div className="flex-1">
                        <div className="text-neutral font-medium">{cmd.title}</div>
                        <div className="text-sm text-control-400">{cmd.description}</div>
                      </div>
                      {cmd.shortcut && (
                        <div className="text-xs text-control-500 bg-control-800 px-2 py-1 rounded font-mono">
                          {cmd.shortcut}
                        </div>
                      )}
                    </div>
                  );
                  
                  return acc;
                }, [] as React.ReactNode[])}
              </div>
            )}
          </div>
          
          <div className="p-3 border-t border-control-600 text-xs text-control-400 flex items-center justify-between">
            <div className="space-x-4">
              <span>↑↓ Navigate</span>
              <span>↵ Select</span>
              <span>ESC Close</span>
            </div>
            <div className="flex items-center space-x-1">
              <span>Powered by</span>
              <Command className="w-3 h-3" />
              <span>+</span>
              <Sparkles className="w-3 h-3" />
            </div>
          </div>
        </div>
      </div>

      <AiCommandPalette 
        isOpen={showAiPalette} 
        onClose={() => setShowAiPalette(false)}
        context={{ selectedCode: '', language: 'ladder' }}
      />
    </>
  );
};

export default CommandPalette;