// Project Packaging Service for Portable Workspaces

import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { PLCProject } from '../types/plc';

export interface ProjectArchive {
  metadata: ProjectMetadata;
  project: PLCProject;
  dependencies: ProjectDependency[];
  assets: ProjectAsset[];
  version: string;
  created: Date;
  signature?: string;
}

export interface ProjectMetadata {
  name: string;
  version: string;
  description: string;
  author: string;
  organization: string;
  created: Date;
  modified: Date;
  tags: string[];
  compatibility: CompatibilityInfo;
}

export interface CompatibilityInfo {
  minIDEVersion: string;
  targetPLCs: string[];
  requiredFeatures: string[];
  safetyLevel?: 'SIL1' | 'SIL2' | 'SIL3';
}

export interface ProjectDependency {
  name: string;
  version: string;
  type: 'library' | 'plugin' | 'driver';
  source: string;
  checksum: string;
  required: boolean;
}

export interface ProjectAsset {
  path: string;
  type: 'hmi' | 'documentation' | 'test' | 'config';
  data: ArrayBuffer | string;
  checksum: string;
}

export class ProjectPackager {
  private static readonly ARCHIVE_VERSION = '1.0.0';
  private static readonly MAGIC_HEADER = 'PLCX';

  async packageProject(
    project: PLCProject,
    options: PackageOptions = {}
  ): Promise<Blob> {
    const archive: ProjectArchive = {
      metadata: this.generateMetadata(project, options),
      project: this.sanitizeProject(project),
      dependencies: await this.collectDependencies(project),
      assets: await this.collectAssets(project),
      version: ProjectPackager.ARCHIVE_VERSION,
      created: new Date()
    };

    // Add digital signature if requested
    if (options.sign) {
      archive.signature = await this.signArchive(archive);
    }

    return this.createArchiveFile(archive);
  }

  async unpackageProject(archiveBlob: Blob): Promise<{
    project: PLCProject;
    metadata: ProjectMetadata;
    dependencies: ProjectDependency[];
    assets: ProjectAsset[];
  }> {
    const zip = new JSZip();
    const zipContent = await zip.loadAsync(archiveBlob);

    // Verify archive structure
    await this.verifyArchiveStructure(zipContent);

    // Extract metadata
    const metadataFile = zipContent.file('metadata.json');
    if (!metadataFile) throw new Error('Invalid archive: missing metadata');
    
    const metadata: ProjectMetadata = JSON.parse(await metadataFile.async('text'));

    // Extract project data
    const projectFile = zipContent.file('project.json');
    if (!projectFile) throw new Error('Invalid archive: missing project data');
    
    const project: PLCProject = JSON.parse(await projectFile.async('text'));

    // Extract dependencies
    const dependenciesFile = zipContent.file('dependencies.json');
    const dependencies: ProjectDependency[] = dependenciesFile 
      ? JSON.parse(await dependenciesFile.async('text'))
      : [];

    // Extract assets
    const assets: ProjectAsset[] = [];
    const assetsFolder = zipContent.folder('assets');
    if (assetsFolder) {
      for (const [path, file] of Object.entries(assetsFolder.files)) {
        if (!file.dir) {
          const data = await file.async('arraybuffer');
          assets.push({
            path,
            type: this.inferAssetType(path),
            data,
            checksum: await this.calculateChecksum(data)
          });
        }
      }
    }

    // Verify compatibility
    await this.verifyCompatibility(metadata.compatibility);

    return { project, metadata, dependencies, assets };
  }

  private generateMetadata(project: PLCProject, options: PackageOptions): ProjectMetadata {
    return {
      name: project.name,
      version: project.version,
      description: project.description || '',
      author: options.author || 'Unknown',
      organization: options.organization || '',
      created: project.created,
      modified: project.modified,
      tags: options.tags || [],
      compatibility: {
        minIDEVersion: '1.0.0',
        targetPLCs: project.targets.map(t => `${t.brand}:${t.model}`),
        requiredFeatures: this.extractRequiredFeatures(project),
        safetyLevel: this.determineSafetyLevel(project)
      }
    };
  }

  private sanitizeProject(project: PLCProject): PLCProject {
    // Remove sensitive information and runtime data
    return {
      ...project,
      targets: project.targets.map(target => ({
        ...target,
        // Remove connection details for security
        ipAddress: undefined,
        connected: false,
        status: 'stop'
      })),
      globalTags: project.globalTags.map(tag => ({
        ...tag,
        // Reset runtime values
        value: this.getDefaultValue(tag.type),
        lastUpdated: undefined
      }))
    };
  }

  private async collectDependencies(project: PLCProject): Promise<ProjectDependency[]> {
    const dependencies: ProjectDependency[] = [];

    // Collect PLC-specific libraries
    for (const target of project.targets) {
      dependencies.push({
        name: `${target.brand}-driver`,
        version: '1.0.0',
        type: 'driver',
        source: `drivers/${target.brand}`,
        checksum: await this.calculateStringChecksum(`${target.brand}-${target.model}`),
        required: true
      });
    }

    // Collect safety libraries if needed
    const hasSafetyPrograms = project.programs.some(p => p.safetyProgram);
    if (hasSafetyPrograms) {
      dependencies.push({
        name: 'safety-library',
        version: '2.0.0',
        type: 'library',
        source: 'libraries/safety',
        checksum: await this.calculateStringChecksum('safety-library-2.0.0'),
        required: true
      });
    }

    return dependencies;
  }

  private async collectAssets(project: PLCProject): Promise<ProjectAsset[]> {
    const assets: ProjectAsset[] = [];

    // Collect HMI screens
    if (project.hmiScreens) {
      for (const screen of project.hmiScreens) {
        const data = JSON.stringify(screen);
        assets.push({
          path: `hmi/${screen.name}.json`,
          type: 'hmi',
          data,
          checksum: await this.calculateStringChecksum(data)
        });
      }
    }

    // Collect documentation
    const documentation = this.generateDocumentation(project);
    assets.push({
      path: 'documentation/project.md',
      type: 'documentation',
      data: documentation,
      checksum: await this.calculateStringChecksum(documentation)
    });

    return assets;
  }

  private async createArchiveFile(archive: ProjectArchive): Promise<Blob> {
    const zip = new JSZip();

    // Add metadata
    zip.file('metadata.json', JSON.stringify(archive.metadata, null, 2));

    // Add project data
    zip.file('project.json', JSON.stringify(archive.project, null, 2));

    // Add dependencies
    if (archive.dependencies.length > 0) {
      zip.file('dependencies.json', JSON.stringify(archive.dependencies, null, 2));
    }

    // Add assets
    const assetsFolder = zip.folder('assets');
    for (const asset of archive.assets) {
      assetsFolder?.file(asset.path, asset.data);
    }

    // Add signature if present
    if (archive.signature) {
      zip.file('signature.txt', archive.signature);
    }

    // Add version info
    zip.file('version.txt', archive.version);

    return zip.generateAsync({ type: 'blob', compression: 'DEFLATE' });
  }

  private async verifyArchiveStructure(zip: JSZip): Promise<void> {
    const requiredFiles = ['metadata.json', 'project.json', 'version.txt'];
    
    for (const file of requiredFiles) {
      if (!zip.file(file)) {
        throw new Error(`Invalid archive: missing ${file}`);
      }
    }

    // Verify version compatibility
    const versionFile = zip.file('version.txt');
    if (versionFile) {
      const version = await versionFile.async('text');
      if (!this.isVersionCompatible(version.trim())) {
        throw new Error(`Incompatible archive version: ${version}`);
      }
    }
  }

  private async verifyCompatibility(compatibility: CompatibilityInfo): Promise<void> {
    // Check IDE version compatibility
    if (!this.isVersionCompatible(compatibility.minIDEVersion)) {
      throw new Error(`Archive requires IDE version ${compatibility.minIDEVersion} or higher`);
    }

    // Check required features
    const availableFeatures = this.getAvailableFeatures();
    const missingFeatures = compatibility.requiredFeatures.filter(
      feature => !availableFeatures.includes(feature)
    );

    if (missingFeatures.length > 0) {
      throw new Error(`Missing required features: ${missingFeatures.join(', ')}`);
    }
  }

  private extractRequiredFeatures(project: PLCProject): string[] {
    const features: string[] = [];

    // Check for safety features
    if (project.programs.some(p => p.safetyProgram)) {
      features.push('safety_programming');
    }

    // Check for HMI features
    if (project.hmiScreens && project.hmiScreens.length > 0) {
      features.push('hmi_design');
    }

    // Check for network features
    if (project.networkTopology && project.networkTopology.length > 0) {
      features.push('network_configuration');
    }

    return features;
  }

  private determineSafetyLevel(project: PLCProject): 'SIL1' | 'SIL2' | 'SIL3' | undefined {
    const safetyPrograms = project.programs.filter(p => p.safetyProgram);
    if (safetyPrograms.length === 0) return undefined;

    // Return highest safety level found
    const levels = safetyPrograms
      .map(p => p.silRating)
      .filter(Boolean) as ('SIL1' | 'SIL2' | 'SIL3')[];

    if (levels.includes('SIL3')) return 'SIL3';
    if (levels.includes('SIL2')) return 'SIL2';
    if (levels.includes('SIL1')) return 'SIL1';

    return undefined;
  }

  private getDefaultValue(type: string): any {
    switch (type) {
      case 'BOOL': return false;
      case 'INT': case 'DINT': return 0;
      case 'REAL': return 0.0;
      case 'STRING': return '';
      case 'TIME': return 'T#0ms';
      default: return null;
    }
  }

  private inferAssetType(path: string): ProjectAsset['type'] {
    if (path.includes('hmi/')) return 'hmi';
    if (path.includes('doc')) return 'documentation';
    if (path.includes('test')) return 'test';
    return 'config';
  }

  private generateDocumentation(project: PLCProject): string {
    let doc = `# ${project.name}\n\n`;
    doc += `**Version:** ${project.version}\n`;
    doc += `**Description:** ${project.description || 'No description'}\n\n`;

    doc += `## Programs\n\n`;
    project.programs.forEach(program => {
      doc += `### ${program.name}\n`;
      doc += `- **Type:** ${program.type}\n`;
      doc += `- **Safety Program:** ${program.safetyProgram ? 'Yes' : 'No'}\n`;
      if (program.silRating) {
        doc += `- **SIL Rating:** ${program.silRating}\n`;
      }
      doc += '\n';
    });

    doc += `## Global Tags\n\n`;
    project.globalTags.forEach(tag => {
      doc += `- **${tag.name}** (${tag.type}): ${tag.description || 'No description'}\n`;
    });

    doc += `\n## Targets\n\n`;
    project.targets.forEach(target => {
      doc += `- **${target.name}**: ${target.brand} ${target.model}\n`;
    });

    return doc;
  }

  private async signArchive(archive: ProjectArchive): Promise<string> {
    // In production, this would use proper digital signing
    const data = JSON.stringify(archive);
    return this.calculateStringChecksum(data);
  }

  private async calculateChecksum(data: ArrayBuffer): Promise<string> {
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private async calculateStringChecksum(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    return this.calculateChecksum(dataBuffer);
  }

  private isVersionCompatible(version: string): boolean {
    // Simple version comparison - in production, use proper semver
    return version <= ProjectPackager.ARCHIVE_VERSION;
  }

  private getAvailableFeatures(): string[] {
    return [
      'ladder_programming',
      'structured_text',
      'safety_programming',
      'hmi_design',
      'network_configuration',
      'simulation',
      'collaboration'
    ];
  }
}

export interface PackageOptions {
  author?: string;
  organization?: string;
  tags?: string[];
  sign?: boolean;
  includeAssets?: boolean;
}

export const projectPackager = new ProjectPackager();