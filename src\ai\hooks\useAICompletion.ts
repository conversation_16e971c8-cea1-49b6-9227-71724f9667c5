import { useState, useCallback, useEffect } from 'react';
import { aiClient } from '../services/aiClient';
import { usePLCStore } from '../../store/plcStore';
import { mcpService } from '../services/mcpService';

export interface CompletionContext {
  mode: 'ladder' | 'st';
  programType?: string;
  previousBlocks?: string[];
  previousLines?: string[];
  scopeTags?: string[];
  currentIndent?: number;
  rungComment?: string;
  selectedElement?: any;
  cursorPosition?: { line: number; column: number };
}

export interface CompletionSuggestion {
  id: string;
  content: string;
  type: 'element' | 'rung' | 'line' | 'block' | 'tag';
  confidence: number;
  alternatives?: string[];
  explanation?: string;
  preview?: string;
}

export function useAICompletion() {
  const [suggestion, setSuggestion] = useState<CompletionSuggestion | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSuggestion, setShowSuggestion] = useState(false);
  const [alternatives, setAlternatives] = useState<string[]>([]);
  const [selectedAlternative, setSelectedAlternative] = useState(0);
  const { currentProject } = usePLCStore();

  // Clear suggestion when project changes
  useEffect(() => {
    setSuggestion(null);
    setShowSuggestion(false);
  }, [currentProject?.id]);

  const requestSuggestion = useCallback(async (context: CompletionContext) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Try to enhance the context with MCP
      try {
        // Get available tags for context
        const availableTags = context.scopeTags || [];
        
        // Build prompt for AI suggestion
        const prompt = buildPrompt(context);
        
        // Enhance the prompt with MCP
        const enhancedPrompt = await mcpService.enhancePrompt({
          prompt,
          contextTypes: ['program', 'tag', 'safety', 'standard']
        });
        
        // Use the enhanced prompt for the AI request
        context.mcpEnhancedPrompt = enhancedPrompt.enhancedPrompt;
      } catch (error) {
        console.warn('Failed to enhance context with MCP:', error);
      }
      
      const prompt = buildPrompt(context);
      
      // In a real implementation, this would call the AI backend
      const result = await aiClient.request({
        type: 'predict_next',
        prompt,
        context: {
          mode: context.mode,
          programType: context.programType
        }
      });
      
      // Process the result into a suggestion
      const completionSuggestion: CompletionSuggestion = {
        id: `suggestion_${Date.now()}`,
        content: result.content,
        type: determineCompletionType(context.mode, result.content),
        confidence: result.confidence || 0.8,
        alternatives: result.suggestions || [],
        explanation: "AI-generated suggestion based on your current context"
      };
      
      setSuggestion(completionSuggestion);
      setAlternatives(completionSuggestion.alternatives || []);
      setSelectedAlternative(0);
      setShowSuggestion(true);
    } catch (error) {
      console.error('AI completion request failed:', error);
      setError(error instanceof Error ? error.message : 'Failed to get AI suggestion');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const acceptSuggestion = useCallback(() => {
    setShowSuggestion(false);
    return suggestion;
  }, [suggestion]);

  const dismissSuggestion = useCallback(() => {
    setShowSuggestion(false);
    setSuggestion(null);
  }, []);

  const cycleAlternatives = useCallback((direction: 'next' | 'prev') => {
    if (!suggestion || !alternatives.length) return;
    
    const newIndex = direction === 'next'
      ? (selectedAlternative + 1) % alternatives.length
      : (selectedAlternative - 1 + alternatives.length) % alternatives.length;
    
    setSelectedAlternative(newIndex);
    
    // Update the suggestion content with the selected alternative
    setSuggestion({
      ...suggestion,
      content: alternatives[newIndex]
    });
  }, [suggestion, alternatives, selectedAlternative]);

  return {
    suggestion,
    showSuggestion,
    isLoading,
    error,
    requestSuggestion,
    acceptSuggestion,
    dismissSuggestion,
    cycleAlternatives,
    selectedAlternative,
    alternatives
  };
}

// Helper functions
function buildPrompt(context: CompletionContext): string {
  switch (context.mode) {
    case 'ladder':
      return buildLadderPrompt(context);
    case 'st':
      return buildSTPrompt(context);
    default:
      return '';
  }
}

function buildLadderPrompt(context: CompletionContext): string {
  let prompt = 'Suggest the next logical ladder element';
  
  if (context.previousBlocks && context.previousBlocks.length > 0) {
    prompt += ` after ${context.previousBlocks.join(', ')}`;
  }
  
  if (context.programType) {
    prompt += `. Program type is ${context.programType}`;
  }
  
  if (context.scopeTags && context.scopeTags.length > 0) {
    prompt += `. Available tags: ${context.scopeTags.join(', ')}`;
  }
  
  if (context.rungComment) {
    prompt += `. Rung comment: "${context.rungComment}"`;
  }
  
  prompt += '. Return element type, tag name, and properties.';
  
  // If we have an MCP-enhanced prompt, use that instead
  if (context.mcpEnhancedPrompt) {
    return context.mcpEnhancedPrompt;
  }
  
  return prompt;
}

function buildSTPrompt(context: CompletionContext): string {
  let prompt = 'Suggest the next line of structured text code';
  
  if (context.previousLines && context.previousLines.length > 0) {
    const recentLines = context.previousLines.slice(-3).join('\n');
    prompt += ` after:\n${recentLines}`;
  }
  
  if (context.scopeTags && context.scopeTags.length > 0) {
    prompt += `\nAvailable tags: ${context.scopeTags.join(', ')}`;
  }
  
  if (context.currentIndent !== undefined) {
    prompt += `\nCurrent indent level: ${context.currentIndent}`;
  }
  
  prompt += '\nFollow IEC 61131-3 standards. Return only the code line.';
  
  // If we have an MCP-enhanced prompt, use that instead
  if (context.mcpEnhancedPrompt) {
    return context.mcpEnhancedPrompt;
  }
  
  return prompt;
}

function determineCompletionType(mode: string, content: string): CompletionSuggestion['type'] {
  if (mode === 'ladder') {
    if (content.includes('rung') || content.includes('Rung')) {
      return 'rung';
    }
    return 'element';
  } else if (mode === 'st') {
    if (content.includes('FUNCTION_BLOCK') || content.includes('FUNCTION') || content.includes('PROGRAM')) {
      return 'block';
    }
    return 'line';
  }
  
  return 'element';
}