# LUREON Model Context Protocol (MCP) Backend

## Overview

The Model Context Protocol (MCP) is a sophisticated context management system that enhances AI interactions in the LUREON Industrial Automation IDE. This document provides comprehensive guidance for implementing the MCP backend services that will integrate with the existing frontend components.

## Architecture

The MCP backend follows a modular architecture designed to collect, process, and utilize contextual information to improve AI responses for industrial automation tasks.

### Core Components

1. **Context Collection Service**
   - Gathers relevant context from user activities
   - Monitors active projects, programs, and tags
   - Extracts safety requirements and standards compliance information

2. **Context Processing Engine**
   - Analyzes and categorizes collected context
   - Prioritizes context based on relevance and importance
   - Maintains context history and versioning

3. **Context Storage Service**
   - Persists context data in a structured format
   - Implements efficient retrieval mechanisms
   - Manages context lifecycle and expiration

4. **Prompt Enhancement Service**
   - Integrates context into AI prompts
   - Optimizes prompt structure for different AI models
   - Balances context completeness with token efficiency

5. **Confidence Scoring System**
   - Evaluates context quality and completeness
   - Calculates confidence scores for AI responses
   - Provides feedback on context gaps

6. **Context API**
   - Exposes RESTful endpoints for context operations
   - Handles authentication and authorization
   - Implements rate limiting and quota management

### Data Flow

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  User Activity  │────▶│ Context         │────▶│ Context         │
│  (IDE Actions)  │     │ Collection      │     │ Processing      │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  AI Response    │◀────│ Prompt          │◀────│ Context         │
│  Generation     │     │ Enhancement     │     │ Storage         │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                                               ▲
        ▼                                               │
┌─────────────────┐     ┌─────────────────┐            │
│  Confidence     │────▶│ Response        │────────────┘
│  Scoring        │     │ Delivery        │
└─────────────────┘     └─────────────────┘
```

## Implementation Phases

### Phase 1: Core Infrastructure

1. **Setup Development Environment**
   - Configure Node.js with TypeScript
   - Set up Docker containers for services
   - Implement CI/CD pipeline

2. **Database Schema Design**
   - Create tables for context items
   - Design schema for context metadata
   - Implement indexing for efficient queries

3. **Context Collection API**
   - Create endpoints for manual context submission
   - Implement automatic context extraction
   - Set up authentication middleware

4. **Basic Context Storage**
   - Implement CRUD operations for context items
   - Create context serialization utilities
   - Set up basic caching mechanisms

### Phase 2: Context Processing

1. **Context Analysis Engine**
   - Implement context categorization
   - Create relevance scoring algorithms
   - Build context relationship mapping

2. **Prompt Enhancement**
   - Develop prompt templates for different request types
   - Implement context injection strategies
   - Create token optimization algorithms

3. **Confidence Scoring**
   - Implement context quality evaluation
   - Create confidence calculation formulas
   - Build feedback mechanisms for context improvement

4. **Context Versioning**
   - Implement context history tracking
   - Create version comparison utilities
   - Build rollback mechanisms

### Phase 3: Integration and Optimization

1. **AI Service Integration**
   - Connect to existing AI backend services
   - Implement context-aware request routing
   - Create fallback mechanisms for missing context

2. **Performance Optimization**
   - Implement caching strategies
   - Optimize database queries
   - Create batch processing for context updates

3. **Monitoring and Analytics**
   - Set up context usage metrics
   - Implement performance monitoring
   - Create analytics dashboards

4. **Documentation and Testing**
   - Complete API documentation
   - Develop comprehensive test suite
   - Create deployment guides

## Technical Specifications

### API Endpoints

#### Context Management

```
GET /api/mcp/context
GET /api/mcp/context/:id
POST /api/mcp/context
PUT /api/mcp/context/:id
DELETE /api/mcp/context/:id
```

#### Context Collection

```
POST /api/mcp/collect/program/:programId
POST /api/mcp/collect/tag/:tagId
POST /api/mcp/collect/rung/:rungId
POST /api/mcp/collect/safety/:programId
POST /api/mcp/collect/standard
```

#### Prompt Enhancement

```
POST /api/mcp/enhance
{
  "prompt": "Generate motor control logic",
  "contextTypes": ["program", "tag", "safety"],
  "maxTokens": 1000
}
```

#### Confidence Scoring

```
POST /api/mcp/confidence
{
  "contextItems": ["context-id-1", "context-id-2", "..."],
  "promptType": "generate"
}
```

### Database Schema

#### Context Items Table

```sql
CREATE TABLE mcp_context_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type VARCHAR(50) NOT NULL,
  source VARCHAR(50) NOT NULL,
  value TEXT NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id UUID REFERENCES users(id),
  project_id UUID REFERENCES projects(id),
  metadata JSONB DEFAULT '{}'::jsonb,
  expires_at TIMESTAMP WITH TIME ZONE,
  version INT DEFAULT 1,
  CONSTRAINT valid_type CHECK (type IN ('tag', 'program', 'rung', 'safety', 'doc', 'version', 'standard'))
);

CREATE INDEX idx_context_items_type ON mcp_context_items(type);
CREATE INDEX idx_context_items_project ON mcp_context_items(project_id);
CREATE INDEX idx_context_items_timestamp ON mcp_context_items(timestamp);
```

#### Context Relationships Table

```sql
CREATE TABLE mcp_context_relationships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_id UUID REFERENCES mcp_context_items(id) ON DELETE CASCADE,
  target_id UUID REFERENCES mcp_context_items(id) ON DELETE CASCADE,
  relationship_type VARCHAR(50) NOT NULL,
  strength FLOAT DEFAULT 1.0,
  metadata JSONB DEFAULT '{}'::jsonb,
  CONSTRAINT valid_relationship CHECK (relationship_type IN ('depends_on', 'related_to', 'conflicts_with', 'enhances'))
);

CREATE INDEX idx_context_relationships_source ON mcp_context_relationships(source_id);
CREATE INDEX idx_context_relationships_target ON mcp_context_relationships(target_id);
```

#### Context Usage Table

```sql
CREATE TABLE mcp_context_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  context_id UUID REFERENCES mcp_context_items(id) ON DELETE CASCADE,
  request_id UUID NOT NULL,
  prompt_type VARCHAR(50) NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  confidence_contribution FLOAT,
  user_id UUID REFERENCES users(id),
  metadata JSONB DEFAULT '{}'::jsonb
);

CREATE INDEX idx_context_usage_context ON mcp_context_usage(context_id);
CREATE INDEX idx_context_usage_request ON mcp_context_usage(request_id);
CREATE INDEX idx_context_usage_timestamp ON mcp_context_usage(timestamp);
```

### Context Item Types

| Type      | Description                                   | Example                                        |
|-----------|-----------------------------------------------|------------------------------------------------|
| `tag`     | PLC tag information                           | `Motor_Start (BOOL): Motor start button input` |
| `program` | Program metadata                              | `Program: Main_Program, Type: ladder`          |
| `rung`    | Ladder logic rung information                 | `Rung 5: Motor start/stop logic`               |
| `safety`  | Safety requirements and certifications        | `Safety Program: E_Stop, SIL Rating: SIL2`     |
| `doc`     | Documentation and comments                    | `This program controls the main conveyor`      |
| `version` | Software and hardware version information     | `PLC Firmware: v2.5.1`                         |
| `standard`| Compliance standards and regulations          | `IEC 61131-3 Standard for PLC Programming`     |

### Confidence Scoring Algorithm

The confidence score is calculated based on multiple factors:

1. **Context Completeness**: Percentage of required context types present
2. **Context Freshness**: Recency of context updates
3. **Context Relevance**: Relationship strength to the current task
4. **Context Quality**: Depth and detail of context items

The formula combines these factors with appropriate weights:

```
Confidence = (w1 * Completeness + w2 * Freshness + w3 * Relevance + w4 * Quality) / (w1 + w2 + w3 + w4)
```

Where:
- w1 = 0.4 (Completeness weight)
- w2 = 0.1 (Freshness weight)
- w3 = 0.3 (Relevance weight)
- w4 = 0.2 (Quality weight)

## Implementation Details

### Context Collection Service

The Context Collection Service is responsible for gathering relevant context from user activities and the current project state.

```typescript
// src/services/contextCollection.ts
import { db } from '../db';
import { ContextItem, ContextItemType } from '../types/mcp';

export class ContextCollectionService {
  async collectProgramContext(programId: string, userId: string): Promise<string> {
    // Get program details
    const program = await db('programs').where({ id: programId }).first();
    if (!program) throw new Error('Program not found');
    
    // Create context item
    const contextItem: Partial<ContextItem> = {
      type: 'program',
      source: 'auto',
      value: `Program: ${program.name}, Type: ${program.type}`,
      user_id: userId,
      project_id: program.project_id,
      metadata: {
        programId: program.id,
        programName: program.name,
        programType: program.type,
        safetyProgram: program.safety_program
      }
    };
    
    // Store in database
    const [result] = await db('mcp_context_items').insert(contextItem).returning('*');
    
    // If safety program, also collect safety context
    if (program.safety_program) {
      await this.collectSafetyContext(programId, userId);
    }
    
    return result.id;
  }
  
  async collectTagContext(tagId: string, userId: string): Promise<string> {
    // Implementation details...
  }
  
  async collectRungContext(rungId: string, userId: string): Promise<string> {
    // Implementation details...
  }
  
  async collectSafetyContext(programId: string, userId: string): Promise<string> {
    // Implementation details...
  }
  
  async collectStandardContext(standardName: string, userId: string): Promise<string> {
    // Implementation details...
  }
}
```

### Prompt Enhancement Service

The Prompt Enhancement Service integrates context into AI prompts to improve response quality.

```typescript
// src/services/promptEnhancement.ts
import { db } from '../db';
import { ContextItem } from '../types/mcp';

export class PromptEnhancementService {
  async enhancePrompt(prompt: string, contextTypes: string[], maxTokens: number = 1000): Promise<string> {
    // Get context items of specified types
    const contextItems = await db('mcp_context_items')
      .whereIn('type', contextTypes)
      .orderBy('timestamp', 'desc');
    
    // Serialize context
    const serializedContext = this.serializeContext(contextItems);
    
    // Optimize for token limit
    const optimizedContext = this.optimizeContext(serializedContext, maxTokens);
    
    // Combine with prompt
    return `${optimizedContext}\n\nUser Request: ${prompt}`;
  }
  
  private serializeContext(contextItems: ContextItem[]): string {
    // Group items by type
    const groupedItems: Record<string, ContextItem[]> = {};
    
    contextItems.forEach(item => {
      if (!groupedItems[item.type]) {
        groupedItems[item.type] = [];
      }
      groupedItems[item.type].push(item);
    });
    
    // Build the prompt with sections for each type
    let result = '';
    
    // Add standards and versions first as they're global context
    if (groupedItems['standard']) {
      result += '### Standards\n';
      groupedItems['standard'].forEach(item => {
        result += `- ${item.value}\n`;
      });
      result += '\n';
    }
    
    // Add remaining context types
    const remainingTypes = Object.keys(groupedItems).filter(type => type !== 'standard');
    
    for (const type of remainingTypes) {
      result += `### ${type.charAt(0).toUpperCase() + type.slice(1)}\n`;
      groupedItems[type].forEach(item => {
        result += `- ${item.value}\n`;
      });
      result += '\n';
    }
    
    return result;
  }
  
  private optimizeContext(context: string, maxTokens: number): string {
    // Simple token estimation (4 chars ≈ 1 token)
    const estimatedTokens = context.length / 4;
    
    if (estimatedTokens <= maxTokens) {
      return context;
    }
    
    // If over token limit, truncate context
    const reductionFactor = maxTokens / estimatedTokens;
    const lines = context.split('\n');
    const sectionsToKeep = Math.ceil(lines.length * reductionFactor);
    
    return lines.slice(0, sectionsToKeep).join('\n');
  }
}
```

### Confidence Scoring Service

The Confidence Scoring Service evaluates the quality and completeness of context to provide confidence metrics for AI responses.

```typescript
// src/services/confidenceScoring.ts
import { db } from '../db';

export class ConfidenceScoringService {
  async calculateConfidence(contextItemIds: string[], promptType: string): Promise<number> {
    // Get context items
    const contextItems = await db('mcp_context_items')
      .whereIn('id', contextItemIds);
    
    // Calculate completeness score
    const completenessScore = this.calculateCompleteness(contextItems, promptType);
    
    // Calculate freshness score
    const freshnessScore = this.calculateFreshness(contextItems);
    
    // Calculate relevance score
    const relevanceScore = this.calculateRelevance(contextItems, promptType);
    
    // Calculate quality score
    const qualityScore = this.calculateQuality(contextItems);
    
    // Combine scores with weights
    const weights = {
      completeness: 0.4,
      freshness: 0.1,
      relevance: 0.3,
      quality: 0.2
    };
    
    const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0);
    
    const confidence = (
      weights.completeness * completenessScore +
      weights.freshness * freshnessScore +
      weights.relevance * relevanceScore +
      weights.quality * qualityScore
    ) / totalWeight;
    
    // Record usage for analytics
    await this.recordContextUsage(contextItemIds, promptType, confidence);
    
    return confidence;
  }
  
  private calculateCompleteness(contextItems: any[], promptType: string): number {
    // Different prompt types require different context
    const requiredTypes: Record<string, string[]> = {
      'generate': ['program', 'tag', 'safety', 'standard'],
      'explain': ['program', 'rung', 'tag'],
      'debug': ['program', 'rung', 'tag', 'safety'],
      'refactor': ['program', 'tag', 'standard']
    };
    
    const required = requiredTypes[promptType] || ['program', 'tag'];
    const presentTypes = new Set(contextItems.map(item => item.type));
    
    let score = 0;
    for (const type of required) {
      if (presentTypes.has(type)) {
        score += 1 / required.length;
      }
    }
    
    return score;
  }
  
  private calculateFreshness(contextItems: any[]): number {
    // Calculate average age of context items
    const now = new Date();
    const ages = contextItems.map(item => {
      const timestamp = new Date(item.timestamp);
      return (now.getTime() - timestamp.getTime()) / (1000 * 60 * 60); // Age in hours
    });
    
    const averageAge = ages.reduce((sum, age) => sum + age, 0) / ages.length;
    
    // Convert to score (newer is better)
    // 0 hours = 1.0, 24 hours = 0.5, 168 hours (1 week) = 0.0
    return Math.max(0, 1 - (averageAge / 168));
  }
  
  private calculateRelevance(contextItems: any[], promptType: string): number {
    // Implementation details...
    return 0.8; // Placeholder
  }
  
  private calculateQuality(contextItems: any[]): number {
    // Implementation details...
    return 0.7; // Placeholder
  }
  
  private async recordContextUsage(contextItemIds: string[], promptType: string, confidence: number): Promise<void> {
    // Record usage for analytics
    const requestId = `req_${Date.now()}`;
    
    const usageRecords = contextItemIds.map(contextId => ({
      context_id: contextId,
      request_id: requestId,
      prompt_type: promptType,
      confidence_contribution: confidence / contextItemIds.length
    }));
    
    await db('mcp_context_usage').insert(usageRecords);
  }
}
```

### Context API Controller

The Context API Controller exposes RESTful endpoints for context operations.

```typescript
// src/controllers/contextController.ts
import { Request, Response } from 'express';
import { ContextCollectionService } from '../services/contextCollection';
import { PromptEnhancementService } from '../services/promptEnhancement';
import { ConfidenceScoringService } from '../services/confidenceScoring';
import { db } from '../db';

const contextCollection = new ContextCollectionService();
const promptEnhancement = new PromptEnhancementService();
const confidenceScoring = new ConfidenceScoringService();

export const getContextItems = async (req: Request, res: Response) => {
  try {
    const { type, projectId } = req.query;
    
    let query = db('mcp_context_items');
    
    if (type) {
      query = query.where({ type });
    }
    
    if (projectId) {
      query = query.where({ project_id: projectId });
    }
    
    const contextItems = await query.orderBy('timestamp', 'desc');
    
    res.json(contextItems);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch context items' });
  }
};

export const createContextItem = async (req: Request, res: Response) => {
  try {
    const { type, source, value, projectId, metadata } = req.body;
    
    // Validate input
    if (!type || !source || !value) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Create context item
    const [contextItem] = await db('mcp_context_items').insert({
      type,
      source,
      value,
      user_id: req.user.id,
      project_id: projectId,
      metadata
    }).returning('*');
    
    res.status(201).json(contextItem);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create context item' });
  }
};

export const enhancePrompt = async (req: Request, res: Response) => {
  try {
    const { prompt, contextTypes, maxTokens } = req.body;
    
    // Validate input
    if (!prompt) {
      return res.status(400).json({ error: 'Missing prompt' });
    }
    
    // Enhance prompt with context
    const enhancedPrompt = await promptEnhancement.enhancePrompt(
      prompt,
      contextTypes || ['program', 'tag', 'safety', 'standard'],
      maxTokens
    );
    
    res.json({ enhancedPrompt });
  } catch (error) {
    res.status(500).json({ error: 'Failed to enhance prompt' });
  }
};

export const calculateConfidence = async (req: Request, res: Response) => {
  try {
    const { contextItems, promptType } = req.body;
    
    // Validate input
    if (!contextItems || !Array.isArray(contextItems)) {
      return res.status(400).json({ error: 'Missing context items' });
    }
    
    // Calculate confidence
    const confidence = await confidenceScoring.calculateConfidence(
      contextItems,
      promptType || 'generate'
    );
    
    res.json({ confidence });
  } catch (error) {
    res.status(500).json({ error: 'Failed to calculate confidence' });
  }
};
```

## Integration with AI Backend

### Enhancing AI Requests

To integrate MCP with the existing AI backend, modify the request processing pipeline to include context enhancement:

```typescript
// src/services/aiService.ts
import { PromptEnhancementService } from './promptEnhancement';
import { ConfidenceScoringService } from './confidenceScoring';
import { llmClient } from './llmClient';

const promptEnhancement = new PromptEnhancementService();
const confidenceScoring = new ConfidenceScoringService();

export const processAIRequest = async (request: any) => {
  try {
    // Extract context items from request
    const contextItems = request.contextItems || [];
    
    // Calculate base confidence from context
    const contextConfidence = await confidenceScoring.calculateConfidence(
      contextItems,
      request.type
    );
    
    // Enhance prompt with context
    const enhancedPrompt = await promptEnhancement.enhancePrompt(
      request.prompt,
      request.contextTypes || ['program', 'tag', 'safety', 'standard']
    );
    
    // Send to LLM
    const response = await llmClient.invoke(request.model, enhancedPrompt);
    
    // Adjust confidence based on context quality
    const adjustedConfidence = (response.confidence + contextConfidence) / 2;
    
    return {
      ...response,
      confidence: adjustedConfidence,
      contextConfidence,
      enhancedPrompt
    };
  } catch (error) {
    throw new Error(`AI request failed: ${error.message}`);
  }
};
```

### Modifying Response Processing

Update the response processor to include context-related metadata:

```typescript
// src/services/responseProcessor.ts
import { ContextItem } from '../types/mcp';

export const processResponse = (response: any, contextItems: ContextItem[]) => {
  // Generate a deterministic "fingerprint" for this response based on context
  const contextTypes = contextItems.map(item => item.type).sort().join('-');
  const contextCount = contextItems.length;
  const timestamp = new Date().getTime();
  const fingerprint = `${contextTypes}-${contextCount}-${timestamp.toString(36)}`.substring(0, 8);
  
  // Add context metadata to response
  return {
    ...response,
    metadata: {
      ...response.metadata,
      contextFingerprint: fingerprint,
      contextTypes: [...new Set(contextItems.map(item => item.type))],
      contextCount: contextItems.length,
      contextConfidence: response.contextConfidence
    }
  };
};
```

## Security Considerations

1. **Access Control**
   - Implement proper authentication for all context endpoints
   - Use role-based permissions for context operations
   - Validate user access to project-specific context

2. **Data Validation**
   - Sanitize all user inputs
   - Validate context item types and values
   - Implement request size limits

3. **Privacy Protection**
   - Avoid storing sensitive information in context
   - Implement data retention policies
   - Provide mechanisms for context purging

4. **Audit Logging**
   - Log all context operations
   - Track context usage in AI requests
   - Implement tamper-proof audit trails

## Performance Optimization

1. **Caching Strategies**
   - Cache frequently used context items
   - Implement context item versioning
   - Use Redis for high-performance caching

2. **Database Optimization**
   - Create appropriate indexes
   - Implement query optimization
   - Use connection pooling

3. **Request Batching**
   - Batch context updates
   - Implement bulk operations
   - Use background processing for non-critical updates

## Monitoring and Analytics

1. **Usage Metrics**
   - Track context item creation and usage
   - Monitor context size and complexity
   - Analyze context contribution to confidence

2. **Performance Monitoring**
   - Measure response times for context operations
   - Track cache hit rates
   - Monitor database query performance

3. **Quality Metrics**
   - Analyze context completeness over time
   - Track confidence scores by context type
   - Correlate context quality with user feedback

## Testing Strategy

1. **Unit Testing**
   - Test individual context services
   - Validate confidence scoring algorithms
   - Verify context serialization

2. **Integration Testing**
   - Test context integration with AI services
   - Verify database operations
   - Test API endpoints

3. **Performance Testing**
   - Measure context processing under load
   - Test with large context collections
   - Verify caching effectiveness

## Deployment Guidelines

1. **Environment Setup**
   - Configure environment variables
   - Set up database connections
   - Configure caching services

2. **Database Migration**
   - Create migration scripts for MCP tables
   - Implement rollback procedures
   - Test migrations in staging environment

3. **Service Deployment**
   - Deploy as part of the AI backend
   - Configure proper scaling
   - Set up health monitoring

## Conclusion

The Model Context Protocol backend enhances the LUREON AI assistant by providing rich, structured context for AI interactions. By following this implementation guide, you'll create a robust system that improves AI response quality, confidence, and relevance for industrial automation tasks.

Remember that context quality directly impacts AI response quality, so prioritize comprehensive context collection and proper context management throughout the implementation process.